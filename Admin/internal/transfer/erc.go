package transfer

import (
	"Admin/config"
	token "Admin/contracts_erc20"
	"Admin/crypto/sha3"
	"Admin/dal"
	"Admin/internal/lib"
	"Admin/model/entity"
	"Admin/tron"
	"context"
	"crypto/ecdsa"
	"errors"
	"fmt"
	"github.com/ethereum/go-ethereum"
	"github.com/ethereum/go-ethereum/accounts/abi/bind"
	"github.com/ethereum/go-ethereum/common"
	"github.com/ethereum/go-ethereum/core/types"
	"github.com/ethereum/go-ethereum/crypto"
	"github.com/spf13/cast"
	"log"
	"math"
	"math/big"
	"os"
	"strconv"
	"time"

	"github.com/ethereum/go-ethereum/ethclient"
)

var (
	EthSUN          float64 = 1000000000000000000
	EthTokenSUN     float64 = 1000000
	BnBSUN          float64 = 1000000000000000000
	BnBTokenSUN     float64 = 1000000000000000000
	UsdtToken               = "TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t"
	UsdtTokenBNB            = "******************************************"
	UsdtTokenEth            = "******************************************"
	HexUsdtToken, _         = tron.DecodeBase58Address(UsdtToken)
	EthNet          *ethclient.Client
	EthMainNet      = "wss://mainnet.infura.io/ws/v3/********************************"
	SpareEthMainNet = "wss://rpc.ankr.com/eth/ws/310c35562452e3195d61d117da6872e7e606024cbdc6c814910f459755acd4bd"
	//EthMainNet = "wss://mainnet.infura.io/ws/v3/********************************"
	Now = 1
)

func InitEthNet() {
	EthSUN = config.NewConfig().Eth.Sun
	UsdtTokenEth = config.NewConfig().Eth.Token
	EthMainNet = config.NewConfig().Eth.Api
	EthTokenSUN = config.NewConfig().Eth.TokenSun
	var c *ethclient.Client
	var err error
	c, err = ethclient.Dial(EthMainNet)
	if err != nil {
		var cerr error
		c, cerr = ethclient.Dial(SpareEthMainNet)
		if cerr != nil {
			log.Println("Eth网络链接失败:", err.Error())
			os.Exit(-1)
		}
		lib.TgSend(config.NewConfig().Tg.Tgid, "元神: Eth网络切换成功,当前为备用网络", config.NewConfig().Tg.Token)
		Now = 2
	} else {
		Now = 1
	}
	EthNet = c
	log.Println("Eth网络初始化成功")
}

func InitBscNet() {
	BnBSUN = config.NewConfig().Bsc.Sun
	UsdtTokenBNB = config.NewConfig().Bsc.Token
	BnbMainNet = config.NewConfig().Bsc.Api
	BnBTokenSUN = config.NewConfig().Bsc.TokenSun
	c, err := ethclient.Dial(BnbMainNet)
	if err != nil {
		log.Println("Bnb网络链接失败:", err.Error())
		//os.Exit(-1)
	} else {
		BnbNet = c
		log.Println("Bnb网络初始化成功")
	}

}

func Gopool(run func(), i int64) {
	go func(n int64) {
		for {
			time.Sleep(time.Second * time.Duration(n))
			run()
		}
	}(i)
}

func Pool() {
	Gopool(CheckEthNet, 30)
}

func CheckEthNet() {
	if Now == 1 {
		return
	}
	EthSUN = config.NewConfig().Eth.Sun
	UsdtTokenEth = config.NewConfig().Eth.Token
	EthMainNet = config.NewConfig().Eth.Api
	EthTokenSUN = config.NewConfig().Eth.TokenSun
	c, err := ethclient.Dial(EthMainNet)
	if err != nil {
		return
	}
	EthNet.Close()
	EthNet = c
	Now = 1
	lib.TgSend(config.NewConfig().Tg.Tgid, "元神: Eth网络切换成功,当前为主网络", config.NewConfig().Tg.Token)
}

func Bep20Send(key, to string, amounts float64) (string, error) { //BEP20转账
	client := BnbNet
	privateKey, err := crypto.HexToECDSA(key)
	if err != nil {
		return "", err
	}

	publicKey := privateKey.Public()
	publicKeyECDSA, ok := publicKey.(*ecdsa.PublicKey)
	if !ok {
		return "", errors.New("cannot assert type: publicKey is not of type *ecdsa.PublicKey")
	}
	fromAddress := crypto.PubkeyToAddress(*publicKeyECDSA)
	nonce, err := client.PendingNonceAt(context.Background(), fromAddress)
	if err != nil {
		return "", err
	}
	value := big.NewInt(0) // in wei (0 eth)
	gasPrice, err := client.SuggestGasPrice(context.Background())
	if err != nil {
		return "", err
	}
	toAddress := common.HexToAddress(to)
	tokenAddress := common.HexToAddress(UsdtTokenBNB)
	transferFnSignature := []byte("transfer(address,uint256)")
	hash := sha3.NewKeccak256()
	hash.Write(transferFnSignature)
	methodID := hash.Sum(nil)[:4]
	paddedAddress := common.LeftPadBytes(toAddress.Bytes(), 32)
	amount := new(big.Int)
	at := cast.ToString(cast.ToFloat64(amounts * BnBTokenSUN))
	amount.SetString(at, 10) //
	paddedAmount := common.LeftPadBytes(amount.Bytes(), 32)
	var data []byte
	data = append(data, methodID...)
	data = append(data, paddedAddress...)
	data = append(data, paddedAmount...)

	gasLimit, err := client.EstimateGas(context.Background(), ethereum.CallMsg{
		To:   &toAddress,
		Data: data,
	})
	if err != nil {
		return "", err
	}
	gasLimit = gasLimit * 3
	if gasLimit > 60000 {
		gasLimit = 60000
	}
	tx := types.NewTransaction(nonce, tokenAddress, value, gasLimit, gasPrice, data)

	chainID, err := client.NetworkID(context.Background())
	if err != nil {
		return "", err
	}

	signedTx, err := types.SignTx(tx, types.NewEIP155Signer(chainID), privateKey)
	if err != nil {
		return "", err
	}

	err = client.SendTransaction(context.Background(), signedTx)
	if err != nil {
		return "", err
	}
	return signedTx.Hash().Hex(), nil
}

func Erc20Send(key, to string, amounts float64) (string, float64, error) { //BEP20转账
	client := EthNet
	privateKey, err := crypto.HexToECDSA(key)
	if err != nil {
		return "", 0, err
	}

	publicKey := privateKey.Public()
	publicKeyECDSA, ok := publicKey.(*ecdsa.PublicKey)
	if !ok {
		return "", 0, errors.New("cannot assert type: publicKey is not of type *ecdsa.PublicKey")
	}
	fromAddress := crypto.PubkeyToAddress(*publicKeyECDSA)
	nonce, err := client.PendingNonceAt(context.Background(), fromAddress)
	if err != nil {
		return "", 0, err
	}
	value := big.NewInt(0) // in wei (0 eth)
	gasPrice, err := client.SuggestGasPrice(context.Background())
	fmt.Println(gasPrice)
	jiaint := big.NewInt(1000000000)
	gasPrice = new(big.Int).Add(gasPrice, jiaint)
	fmt.Println(gasPrice)
	if err != nil {
		return "", 0, err
	}
	toAddress := common.HexToAddress(to)
	tokenAddress := common.HexToAddress(UsdtTokenEth)
	transferFnSignature := []byte("transfer(address,uint256)")
	hash := sha3.NewKeccak256()
	hash.Write(transferFnSignature)
	methodID := hash.Sum(nil)[:4]
	paddedAddress := common.LeftPadBytes(toAddress.Bytes(), 32)
	amount := new(big.Int)
	at := cast.ToString(cast.ToFloat64(amounts * EthTokenSUN))
	amount.SetString(at, 10) //
	paddedAmount := common.LeftPadBytes(amount.Bytes(), 32)
	var data []byte
	data = append(data, methodID...)
	data = append(data, paddedAddress...)
	data = append(data, paddedAmount...)
	gasLimit, err := client.EstimateGas(context.Background(), ethereum.CallMsg{
		From:     fromAddress,
		To:       &tokenAddress,
		Data:     data,
		Gas:      100000,
		GasPrice: gasPrice,
		Value:    value,
	})
	newgaslimit := big.NewInt(int64(gasLimit))
	transactionFee := new(big.Int).Mul(newgaslimit, gasPrice)
	bigFloat := new(big.Float).SetInt(transactionFee)
	divisor := new(big.Float).SetFloat64(EthSUN)
	transactionFeeInEth := new(big.Float).Quo(bigFloat, divisor)
	cfg, _ := dal.Cfg.Where(dal.Cfg.Func.Eq("ETH=USDT")).First()
	ethToUsd := lib.StringToFloat64(cfg.Value)
	//矿工费
	transactionFeeInEthFloat, _ := transactionFeeInEth.Float64()
	transactionFeeInUsd := transactionFeeInEthFloat * ethToUsd
	transactionFeeInUsd = math.Trunc(transactionFeeInUsd*100) / 100
	fmt.Println(transactionFeeInUsd)
	if err != nil {
		return "", 0, err
	}
	gasLimit = gasLimit * 10
	if gasLimit > 100000 {
		gasLimit = 100000
	}
	tx := types.NewTransaction(nonce, tokenAddress, value, gasLimit, gasPrice, data)
	chainID, err := client.NetworkID(context.Background())
	if err != nil {
		return "", 0, err
	}

	signedTx, err := types.SignTx(tx, types.NewEIP155Signer(chainID), privateKey)
	if err != nil {
		return "", 0, err
	}

	err = client.SendTransaction(context.Background(), signedTx)
	if err != nil {
		return "", 0, err
	}
	return signedTx.Hash().Hex(), transactionFeeInUsd, nil
}

func Erc20SendGetFee(key, to string, amounts float64) (float64, float64, error) { //BEP20转账
	client := EthNet
	privateKey, err := crypto.HexToECDSA(key)
	if err != nil {
		return 0, 0, err
	}

	publicKey := privateKey.Public()
	publicKeyECDSA, ok := publicKey.(*ecdsa.PublicKey)
	if !ok {
		return 0, 0, errors.New("cannot assert type: publicKey is not of type *ecdsa.PublicKey")
	}
	fromAddress := crypto.PubkeyToAddress(*publicKeyECDSA)
	_, err = client.PendingNonceAt(context.Background(), fromAddress)
	if err != nil {
		return 0, 0, err
	}
	value := big.NewInt(0) // in wei (0 eth)
	gasPrice, err := client.SuggestGasPrice(context.Background())
	if err != nil {
		return 0, 0, err
	}
	toAddress := common.HexToAddress(to)
	tokenAddress := common.HexToAddress(UsdtTokenEth)
	transferFnSignature := []byte("transfer(address,uint256)")
	hash := sha3.NewKeccak256()
	hash.Write(transferFnSignature)
	methodID := hash.Sum(nil)[:4]
	paddedAddress := common.LeftPadBytes(toAddress.Bytes(), 32)
	amount := new(big.Int)
	at := cast.ToString(cast.ToFloat64(amounts * EthTokenSUN))
	amount.SetString(at, 10) //
	paddedAmount := common.LeftPadBytes(amount.Bytes(), 32)
	var data []byte
	data = append(data, methodID...)
	data = append(data, paddedAddress...)
	data = append(data, paddedAmount...)
	gasLimit, err := client.EstimateGas(context.Background(), ethereum.CallMsg{
		From:     fromAddress,
		To:       &tokenAddress,
		Data:     data,
		Gas:      100000,
		GasPrice: gasPrice,
		Value:    value,
	})
	newgaslimit := big.NewInt(int64(gasLimit))
	transactionFee := new(big.Int).Mul(newgaslimit, gasPrice)
	bigFloat := new(big.Float).SetInt(transactionFee)
	divisor := new(big.Float).SetFloat64(EthSUN)
	transactionFeeInEth := new(big.Float).Quo(bigFloat, divisor)
	cfg, _ := dal.Cfg.Where(dal.Cfg.Func.Eq("ETH=USDT")).First()
	ethToUsd := lib.StringToFloat64(cfg.Value)
	//矿工费
	transactionFeeInEthFloat, _ := transactionFeeInEth.Float64()
	transactionFeeInUsd := transactionFeeInEthFloat * ethToUsd
	transactionFeeInUsd = math.Trunc(transactionFeeInUsd*100) / 100
	return transactionFeeInUsd, transactionFeeInEthFloat, nil
}

func BnbSend(key, to string, amount float64) (string, error) { //BNB转账
	client := BnbNet
	privateKey, err := crypto.HexToECDSA(key)
	if err != nil {
		return "", err
	}

	publicKey := privateKey.Public()
	publicKeyECDSA, ok := publicKey.(*ecdsa.PublicKey)
	if !ok {
		return "", errors.New("cannot assert type: publicKey is not of type *ecdsa.PublicKey")
	}

	fromAddress := crypto.PubkeyToAddress(*publicKeyECDSA)
	nonce, err := client.PendingNonceAt(context.Background(), fromAddress)
	if err != nil {
		return "", err
	}
	//amount
	at := amount * BnBSUN
	amounts := cast.ToInt64(at)
	value := big.NewInt(amounts) // in wei (1 eth) //100000000000000
	gasLimit := uint64(21000)    // in units
	gasPrice, err := client.SuggestGasPrice(context.Background())
	if err != nil {
		return "", err
	}

	toAddress := common.HexToAddress(to)
	var data []byte
	tx := types.NewTransaction(nonce, toAddress, value, gasLimit, gasPrice, data)

	chainID, err := client.NetworkID(context.Background())
	if err != nil {
		return "", err
	}

	signedTx, err := types.SignTx(tx, types.NewEIP155Signer(chainID), privateKey)
	if err != nil {
		return "", err
	}

	err = client.SendTransaction(context.Background(), signedTx)
	if err != nil {
		return "", err
	}
	return signedTx.Hash().Hex(), nil
}

func EthSend(key, to string, amount float64) (string, error) { //Eth转账
	client := EthNet
	privateKey, err := crypto.HexToECDSA(key)
	if err != nil {
		return "", err
	}
	publicKey := privateKey.Public()
	publicKeyECDSA, ok := publicKey.(*ecdsa.PublicKey)
	if !ok {
		return "", errors.New("cannot assert type: publicKey is not of type *ecdsa.PublicKey")
	}

	fromAddress := crypto.PubkeyToAddress(*publicKeyECDSA)
	nonce, err := client.PendingNonceAt(context.Background(), fromAddress)
	if err != nil {
		return "", err
	}
	//amount
	at := amount * EthSUN
	amounts := cast.ToInt64(at)
	value := big.NewInt(amounts) // in wei (1 eth) //100000000000000
	gasLimit := uint64(21000)    // in units
	gasPrice, err := client.SuggestGasPrice(context.Background())
	if err != nil {
		return "", err
	}

	toAddress := common.HexToAddress(to)
	var data []byte
	tx := types.NewTransaction(nonce, toAddress, value, gasLimit, gasPrice, data)

	chainID, err := client.NetworkID(context.Background())
	if err != nil {
		return "", err
	}

	signedTx, err := types.SignTx(tx, types.NewEIP155Signer(chainID), privateKey)
	if err != nil {
		return "", err
	}

	err = client.SendTransaction(context.Background(), signedTx)
	if err != nil {
		return "", err
	}
	return signedTx.Hash().Hex(), nil
}

func GetBNB(client *ethclient.Client, Adderss string) (string, error) { //获取币安账户余额
	account := common.HexToAddress(Adderss)
	balance, err := client.BalanceAt(context.Background(), account, nil)
	if err != nil {
		return "0", err
	}
	return fmt.Sprint(cast.ToFloat64(cast.ToString(balance)) / BnBSUN), nil
}

func GetETH(client *ethclient.Client, Adderss string) (string, error) { //获取eth账户余额
	account := common.HexToAddress(Adderss)
	balance, err := client.BalanceAt(context.Background(), account, nil)
	if err != nil {
		return "0", err
	}
	return fmt.Sprint(cast.ToFloat64(cast.ToString(balance)) / EthSUN), nil
}

func BNBGetUSDT(client *ethclient.Client, Address string) (string, error) { //币安币获取USDT
	tokenAddress := common.HexToAddress(UsdtTokenBNB)
	instance, err := token.NewToken(tokenAddress, client)
	if err != nil {
		return fmt.Sprint(0), err
	}

	address := common.HexToAddress(Address)
	bal, err := instance.BalanceOf(&bind.CallOpts{}, address)
	if err != nil {
		return fmt.Sprint(0), err
	}
	fbal := new(big.Int).Set(bal)
	return fmt.Sprint(cast.ToFloat64(cast.ToString(fbal)) / BnBTokenSUN), nil
}

func ETHGetUSDT(client *ethclient.Client, Address string) (string, error) { //ETH获取USDT
	tokenAddress := common.HexToAddress(UsdtTokenEth)
	instance, err := token.NewToken(tokenAddress, client)
	if err != nil {
		return fmt.Sprint(0), err
	}

	address := common.HexToAddress(Address)
	bal, err := instance.BalanceOf(&bind.CallOpts{}, address)
	if err != nil {
		return fmt.Sprint(0), err
	}
	fbal := new(big.Int).Set(bal)
	return fmt.Sprint(cast.ToFloat64(cast.ToString(fbal)) / EthTokenSUN), nil

}

// 根据Hash获取矿工费
func GetTranFeeByHash(client *ethclient.Client, hash string) (string, string) {
	txHash := common.HexToHash(hash)
	tx, _, err := client.TransactionByHash(context.Background(), txHash)
	if err != nil {
		log.Fatal(err)
	}
	receipt, err := client.TransactionReceipt(context.Background(), txHash)
	if err != nil {
		log.Fatal(err)
	}
	gasUsed := big.NewInt(int64(receipt.GasUsed))
	gasPrice := tx.GasPrice()
	transactionFee := new(big.Int).Mul(gasUsed, gasPrice)
	transactionFeeInEth := new(big.Float).Quo(new(big.Float).SetInt(transactionFee), big.NewFloat(math.Pow10(18)))
	cfg, _ := dal.Cfg.Where(dal.Cfg.Func.Eq("ETH=USDT")).First()
	ethToUsd := lib.StringToFloat64(cfg.Value)
	transactionFeeInEthFloat, _ := transactionFeeInEth.Float64()
	transactionFeeInUsd := transactionFeeInEthFloat * ethToUsd
	return transactionFeeInEth.String(), strconv.FormatFloat(transactionFeeInUsd, 'f', 2, 64)
}

// 根据Hash获取交易状态
func GetStatusByHash(client *ethclient.Client, hash string) string {
	txHash := common.HexToHash(hash)
	_, isPending, err := client.TransactionByHash(context.Background(), txHash)
	if err != nil {
		log.Fatal(err)
	}

	if isPending {
		return "pending"
	} else {
		receipt, err := client.TransactionReceipt(context.Background(), txHash)
		if err != nil {
			log.Fatal(err)
		}

		if receipt.Status == uint64(1) {
			return "success"
		} else {
			return "fail"
		}
	}
}

// 查询以太坊链交易状态
func QueryErcTransferStatus() {
	order, _ := dal.Payorder.Where(dal.Payorder.Status.Eq(2)).Where(dal.Payorder.Chain.Eq("Erc20")).Find()
	for _, o := range order {
		status := GetStatusByHash(EthNet, *o.Transactionid)
		switch status {
		case "success":
			_, usdtfee := GetTranFeeByHash(EthNet, *o.Transactionid)
			float64fee := lib.StringToFloat64(usdtfee)
			//更新订单状态
			dal.Payorder.Where(dal.Payorder.ID.Eq(o.ID)).Updates(&entity.Payorder{
				RealTranFee: &float64fee,
			})
			//case "fail":
			//	//更新订单状态
			//	dal.Payorder.Where(dal.Payorder.ID.Eq(o.ID)).Updates(&entity.Payorder{
			//		Status: lib.Int64ToInt32Ptr(5),
			//	})
		}
	}
}
