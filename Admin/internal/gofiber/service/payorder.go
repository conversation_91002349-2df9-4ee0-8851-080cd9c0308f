package service

import (
	"Admin/dal"
	"Admin/internal/lib"
	"Admin/model/entity"
	"github.com/gofiber/fiber/v2"
	"github.com/spf13/cast"
	"gorm.io/gen"
	"time"
)

// 获取下发订单列表
func GetPayOrderList(c *fiber.Ctx) error {
	page := c.FormValue("page")
	intpage := lib.StrToint64(page)
	limit := c.FormValue("limit")
	intlimit := lib.StrToint64(limit)
	var offset int64 = 0
	if intpage == 1 {
		offset = 0
	} else {
		offset = (intpage - 1) * intlimit
	}
	var where []gen.Condition
	orderid := c.FormValue("orderid")
	rorderid := c.FormValue("rorderid")
	status := c.FormValue("status")
	addr := c.FormValue("addr")
	chain := c.FormValue("chain")
	if orderid != "" {
		where = append(where, dal.Payorder.Orderid.Eq(orderid))
	}
	if rorderid != "" {
		where = append(where, dal.Payorder.Rorderid.Eq(rorderid))
	}
	if addr != "" {
		where = append(where, dal.Payorder.Addr.Eq(addr))
	}
	if chain != "" {
		where = append(where, dal.Payorder.Chain.Eq(chain))
	}
	if status != "" {
		if status == "2" {
			where = append(where, dal.Payorder.Status.In(2, 3))
		} else {
			where = append(where, dal.Payorder.Status.Eq(cast.ToInt32(status)))
		}
	}
	start_time := c.FormValue("start_time")
	end_time := c.FormValue("end_time")
	if start_time != "" {
		tres, _ := time.ParseInLocation("2006-01-02 15:04:05", start_time, time.Local)
		where = append(where, dal.Payorder.Ctime.Gt(cast.ToInt32(tres.Unix())))
	}
	if end_time != "" {
		tres, _ := time.ParseInLocation("2006-01-02 15:04:05", end_time, time.Local)
		where = append(where, dal.Payorder.Ctime.Lt(cast.ToInt32(tres.Unix())))
	}
	list, _ := dal.Payorder.Where(where...).Order(dal.Payorder.ID.Desc()).Limit(cast.ToInt(limit)).Offset(cast.ToInt(offset)).Find()
	count, _ := dal.Payorder.Where(where...).Count()
	return c.Status(fiber.StatusOK).JSON(fiber.Map{
		"code":  0,
		"count": count,
		"data":  list,
	})
}

// 获取下发订单详情
func GetPayOrderInfo(c *fiber.Ctx) error {
	id := c.FormValue("id")
	order, _ := dal.Payorder.Where(dal.Payorder.ID.Eq(cast.ToInt64(id))).First()
	return c.Status(fiber.StatusOK).JSON(fiber.Map{
		"code": true,
		"msg":  "成功",
		"data": order,
	})
}

// 修改下发订单状态
func UpdatePayOrder(c *fiber.Ctx) error {
	id := c.FormValue("id")
	status := c.FormValue("status")
	dal.Payorder.Where(dal.Payorder.ID.Eq(cast.ToInt64(id))).Update(dal.Payorder.Status, status)
	return c.Status(fiber.StatusOK).JSON(fiber.Map{
		"code": true,
		"msg":  "成功",
		"data": nil,
	})
}

// 补发下发订单回调
func DoCallbackPayOrder(c *fiber.Ctx) error {
	id := c.FormValue("id")
	dal.Payorder.Where(dal.Payorder.ID.Eq(cast.ToInt64(id))).Updates(&entity.Payorder{
		Status:      lib.Int64ToInt32Ptr(2),
		Callbacknum: lib.Int64ToInt32Ptr(0),
	})
	return c.Status(fiber.StatusOK).JSON(fiber.Map{
		"code": true,
		"msg":  "成功",
		"data": nil,
	})
}
