package service

import (
	"Admin/dal"
	"Admin/internal/lib"
	"github.com/gofiber/fiber/v2"
	"github.com/spf13/cast"
	"gorm.io/gen"
	"time"
)

// 获取商户提现列表
func GetSettlementList(c *fiber.Ctx) error {
	page := c.FormValue("page")
	intpage := lib.StrToint64(page)
	limit := c.FormValue("limit")
	intlimit := lib.StrToint64(limit)
	var offset int64 = 0
	if intpage == 1 {
		offset = 0
	} else {
		offset = (intpage - 1) * intlimit
	}
	var where []gen.Condition
	username := c.FormValue("mchname")
	if username != "" {
		where = append(where, dal.Settlement.Username.Eq(username))
	}
	status := c.FormValue("status")
	if status != "" {
		where = append(where, dal.Settlement.Status.Eq(cast.ToInt32(status)))
	}
	start_time := c.FormValue("start_time")
	end_time := c.FormValue("end_time")
	if start_time != "" {
		tres, _ := time.ParseInLocation("2006-01-02 15:04:05", start_time, time.Local)
		where = append(where, dal.Settlement.Ctime.Gt(cast.ToInt32(tres.Unix())))
	}
	if end_time != "" {
		tres, _ := time.ParseInLocation("2006-01-02 15:04:05", end_time, time.Local)
		where = append(where, dal.Settlement.Ctime.Lt(cast.ToInt32(tres.Unix())))
	}
	list, _ := dal.Settlement.Where(where...).Order(dal.Settlement.ID.Desc()).Limit(cast.ToInt(limit)).Offset(cast.ToInt(offset)).Find()
	count, _ := dal.Settlement.Where(where...).Count()
	return c.Status(fiber.StatusOK).JSON(fiber.Map{
		"code":  0,
		"count": count,
		"data":  list,
	})
}

// 获取商户提现详情
func GetSettlementInfo(c *fiber.Ctx) error {
	id := c.FormValue("id")
	settlement, _ := dal.Settlement.Where(dal.Settlement.ID.Eq(cast.ToInt64(id))).First()
	return c.Status(fiber.StatusOK).JSON(fiber.Map{
		"code": true,
		"msg":  "成功",
		"data": settlement,
	})
}

// 修改商户提现状态
func UpdateSettlement(c *fiber.Ctx) error {
	id := c.FormValue("id")
	status := c.FormValue("status")
	dal.Settlement.Where(dal.Settlement.ID.Eq(cast.ToInt64(id))).Update(dal.Settlement.Status, status)
	return c.Status(fiber.StatusOK).JSON(fiber.Map{
		"code": true,
		"msg":  "成功",
		"data": nil,
	})
}
