package service

import (
	"Admin/dal"
	"Admin/internal/lib"
	"github.com/gofiber/fiber/v2"
	"github.com/spf13/cast"
	"gorm.io/gen"
	"time"
)

// 获取商户资金明细列表
func GetTranList(c *fiber.Ctx) error {
	page := c.FormValue("page")
	intpage := lib.StrToint64(page)
	limit := c.FormValue("limit")
	intlimit := lib.StrToint64(limit)
	var offset int64 = 0
	if intpage == 1 {
		offset = 0
	} else {
		offset = (intpage - 1) * intlimit
	}
	var where []gen.Condition
	username := c.FormValue("username")
	if username != "" {
		where = append(where, dal.Tran.Username.Eq(username))
	}
	types := c.FormValue("type")
	if types != "" {
		where = append(where, dal.Tran.Type.Eq(cast.ToInt32(types)))
	}
	start_time := c.FormValue("start_time")
	end_time := c.FormValue("end_time")
	if start_time != "" {
		tres, _ := time.ParseInLocation("2006-01-02 15:04:05", start_time, time.Local)
		where = append(where, dal.Tran.Ctime.Gt(cast.ToInt32(tres.Unix())))
	}
	if end_time != "" {
		tres, _ := time.ParseInLocation("2006-01-02 15:04:05", end_time, time.Local)
		where = append(where, dal.Tran.Ctime.Lt(cast.ToInt32(tres.Unix())))
	}
	list, _ := dal.Tran.Where(where...).Order(dal.Tran.ID.Desc()).Limit(cast.ToInt(limit)).Offset(cast.ToInt(offset)).Find()
	count, _ := dal.Tran.Where(where...).Count()
	return c.Status(fiber.StatusOK).JSON(fiber.Map{
		"code":  0,
		"count": count,
		"data":  list,
	})
}
