package service

import (
	"Admin/dal"
	"Admin/internal/lib"
	"Admin/model/entity"
	"github.com/gofiber/fiber/v2"
	"github.com/spf13/cast"
	"gorm.io/gen"
	"time"
)

// 获取白名单列表
func GetWhiteList(c *fiber.Ctx) error {
	page := c.FormValue("page")
	intpage := lib.StrToint64(page)
	limit := c.FormValue("limit")
	intlimit := lib.StrToint64(limit)
	var offset int64 = 0
	if intpage == 1 {
		offset = 0
	} else {
		offset = (intpage - 1) * intlimit
	}
	var where []gen.Condition
	where = append(where, dal.Whitelist.DeletedAt.IsNull())
	mchname := c.FormValue("mchname")
	if mchname != "" {
		where = append(where, dal.Whitelist.Username.Eq(mchname))
	}
	ty := c.FormValue("type")
	if ty != "" {
		where = append(where, dal.Whitelist.Type.Eq(cast.ToInt32(ty)))
	}
	list, _ := dal.Whitelist.Where(where...).Order(dal.Whitelist.ID.Desc()).Limit(cast.ToInt(limit)).Offset(cast.ToInt(offset)).Find()
	count, _ := dal.Whitelist.Where(where...).Count()
	return c.Status(fiber.StatusOK).JSON(fiber.Map{
		"code":  0,
		"count": count,
		"data":  list,
	})
}

// 添加白名单
func InsertWhite(c *fiber.Ctx) error {
	username := c.FormValue("username")
	content := c.FormValue("content")
	ty := c.FormValue("type")
	mch, merr := dal.Merchant.Where(dal.Merchant.Username.Eq(username)).First()
	if mch == nil || merr != nil {
		return c.Status(fiber.StatusOK).JSON(fiber.Map{
			"code": false,
			"msg":  "失败",
			"data": nil,
		})
	}
	now := time.Now()
	dal.Whitelist.Create(&entity.Whitelist{
		CreatedAt: &now,
		UpdatedAt: &now,
		Type:      lib.Int64ToInt32Ptr(cast.ToInt64(ty)),
		UserID:    lib.Int64ToInt32Ptr(mch.ID),
		Content:   &content,
		Username:  &username,
	})
	aa := lib.UpdateIpWhiteList(cast.ToInt64(ty))
	if aa == false {
		return c.Status(fiber.StatusOK).JSON(fiber.Map{
			"code": true,
			"msg":  "添加白名单成功,同步CF失败",
			"data": nil,
		})
	}
	return c.Status(fiber.StatusOK).JSON(fiber.Map{
		"code": true,
		"msg":  "成功",
		"data": nil,
	})

}

// 删除白名单
func DeleteWhite(c *fiber.Ctx) error {
	id := c.FormValue("id")
	white, _ := dal.Whitelist.Where(dal.Whitelist.ID.Eq(cast.ToInt64(id))).First()
	ty := white.Type
	dal.Whitelist.Where(dal.Whitelist.ID.Eq(cast.ToInt64(id))).Update(dal.Whitelist.DeletedAt, time.Now())
	aa := lib.UpdateIpWhiteList(cast.ToInt64(ty))
	if aa == false {
		return c.Status(fiber.StatusOK).JSON(fiber.Map{
			"code": true,
			"msg":  "删除白名单成功,同步CF失败",
			"data": nil,
		})
	}
	return c.Status(fiber.StatusOK).JSON(fiber.Map{
		"code": true,
		"msg":  "成功",
		"data": nil,
	})
}
