package service

import (
	"Admin/dal"
	"Admin/internal/gofiber/middleware"
	"Admin/internal/lib"
	"Admin/model/entity"
	"encoding/json"
	"github.com/gofiber/fiber/v2"
	"github.com/spf13/cast"
	"golang.org/x/crypto/bcrypt"
	"time"
)

// 登录
func DoLogin(c *fiber.Ctx) fiber.Map {
	username := c.FormValue("username")
	password := c.FormValue("password")
	admin, err := dal.Admin.Where(dal.Admin.Username.Eq(username)).Where(dal.Admin.DelFlg.Eq(0)).First()
	if admin == nil || err != nil {
		return fiber.Map{"code": false, "msg": "用户名错误", "data": nil}
	}

	err = bcrypt.CompareHashAndPassword([]byte(*admin.Password), []byte(password))
	if err != nil {
		return fiber.Map{"code": false, "msg": "密码错误", "data": nil}
	}
	data := make(map[string]string)
	t, terr := middleware.CreateToken(username, cast.ToString(admin.ID))
	if terr != nil {
		return fiber.Map{"code": false, "msg": "Token False", "data": nil}
	}
	data["token"] = t
	data["username"] = username
	// 创建日志
	path := c.Path()
	method := c.Method()
	ip := c.Locals("ip").(string)
	jsond := map[string]string{
		"username": username,
		"password": "******",
	}
	jsonData, err := json.Marshal(jsond)
	if err != nil {
		return fiber.Map{"code": false, "msg": "存入日志失败", "data": nil}
	}
	content := string(jsonData)
	action := "登录"
	dal.Adminslog.Create(&entity.Adminslog{
		Adminid:  lib.Int64ToInt32Ptr(cast.ToInt64(admin.ID)),
		Username: &username,
		Action:   &action,
		Route:    &path,
		Method:   &method,
		Content:  &content,
		IP:       &ip,
		Ctime:    lib.Int64ToInt32Ptr(time.Now().Unix()),
		DelFlg:   lib.Int64ToInt32Ptr(0),
	})
	return fiber.Map{"code": true, "msg": "OK", "data": data}
}

// 退出登录
func DoLogout(c *fiber.Ctx) fiber.Map {
	userid, ok := c.Locals("userid").(string)
	username, uok := c.Locals("username").(string)
	if !ok || !uok {
		return fiber.Map{"code": false, "msg": "未登录", "data": nil}
	}
	// 创建日志
	path := c.Path()
	method := c.Method()
	ip := c.Locals("ip").(string)
	content := ""
	action := "退出登录"
	dal.Adminslog.Create(&entity.Adminslog{
		Adminid:  lib.Int64ToInt32Ptr(cast.ToInt64(userid)),
		Username: &username,
		Action:   &action,
		Route:    &path,
		Method:   &method,
		Content:  &content,
		IP:       &ip,
		Ctime:    lib.Int64ToInt32Ptr(time.Now().Unix()),
		DelFlg:   lib.Int64ToInt32Ptr(0),
	})
	c.Locals("username", nil)
	c.Locals("userid", nil)
	return fiber.Map{"code": true, "msg": "OK", "data": nil}
}
