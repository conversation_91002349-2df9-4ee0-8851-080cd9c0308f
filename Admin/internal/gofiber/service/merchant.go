package service

import (
	"Admin/dal"
	"Admin/internal/lib"
	"Admin/model/entity"
	"github.com/gofiber/fiber/v2"
	"github.com/shopspring/decimal"
	"github.com/spf13/cast"
	"gorm.io/gen"
	"gorm.io/gorm"
	"time"
)

type MerchantList struct {
	ID                 int64          `gorm:"column:id;type:bigint(20) unsigned;primaryKey;autoIncrement:true" json:"id"`
	CreatedAt          *time.Time     `gorm:"column:created_at;type:datetime(3)" json:"created_at"`
	UpdatedAt          *time.Time     `gorm:"column:updated_at;type:datetime(3)" json:"updated_at"`
	DeletedAt          gorm.DeletedAt `gorm:"column:deleted_at;type:datetime(3);index:idx_sys_merchant_deleted_at,priority:1" json:"deleted_at"`
	Username           *string        `gorm:"column:username;type:varchar(50);comment:用户名" json:"username"`
	Password           *string        `gorm:"column:password;type:varchar(255);comment:密码" json:"password"`
	Status             *int32         `gorm:"column:status;type:int(11);comment:状态 启用0 禁用1" json:"status"`
	Fee                *float64       `gorm:"column:fee;type:decimal(11,2);comment:费率" json:"fee"`
	Ethfee             *float64       `gorm:"column:ethfee;type:decimal(11,2);comment:eth费率" json:"ethfee"`
	Bscfee             *float64       `gorm:"column:bscfee;type:decimal(11,2);comment:bsc费率" json:"bscfee"`
	Money              *float64       `gorm:"column:money;type:decimal(11,2);comment:TRC余额" json:"money"`
	LockMoney          *float64       `gorm:"column:lock_money;type:decimal(11,2);comment:TRC锁定余额" json:"lock_money"`
	FeeMoney           *float64       `gorm:"column:fee_money;type:decimal(11,2);comment:TRC手续费余额(TRX)" json:"fee_money"`
	EthFeemoney        *float64       `gorm:"column:eth_feemoney;type:decimal(11,6);comment:ERC手续费余额(ETH)" json:"eth_feemoney"`
	ErcLockmoney       *float64       `gorm:"column:erc_lockmoney;type:decimal(11,2);comment:ERC锁定余额" json:"erc_lockmoney"`
	ErcMoney           *float64       `gorm:"column:erc_money;type:decimal(11,2);comment:ERC余额" json:"erc_money"`
	BscMoney           *float64       `gorm:"column:bsc_money;type:decimal(11,5);comment:BSC余额" json:"bsc_money"`
	BscLockmoney       *float64       `gorm:"column:bsc_lockmoney;type:decimal(11,5);comment:BSC锁定余额" json:"bsc_lockmoney"`
	BscFeemoney        *float64       `gorm:"column:bsc_feemoney;type:decimal(11,5);comment:BSC手续费余额(BNB)" json:"bsc_feemoney"`
	Secret             *string        `gorm:"column:secret;type:varchar(255);comment:Google密钥" json:"secret"`
	AgentID            *int32         `gorm:"column:agent_id;type:int(11);comment:所属代理id" json:"agent_id"`
	AgentName          *string        `gorm:"column:agent_name;type:varchar(255);comment:所属代理名" json:"agent_name"`
	EthAgentFee        *float64       `gorm:"column:eth_agent_fee;type:decimal(11,2);comment:eth代理费率" json:"eth_agent_fee"`
	Appkey             *string        `gorm:"column:appkey;type:varchar(255);comment:密钥" json:"appkey"`
	Token              *string        `gorm:"column:token;type:varchar(255);comment:token" json:"token"`
	Credits            *float64       `gorm:"column:credits;type:decimal(11,2);comment:信用额度(可透支手续费)" json:"credits"`
	Rate               *float64       `gorm:"column:rate;type:decimal(11,2);comment:固定汇率" json:"rate"`
	Ratetype           *string        `gorm:"column:ratetype;type:varchar(255);comment:汇率类型 1固定 2浮动" json:"ratetype"`
	Floatratetype      *string        `gorm:"column:floatratetype;type:varchar(255);comment:浮动汇率差值类型 1百分比 2固定数值" json:"floatratetype"`
	Floatratedvalue    *string        `gorm:"column:floatratedvalue;type:varchar(255);comment:浮动汇率差值" json:"floatratedvalue"`
	AgentFee           *float64       `gorm:"column:agent_fee;type:decimal(11,2);comment:代理费率" json:"agent_fee"`
	IsFloatdown        *string        `gorm:"column:is_floatdown;type:varchar(11);comment:是否开启下浮" json:"is_floatdown"`
	Largeordertimeout  *string        `gorm:"column:largeordertimeout;type:varchar(255);comment:大额订单超时时间" json:"largeordertimeout"`
	Smallordertimeout  *string        `gorm:"column:smallordertimeout;type:varchar(255);comment:小额订单超时时间" json:"smallordertimeout"`
	Largeorderlocktime *string        `gorm:"column:largeorderlocktime;type:varchar(255);comment:大额订单trx锁定时间" json:"largeorderlocktime"`
	Smallorderlocktime *string        `gorm:"column:smallorderlocktime;type:varchar(255);comment:小额订单trx锁定时间" json:"smallorderlocktime"`
	Largeorderamount   *string        `gorm:"column:largeorderamount;type:varchar(255);comment:大额订单金额(>)" json:"largeorderamount"`
	AddressID          *int32         `gorm:"column:address_id;type:int(11);comment:地址ID" json:"address_id"`
	TrcStatus          *int32         `gorm:"column:trc_status;type:int(11);comment:TRC归集状态 0未执行归集 1等待归集 2归集执行中" json:"trc_status"`
	ErcStatus          *int32         `gorm:"column:erc_status;type:int(11);comment:ERC归集状态 0未执行归集 1等待归集 2归集执行中" json:"erc_status"`
	BscStatus          *int32         `gorm:"column:bsc_status;type:int(11);comment:BSC归集状态 0未执行归集 1等待归集 2归集执行中" json:"bsc_status"`
	GatherType         *int32         `gorm:"column:gather_type;type:int(11);comment:归集类型 1自动归集 2下发时归集" json:"gather_type"`
	AddressUsdt        *float64       `json:"address_usdt"`
	Trcaddress         *string        `json:"trcaddress"`
	Ercaddress         *string        `json:"ercaddress"`
	CommissionPrice    *float64       `gorm:"column:commission_price;type:decimal(11,2);comment:商户手续费" json:"commission_price"`
}

// 获取商户列表
func GetMerchantList(c *fiber.Ctx) error {
	page := c.FormValue("page")
	intpage := lib.StrToint64(page)
	limit := c.FormValue("limit")
	intlimit := lib.StrToint64(limit)
	var offset int64 = 0
	if intpage == 1 {
		offset = 0
	} else {
		offset = (intpage - 1) * intlimit
	}
	var where []gen.Condition
	username := c.FormValue("username")
	status := c.FormValue("status")
	sql := "SELECT merchant.*, COALESCE(SUM(address.usdt_trc), 0) + COALESCE(SUM(address.usdt_erc), 0) AS address_usdt FROM merchant LEFT JOIN address ON merchant.id = address.merchant_id WHERE merchant.id > 0"
	if username != "" {
		sql = sql + " AND merchant.username = '" + username + "'"
		where = append(where, dal.Merchant.Username.Eq(username))
	}
	if status != "" {
		sql = sql + " AND merchant.status = " + status
		where = append(where, dal.Merchant.Status.Eq(cast.ToInt32(status)))
	}
	sql = sql + " GROUP BY merchant.id order by merchant.id desc limit " + limit + " offset " + cast.ToString(offset)
	//list, _ := dal.Merchant.Select(dal.Merchant.ALL, dal.Address.UsdtTrc.Sum().As("usdt_trc")).Where(where...).LeftJoin(dal.Address, dal.Merchant.ID.EqCol(dal.Address.MerchantID)).Group(dal.Merchant.ID).Order(dal.Merchant.ID.Desc()).Limit(cast.ToInt(limit)).Offset(cast.ToInt(offset)).Find()
	var results []MerchantList
	dal.DB.Raw(sql).Scan(&results)
	count, _ := dal.Merchant.Where(where...).Count()
	for i := range results {
		if *results[i].AddressID != 0 && results[i].AddressID != nil {
			addr, _ := dal.Address.Where(dal.Address.ID.Eq(cast.ToInt64(results[i].AddressID))).First()
			results[i].Trcaddress = addr.Trxaddr
			results[i].Ercaddress = addr.Ethaddr
		}
	}
	return c.Status(fiber.StatusOK).JSON(fiber.Map{
		"code":  0,
		"count": count,
		"data":  results,
	})
}

// 获取商户详情
func GetMerchantInfo(c *fiber.Ctx) error {
	id := c.FormValue("id")
	mch, _ := dal.Merchant.Where(dal.Merchant.ID.Eq(cast.ToInt64(id))).First()
	return c.Status(fiber.StatusOK).JSON(fiber.Map{
		"code": true,
		"msg":  "成功",
		"data": mch,
	})
}

// 添加商户
func InsertMerchant(c *fiber.Ctx) error {
	username := c.FormValue("username")
	password := c.FormValue("password")
	credits := cast.ToFloat64(c.FormValue("credits"))
	rate := 7.00
	ratetype := "2"
	floatratetype := "2"
	floatratedvalue := "0"
	is_floatdown := c.FormValue("is_floatdown")
	gather := cast.ToInt32(c.FormValue("gather"))
	largeordertimeout := "30"
	smallordertimeout := "30"
	largeorderlocktime := "360"
	smallorderlocktime := "30"
	largeorderamount := "50"
	pyd := lib.Md5(password)
	fee := cast.ToFloat64(c.FormValue("fee"))
	commissionPrice := cast.ToFloat64(c.FormValue("commission_price"))
	ethfee := cast.ToFloat64(c.FormValue("ethfee"))
	bscfee := cast.ToFloat64(c.FormValue("bscfee"))
	ling := 0.00
	appkey := lib.Md5(lib.RandAllString(32))
	dal.Merchant.Create(&entity.Merchant{
		Username:           &username,
		Password:           &pyd,
		Status:             lib.Int64ToInt32Ptr(0),
		Fee:                &fee,
		Ethfee:             &ethfee,
		Bscfee:             &bscfee,
		Money:              &ling,
		LockMoney:          &ling,
		FeeMoney:           &ling,
		ErcMoney:           &ling,
		ErcLockmoney:       &ling,
		EthFeemoney:        &ling,
		BscMoney:           &ling,
		BscLockmoney:       &ling,
		BscFeemoney:        &ling,
		AgentID:            lib.Int64ToInt32Ptr(0),
		AgentName:          &ratetype,
		Appkey:             &appkey,
		Credits:            &credits,
		Rate:               &rate,
		Ratetype:           &ratetype,
		Floatratetype:      &floatratetype,
		Floatratedvalue:    &floatratedvalue,
		AgentFee:           &ling,
		IsFloatdown:        &is_floatdown,
		Largeordertimeout:  &largeordertimeout,
		Smallordertimeout:  &smallordertimeout,
		Largeorderlocktime: &largeorderlocktime,
		Smallorderlocktime: &smallorderlocktime,
		Largeorderamount:   &largeorderamount,
		TrcStatus:          lib.Int64ToInt32Ptr(0),
		ErcStatus:          lib.Int64ToInt32Ptr(0),
		BscStatus:          lib.Int64ToInt32Ptr(0),
		GatherType:         &gather,
		AddressID:          lib.Int64ToInt32Ptr(0),
		IsTieredRate:       lib.Int64ToInt32Ptr(0),
		CommissionPrice:    &commissionPrice,
	})
	return c.Status(fiber.StatusOK).JSON(fiber.Map{
		"code": true,
		"msg":  "成功",
		"data": nil,
	})
}

// 修改商户
func UpdateMerchant(c *fiber.Ctx) error {
	id := c.FormValue("id")
	status := c.FormValue("status")
	if status != "" {
		dal.Merchant.Where(dal.Merchant.ID.Eq(cast.ToInt64(id))).Update(dal.Merchant.Status, status)
		return c.Status(fiber.StatusOK).JSON(fiber.Map{
			"code": true,
			"msg":  "成功",
			"data": nil,
		})
	}
	money := c.FormValue("money")
	if money != "" {
		m64 := cast.ToFloat64(money)
		if m64 < 0.01 {
			return c.Status(fiber.StatusOK).JSON(fiber.Map{
				"code": true,
				"msg":  "最小充值金额0.01",
				"data": nil,
			})
		}
		mch, _ := dal.Merchant.Where(dal.Merchant.ID.Eq(cast.ToInt64(id))).First()
		newmoney, _ := decimal.NewFromFloat(*mch.Money).Add(decimal.NewFromFloat(m64)).Float64()
		dal.Merchant.Where(dal.Merchant.ID.Eq(cast.ToInt64(id))).Update(dal.Merchant.Money, newmoney)
		str := "后台充值余额(TRC)"
		dal.Tran.Create(&entity.Tran{
			UserID:        lib.Int64ToInt32Ptr(mch.ID),
			UserType:      lib.Int64ToInt32Ptr(int64(1)),
			Type:          lib.Int64ToInt32Ptr(int64(9)),
			Money:         &m64,
			Orderid:       &str,
			Rorderid:      &str,
			Transactionid: &str,
			Username:      mch.Username,
			Ctime:         lib.Int64ToInt32Ptr(time.Now().Unix()),
		})
		return c.Status(fiber.StatusOK).JSON(fiber.Map{
			"code": true,
			"msg":  "成功",
			"data": nil,
		})
	}
	ercmoney := c.FormValue("ercmoney")
	if ercmoney != "" {
		m64 := cast.ToFloat64(ercmoney)
		if m64 < 0.01 {
			return c.Status(fiber.StatusOK).JSON(fiber.Map{
				"code": true,
				"msg":  "最小充值金额0.01",
				"data": nil,
			})
		}
		mch, _ := dal.Merchant.Where(dal.Merchant.ID.Eq(cast.ToInt64(id))).First()
		newmoney, _ := decimal.NewFromFloat(*mch.ErcMoney).Add(decimal.NewFromFloat(m64)).Float64()
		dal.Merchant.Where(dal.Merchant.ID.Eq(cast.ToInt64(id))).Update(dal.Merchant.ErcMoney, newmoney)
		str := "后台充值余额(ERC)"
		dal.Tran.Create(&entity.Tran{
			UserID:        lib.Int64ToInt32Ptr(mch.ID),
			UserType:      lib.Int64ToInt32Ptr(int64(1)),
			Type:          lib.Int64ToInt32Ptr(int64(11)),
			Money:         &m64,
			Orderid:       &str,
			Rorderid:      &str,
			Transactionid: &str,
			Username:      mch.Username,
			Ctime:         lib.Int64ToInt32Ptr(time.Now().Unix()),
		})
		return c.Status(fiber.StatusOK).JSON(fiber.Map{
			"code": true,
			"msg":  "成功",
			"data": nil,
		})
	}
	bscmoney := c.FormValue("bscmoney")
	if bscmoney != "" {
		m64 := cast.ToFloat64(bscmoney)
		if m64 < 0.01 {
			return c.Status(fiber.StatusOK).JSON(fiber.Map{
				"code": true,
				"msg":  "最小充值金额0.01",
				"data": nil,
			})
		}
		mch, _ := dal.Merchant.Where(dal.Merchant.ID.Eq(cast.ToInt64(id))).First()
		newmoney, _ := decimal.NewFromFloat(*mch.BscMoney).Add(decimal.NewFromFloat(m64)).Float64()
		dal.Merchant.Where(dal.Merchant.ID.Eq(cast.ToInt64(id))).Update(dal.Merchant.BscMoney, newmoney)
		str := "后台充值余额(BSC)"
		dal.Tran.Create(&entity.Tran{
			UserID:        lib.Int64ToInt32Ptr(mch.ID),
			UserType:      lib.Int64ToInt32Ptr(int64(1)),
			Type:          lib.Int64ToInt32Ptr(int64(20)),
			Money:         &m64,
			Orderid:       &str,
			Rorderid:      &str,
			Transactionid: &str,
			Username:      mch.Username,
			Ctime:         lib.Int64ToInt32Ptr(time.Now().Unix()),
		})
		return c.Status(fiber.StatusOK).JSON(fiber.Map{
			"code": true,
			"msg":  "成功",
			"data": nil,
		})
	}
	feemoney := c.FormValue("feemoney")
	if feemoney != "" {
		m64 := cast.ToFloat64(feemoney)
		if m64 < 0.01 {
			return c.Status(fiber.StatusOK).JSON(fiber.Map{
				"code": true,
				"msg":  "最小充值金额0.01",
				"data": nil,
			})
		}
		mch, _ := dal.Merchant.Where(dal.Merchant.ID.Eq(cast.ToInt64(id))).First()
		newmoney, _ := decimal.NewFromFloat(*mch.FeeMoney).Add(decimal.NewFromFloat(m64)).Float64()
		dal.Merchant.Where(dal.Merchant.ID.Eq(cast.ToInt64(id))).Update(dal.Merchant.FeeMoney, newmoney)
		str := "后台充值手续费(TRC)"
		dal.Tran.Create(&entity.Tran{
			UserID:        lib.Int64ToInt32Ptr(mch.ID),
			UserType:      lib.Int64ToInt32Ptr(int64(1)),
			Type:          lib.Int64ToInt32Ptr(int64(10)),
			Money:         &m64,
			Orderid:       &str,
			Rorderid:      &str,
			Transactionid: &str,
			Username:      mch.Username,
			Ctime:         lib.Int64ToInt32Ptr(time.Now().Unix()),
		})
		return c.Status(fiber.StatusOK).JSON(fiber.Map{
			"code": true,
			"msg":  "成功",
			"data": nil,
		})
	}
	ercfeemoney := c.FormValue("ercfeemoney")
	if ercfeemoney != "" {
		m64 := cast.ToFloat64(ercfeemoney)
		if m64 < 0.001 {
			return c.Status(fiber.StatusOK).JSON(fiber.Map{
				"code": true,
				"msg":  "最小充值金额0.001",
				"data": nil,
			})
		}
		mch, _ := dal.Merchant.Where(dal.Merchant.ID.Eq(cast.ToInt64(id))).First()
		newmoney, _ := decimal.NewFromFloat(*mch.EthFeemoney).Add(decimal.NewFromFloat(m64)).Float64()
		dal.Merchant.Where(dal.Merchant.ID.Eq(cast.ToInt64(id))).Update(dal.Merchant.EthFeemoney, newmoney)
		str := "后台充值手续费(ERC)"
		dal.Tran.Create(&entity.Tran{
			UserID:        lib.Int64ToInt32Ptr(mch.ID),
			UserType:      lib.Int64ToInt32Ptr(int64(1)),
			Type:          lib.Int64ToInt32Ptr(int64(12)),
			Money:         &m64,
			Orderid:       &str,
			Rorderid:      &str,
			Transactionid: &str,
			Username:      mch.Username,
			Ctime:         lib.Int64ToInt32Ptr(time.Now().Unix()),
		})
		return c.Status(fiber.StatusOK).JSON(fiber.Map{
			"code": true,
			"msg":  "成功",
			"data": nil,
		})
	}
	bscfeemoney := c.FormValue("bscfeemoney")
	if bscfeemoney != "" {
		m64 := cast.ToFloat64(bscfeemoney)
		if m64 < 0.001 {
			return c.Status(fiber.StatusOK).JSON(fiber.Map{
				"code": true,
				"msg":  "最小充值金额0.001",
				"data": nil,
			})
		}
		mch, _ := dal.Merchant.Where(dal.Merchant.ID.Eq(cast.ToInt64(id))).First()
		newmoney, _ := decimal.NewFromFloat(*mch.BscFeemoney).Add(decimal.NewFromFloat(m64)).Float64()
		dal.Merchant.Where(dal.Merchant.ID.Eq(cast.ToInt64(id))).Update(dal.Merchant.BscFeemoney, newmoney)
		str := "后台充值手续费(BSC)"
		dal.Tran.Create(&entity.Tran{
			UserID:        lib.Int64ToInt32Ptr(mch.ID),
			UserType:      lib.Int64ToInt32Ptr(int64(1)),
			Type:          lib.Int64ToInt32Ptr(int64(21)),
			Money:         &m64,
			Orderid:       &str,
			Rorderid:      &str,
			Transactionid: &str,
			Username:      mch.Username,
			Ctime:         lib.Int64ToInt32Ptr(time.Now().Unix()),
		})
		return c.Status(fiber.StatusOK).JSON(fiber.Map{
			"code": true,
			"msg":  "成功",
			"data": nil,
		})
	}
	password := c.FormValue("password")
	if password != "" {
		dal.Merchant.Where(dal.Merchant.ID.Eq(cast.ToInt64(id))).Update(dal.Merchant.Password, lib.Md5(password))
		return c.Status(fiber.StatusOK).JSON(fiber.Map{
			"code": true,
			"msg":  "成功",
			"data": nil,
		})
	}
	fee := cast.ToFloat64(c.FormValue("fee"))
	commissionPrice := cast.ToFloat64(c.FormValue("commission_price"))
	ethfee := cast.ToFloat64(c.FormValue("ethfee"))
	bscfee := cast.ToFloat64(c.FormValue("bscfee"))
	credits := cast.ToFloat64(c.FormValue("credits"))
	is_floatdown := c.FormValue("is_floatdown")
	gather := cast.ToInt32(c.FormValue("gather"))
	dal.Merchant.Where(dal.Merchant.ID.Eq(cast.ToInt64(id))).Updates(&entity.Merchant{
		Fee:             &fee,
		Ethfee:          &ethfee,
		Bscfee:          &bscfee,
		Credits:         &credits,
		IsFloatdown:     &is_floatdown,
		GatherType:      &gather,
		CommissionPrice: &commissionPrice,
	})
	return c.Status(fiber.StatusOK).JSON(fiber.Map{
		"code": true,
		"msg":  "成功",
		"data": nil,
	})
}

// 修改商户
func ResetGoogle(c *fiber.Ctx) error {
	id := c.FormValue("id")
	dal.Merchant.Where(dal.Merchant.ID.Eq(cast.ToInt64(id))).Update(dal.Merchant.Secret, nil)
	return c.Status(fiber.StatusOK).JSON(fiber.Map{
		"code": true,
		"msg":  "成功",
		"data": nil,
	})
}
