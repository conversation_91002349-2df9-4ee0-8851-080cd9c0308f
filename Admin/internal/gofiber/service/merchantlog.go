package service

import (
	"Admin/dal"
	"Admin/internal/lib"
	"github.com/gofiber/fiber/v2"
	"github.com/spf13/cast"
	"gorm.io/gen"
	"time"
)

// 获取商户操作记录列表
func GetMerchantLogList(c *fiber.Ctx) error {
	page := c.FormValue("page")
	intpage := lib.StrToint64(page)
	limit := c.FormValue("limit")
	intlimit := lib.StrToint64(limit)
	var offset int64 = 0
	if intpage == 1 {
		offset = 0
	} else {
		offset = (intpage - 1) * intlimit
	}
	var where []gen.Condition
	mchname := c.FormValue("mchname")
	if mchname != "" {
		where = append(where, dal.Merchantlog.MerchantName.Eq(mchname))
	}
	types := c.FormValue("type")
	if types != "" {
		where = append(where, dal.Merchantlog.Type.Eq(cast.ToInt32(types)))
	}
	start_time := c.FormValue("start_time")
	end_time := c.FormValue("end_time")
	if start_time != "" {
		tres, _ := time.ParseInLocation("2006-01-02 15:04:05", start_time, time.Local)
		where = append(where, dal.Merchantlog.Ctime.Gt(cast.ToInt32(tres.Unix())))
	}
	if end_time != "" {
		tres, _ := time.ParseInLocation("2006-01-02 15:04:05", end_time, time.Local)
		where = append(where, dal.Merchantlog.Ctime.Lt(cast.ToInt32(tres.Unix())))
	}
	list, _ := dal.Merchantlog.Where(where...).Order(dal.Merchantlog.ID.Desc()).Limit(cast.ToInt(limit)).Offset(cast.ToInt(offset)).Find()
	count, _ := dal.Merchantlog.Where(where...).Count()
	return c.Status(fiber.StatusOK).JSON(fiber.Map{
		"code":  0,
		"count": count,
		"data":  list,
	})
}
