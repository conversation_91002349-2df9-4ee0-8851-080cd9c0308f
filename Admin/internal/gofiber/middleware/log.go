package middleware

import (
	"Admin/dal"
	"Admin/internal/lib"
	"Admin/model/entity"
	"encoding/json"
	"fmt"
	"github.com/gofiber/fiber/v2"
	"github.com/spf13/cast"
	"net/url"
	"strings"
	"time"
)

// 管理员日志生成
func DoLog() fiber.Handler {
	return func(c *fiber.Ctx) error {
		// 先处理请求
		err := c.Next()
		if err != nil {
			return c.Status(fiber.StatusOK).JSON(fiber.Map{"code": false, "msg": "存入日志失败1", "data": nil})
		}
		// 获取用户信息
		userid, ok := c.Locals("userid").(string)
		username, nok := c.Locals("username").(string)
		if !ok || !nok {
			return c.Status(fiber.StatusOK).JSON(fiber.Map{"code": false, "msg": "未登录", "data": nil})
		}
		path := c.Path()
		method := c.Method()
		ip := c.Locals("ip").(string)
		// 获取请求参数并转换为 JSON 字符串
		var content string
		if c.Request().Body() == nil || len(c.Request().Body()) == 0 {
			content = ""
		} else {
			// 解析表单数据
			//formData := make(map[string][]string)
			//form, err := c.MultipartForm()
			//if err != nil {
			//	return c.Status(fiber.StatusOK).JSON(fiber.Map{"code": false, "msg": "存入日志失败2", "data": nil})
			//}
			//for key, values := range form.Value {
			//	formData[key] = values
			//}
			//// 将表单数据转换为 JSON
			//contentBytes, err := json.Marshal(formData)
			//if err != nil {
			//	return c.Status(fiber.StatusOK).JSON(fiber.Map{"code": false, "msg": "存入日志失败3	", "data": nil})
			//}
			//content = string(contentBytes)
			// 创建一个 map 来存储表单数据
			formData := make(map[string]interface{})

			// 检查 Content-Type
			contentType := c.Get("Content-Type")
			fmt.Println(contentType)
			// 根据 Content-Type 解析请求体
			if strings.Contains(contentType, "application/x-www-form-urlencoded") {
				body := string(c.Body())
				values, err := url.ParseQuery(body)
				if err != nil {
					return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{"error": "Cannot parse form"})
				}
				for key, value := range values {
					if len(value) > 1 {
						formData[key] = value
					} else {
						formData[key] = value[0]
					}
				}
			} else if strings.Contains(contentType, "multipart/form-data") {
				form, err := c.MultipartForm()
				if err != nil {
					fmt.Println(err)
					return c.Status(fiber.StatusOK).JSON(fiber.Map{"code": false, "msg": "存入日志失败2", "data": nil})
				}
				for key, values := range form.Value {
					if len(values) > 1 {
						formData[key] = values
					} else {
						formData[key] = values[0]
					}
				}
			} else if strings.Contains(contentType, "application/json") {
				if err := c.BodyParser(&formData); err != nil {
					return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{"error": "Cannot parse JSON"})
				}
			} else {
				return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{"error": "Unsupported Content-Type"})
			}

			// 检查并修改特定的键值
			keys := []string{"confirmpwd", "newpwd", "oldpwd", "password"}
			for _, key := range keys {
				if _, ok := formData[key]; ok {
					formData[key] = "******"
				}
			}

			// 将表单数据转换为 JSON
			contentBytes, err := json.Marshal(formData)
			if err != nil {
				return c.Status(fiber.StatusOK).JSON(fiber.Map{"code": false, "msg": "存入日志失败3", "data": nil})
			}
			content = string(contentBytes)
		}
		per, perr := dal.Permission.Where(dal.Permission.Route.Eq(path)).First()
		var name string
		if per == nil || perr != nil {
			name = path
		} else {
			name = *per.Name
		}
		// 创建日志
		dal.Adminslog.Create(&entity.Adminslog{
			Adminid:  lib.Int64ToInt32Ptr(cast.ToInt64(userid)),
			Username: &username,
			Action:   &name,
			Route:    &path,
			Method:   &method,
			Content:  &content,
			IP:       &ip,
			Ctime:    lib.Int64ToInt32Ptr(time.Now().Unix()),
			DelFlg:   lib.Int64ToInt32Ptr(0),
		})
		return nil
	}
}
