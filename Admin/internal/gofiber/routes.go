package gofiber

import (
	"Admin/internal/gofiber/controller"
	"Admin/internal/gofiber/middleware"
	"github.com/gofiber/fiber/v2"
)

func SetupRoutes(app *fiber.App) {
	//登录
	app.Post("/Auth/Login", controller.DoLogin)

	//jwt中间件
	app.Use(middleware.JwtMiddleware())
	//退出登录
	app.Post("/Auth/Logout", controller.DoLogout)
	app.Use(middleware.DoPermission())
	app.Use(middleware.DoLog())
	//地址
	//获取地址列表
	app.Post("/Address/GetAddressList", controller.GetAddressList)
	//获取地址详情
	app.Post("/Address/GetAddressInfo", controller.GetAddressInfo)
	//删除地址
	app.Post("/Address/DeleteAddress", controller.DeleteAddress)
	//执行归集
	app.Post("/Address/DoCollection", controller.DoCollection)

	//配置
	//获取网站配置
	app.Post("/Config/GetConfigInfo", controller.GetConfigInfo)
	//修改网站配置
	app.Post("/Config/UpdateConfig", controller.UpdateConfig)

	//充值订单
	//获取充值订单列表
	app.Post("/Order/GetOrderList", controller.GetOrderList)
	//获取充值订单详情
	app.Post("/Order/GetOrderInfo", controller.GetOrderInfo)
	//修改充值订单状态
	app.Post("/Order/UpdateOrder", controller.UpdateOrder)
	//补发充值订单回调
	app.Post("/Order/DoCallbackOrder", controller.DoCallbackOrder)
	//创建空单
	app.Post("/Order/CreateEmptyOrder", controller.CreateEmptyOrder)

	//下发订单
	//获取下发订单列表
	app.Post("/PayOrder/GetPayOrderList", controller.GetPayOrderList)
	//获取下发订单详情
	app.Post("/PayOrder/GetPayOrderInfo", controller.GetPayOrderInfo)
	//修改下发订单状态
	app.Post("/PayOrder/UpdatePayOrder", controller.UpdatePayOrder)
	//补发下发订单回调
	app.Post("/PayOrder/DoCallbackPayOrder", controller.DoCallbackPayOrder)

	//商户
	//获取商户列表
	app.Post("/Merchant/GetMerchantList", controller.GetMerchantList)
	//获取商户详情
	app.Post("/Merchant/GetMerchantInfo", controller.GetMerchantInfo)
	//添加商户
	app.Post("/Merchant/InsertMerchant", controller.InsertMerchant)
	//修改商户
	app.Post("/Merchant/UpdateMerchant", controller.UpdateMerchant)
	//重置谷歌
	app.Post("/Merchant/ResetGoogle", controller.ResetGoogle)

	//商户操作记录
	//获取商户操作记录列表
	app.Post("/MerchantLog/GetMerchantLogList", controller.GetMerchantLogList)

	//商户提现
	//获取商户提现列表
	app.Post("/Settlement/GetSettlementList", controller.GetSettlementList)
	//获取商户提现详情
	app.Post("/Settlement/GetSettlementInfo", controller.GetSettlementInfo)
	//修改商户提现状态
	app.Post("/Settlement/UpdateSettlement", controller.UpdateSettlement)

	//商户资金明细
	//获取商户资金明细列表
	app.Post("/Tran/GetTranList", controller.GetTranList)

	//管理员
	//获取管理员列表
	app.Post("/Admin/GetAdminList", controller.GetAdminList)
	//获取管理员详情
	app.Post("/Admin/GetAdminInfo", controller.GetAdminInfo)
	//删除管理员
	app.Post("/Admin/DeleteAdmin", controller.DeleteAdmin)
	//添加管理员
	app.Post("/Admin/InsertAdmin", controller.InsertAdmin)
	//修改管理员
	app.Post("/Admin/UpdateAdmin", controller.UpdateAdmin)
	//改密
	app.Post("/Admin/UpdatePassword", controller.UpdatePassword)
	//修改自己密码
	app.Post("/Admin/ResetPassword", controller.ResetPassword)

	//角色
	//获取角色列表
	app.Post("/Role/GetRoleList", controller.GetRoleList)
	//获取角色详情
	app.Post("/Role/GetRoleInfo", controller.GetRoleInfo)
	//删除角色
	app.Post("/Role/DeleteRole", controller.DeleteRole)
	//添加角色
	app.Post("/Role/InsertRole", controller.InsertRole)
	//修改角色
	app.Post("/Role/UpdateRole", controller.UpdateRole)

	//权限
	//获取权限列表
	app.Post("/Permission/GetPermissionList", controller.GetPermissionList)

	//主页
	//获取主页统计数据
	app.Post("/Stat/GetConsole", controller.GetConsole)

	//后台日志
	//获取后台日志列表
	app.Post("/AdminLog/GetAdminLogList", controller.GetAdminLogList)

	//白名单
	//获取白名单列表
	app.Post("/WhiteList/GetWhiteList", controller.GetWhiteList)
	//添加白名单
	app.Post("/WhiteList/InsertWhite", controller.InsertWhite)
	//删除白名单
	app.Post("/WhiteList/DeleteWhite", controller.DeleteWhite)
}
