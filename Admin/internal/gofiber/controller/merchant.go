package controller

import (
	"Admin/internal/gofiber/service"
	"github.com/gofiber/fiber/v2"
)

// 获取商户列表
func GetMerchantList(c *fiber.Ctx) error {
	return service.GetMerchantList(c)
}

// 获取商户详情
func GetMerchantInfo(c *fiber.Ctx) error {
	return service.GetMerchantInfo(c)
}

// 添加商户
func InsertMerchant(c *fiber.Ctx) error {
	return service.InsertMerchant(c)
}

// 修改商户
func UpdateMerchant(c *fiber.Ctx) error {
	return service.UpdateMerchant(c)
}

// 充值谷歌
func ResetGoogle(c *fiber.Ctx) error {
	return service.ResetGoogle(c)
}
