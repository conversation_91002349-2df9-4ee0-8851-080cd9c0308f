package controller

import (
	"Admin/internal/gofiber/service"
	"github.com/gofiber/fiber/v2"
)

// 获取角色列表
func GetRoleList(c *fiber.Ctx) error {
	return service.GetRoleList(c)
}

// 获取角色详情
func GetRoleInfo(c *fiber.Ctx) error {
	return service.GetRoleInfo(c)
}

// 删除角色
func DeleteRole(c *fiber.Ctx) error {
	return service.DeleteRole(c)
}

// 添加角色
func InsertRole(c *fiber.Ctx) error {
	return service.InsertRole(c)
}

// 修改角色
func UpdateRole(c *fiber.Ctx) error {
	return service.UpdateRole(c)
}
