// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package entity

const TableNameMerchantaddress = "merchantaddress"

// Merchantaddress mapped from table <merchantaddress>
type Merchantaddress struct {
	ID         int32   `gorm:"column:id;type:int unsigned;primaryKey;autoIncrement:true" json:"id"`
	Address    *string `gorm:"column:address;type:varchar(255)" json:"address"`
	MerchantID *int32  `gorm:"column:merchant_id;type:int" json:"merchant_id"`
	Ctime      *int32  `gorm:"column:ctime;type:int" json:"ctime"`
	DelFlg     *int32  `gorm:"column:del_flg;type:int" json:"del_flg"`
}

// TableName Merchantaddress's table name
func (*Merchantaddress) TableName() string {
	return TableNameMerchantaddress
}
