// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package entity

const TableNameLease = "lease"

// Lease mapped from table <lease>
type Lease struct {
	ID        int32   `gorm:"column:id;type:int unsigned;primaryKey;autoIncrement:true" json:"id"`
	Addressid *int32  `gorm:"column:addressid;type:int" json:"addressid"`
	Leaseid   *string `gorm:"column:leaseid;type:varchar(255)" json:"leaseid"`
	Orderid   *int32  `gorm:"column:orderid;type:int" json:"orderid"`
	Status    *int32  `gorm:"column:status;type:int" json:"status"`
	Ctime     *int32  `gorm:"column:ctime;type:int" json:"ctime"`
}

// TableName Lease's table name
func (*Lease) TableName() string {
	return TableNameLease
}
