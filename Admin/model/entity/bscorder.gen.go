// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package entity

const TableNameBscorder = "bscorder"

// Bscorder mapped from table <bscorder>
type Bscorder struct {
	ID             int32  `gorm:"column:id;type:int;primaryKey;autoIncrement:true;comment:id" json:"id"`
	Transactionid  string `gorm:"column:transactionid;type:varchar(255);not null;comment:transactionid" json:"transactionid"`
	From           string `gorm:"column:from;type:varchar(255);not null;comment:from" json:"from"`
	To             string `gorm:"column:to;type:varchar(255);not null;comment:to" json:"to"`
	Value          string `gorm:"column:value;type:varchar(255);not null;comment:value" json:"value"`
	BlockTimestamp int32  `gorm:"column:block_timestamp;type:int;not null;comment:时间" json:"block_timestamp"`
}

// TableName Bscorder's table name
func (*Bscorder) TableName() string {
	return TableNameBscorder
}
