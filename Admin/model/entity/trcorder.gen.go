// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package entity

const TableNameTrcorder = "trcorder"

// Trcorder mapped from table <trcorder>
type Trcorder struct {
	ID             int32  `gorm:"column:id;type:int;primaryKey;autoIncrement:true;comment:id" json:"id"`
	Transactionid  string `gorm:"column:transactionid;type:varchar(255);not null;comment:transactionid" json:"transactionid"`
	From           string `gorm:"column:from;type:varchar(255);not null;comment:from" json:"from"`
	To             string `gorm:"column:to;type:varchar(255);not null;comment:to" json:"to"`
	Value          string `gorm:"column:value;type:varchar(255);not null;comment:value" json:"value"`
	BlockTimestamp int32  `gorm:"column:block_timestamp;type:int;not null;comment:block_timestamp" json:"block_timestamp"`
}

// TableName Trcorder's table name
func (*Trcorder) TableName() string {
	return TableNameTrcorder
}
