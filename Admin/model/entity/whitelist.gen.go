// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package entity

import (
	"time"

	"gorm.io/gorm"
)

const TableNameWhitelist = "whitelist"

// Whitelist mapped from table <whitelist>
type Whitelist struct {
	ID        int64          `gorm:"column:id;type:bigint unsigned;primaryKey;autoIncrement:true" json:"id"`
	CreatedAt *time.Time     `gorm:"column:created_at;type:datetime(3)" json:"created_at"`
	UpdatedAt *time.Time     `gorm:"column:updated_at;type:datetime(3)" json:"updated_at"`
	DeletedAt gorm.DeletedAt `gorm:"column:deleted_at;type:datetime(3);index:idx_whitelist_deleted_at,priority:1" json:"deleted_at"`
	Type      *int32         `gorm:"column:type;type:smallint;comment:1:商户白名单" json:"type"`
	UserID    *int32         `gorm:"column:user_id;type:smallint;comment:商户或代理ID" json:"user_id"`
	Content   *string        `gorm:"column:content;type:varchar(255);comment:IP地址" json:"content"`
	Username  *string        `gorm:"column:username;type:varchar(255);comment:用户名" json:"username"`
}

// TableName Whitelist's table name
func (*Whitelist) TableName() string {
	return TableNameWhitelist
}
