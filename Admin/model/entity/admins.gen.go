// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package entity

const TableNameAdmin = "admins"

// Admin mapped from table <admins>
type Admin struct {
	ID       int32   `gorm:"column:id;type:int unsigned;primaryKey;autoIncrement:true" json:"id"`
	Username *string `gorm:"column:username;type:varchar(255);comment:用户名" json:"username"`
	Password *string `gorm:"column:password;type:varchar(255);comment:密码" json:"password"`
	Status   *int32  `gorm:"column:status;type:int;comment:0 启用 1 禁用" json:"status"`
	Ctime    *int32  `gorm:"column:ctime;type:int" json:"ctime"`
	DelFlg   *int32  `gorm:"column:del_flg;type:int" json:"del_flg"`
}

// TableName Admin's table name
func (*Admin) TableName() string {
	return TableNameAdmin
}
