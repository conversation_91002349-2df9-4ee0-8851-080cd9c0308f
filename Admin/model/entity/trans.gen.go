// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package entity

const TableNameTran = "trans"

// Tran mapped from table <trans>
type Tran struct {
	ID            int32    `gorm:"column:id;type:int unsigned;primaryKey;autoIncrement:true" json:"id"`
	UserID        *int32   `gorm:"column:user_id;type:int;comment:用户id" json:"user_id"`
	UserType      *int32   `gorm:"column:user_type;type:int;comment:1商户 2代理" json:"user_type"`
	Type          *int32   `gorm:"column:type;type:int;comment:1商户订单收入(TRC) 2商户手续费扣除(TRC) 3商户下发支出(TRC) 4归集支出(TRC) 5商户订单收入(ERC) 6商户手续费扣除(ERC) 7商户下发支出(ERC) 8归集支出(ERC)" json:"type"`
	Money         *float64 `gorm:"column:money;type:decimal(20,5);comment:金额" json:"money"`
	Orderid       *string  `gorm:"column:orderid;type:varchar(255);comment:平台订单id" json:"orderid"`
	Rorderid      *string  `gorm:"column:rorderid;type:varchar(255);comment:商户订单id" json:"rorderid"`
	Transactionid *string  `gorm:"column:transactionid;type:varchar(255);comment:hash" json:"transactionid"`
	Username      *string  `gorm:"column:username;type:varchar(255);comment:用户名" json:"username"`
	Remark        *string  `gorm:"column:remark;type:varchar(255);comment:备注" json:"remark"`
	Ctime         *int32   `gorm:"column:ctime;type:int" json:"ctime"`
	St            *int32   `gorm:"column:st;type:int;default:1" json:"st"`
}

// TableName Tran's table name
func (*Tran) TableName() string {
	return TableNameTran
}
