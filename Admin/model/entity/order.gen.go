// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package entity

import (
	"time"

	"gorm.io/gorm"
)

const TableNameOrder = "order"

// Order mapped from table <order>
type Order struct {
	ID             int64          `gorm:"column:id;type:bigint unsigned;primaryKey;autoIncrement:true" json:"id"`
	CreatedAt      *time.Time     `gorm:"column:created_at;type:datetime(3)" json:"created_at"`
	UpdatedAt      *time.Time     `gorm:"column:updated_at;type:datetime(3)" json:"updated_at"`
	DeletedAt      gorm.DeletedAt `gorm:"column:deleted_at;type:datetime(3);index:idx_order_deleted_at,priority:1" json:"deleted_at"`
	MerchantID     *string        `gorm:"column:merchant_id;type:varchar(11);comment:商户id" json:"merchant_id"`
	Appkey         *string        `gorm:"column:appkey;type:varchar(255);comment:商户密钥" json:"appkey"`
	Transactionid  *string        `gorm:"column:transactionid;type:varchar(255)" json:"transactionid"`
	Orderid        *string        `gorm:"column:orderid;type:varchar(255);comment:订单号" json:"orderid"`
	Rorderid       *string        `gorm:"column:rorderid;type:varchar(255);comment:商户订单id" json:"rorderid"`
	Fromaddr       *string        `gorm:"column:fromaddr;type:varchar(255);comment:付款地址" json:"fromaddr"`
	Score          *string        `gorm:"column:score;type:varchar(255);comment:付款地址风险评分" json:"score"`
	Trxadd         *string        `gorm:"column:trxadd;type:varchar(255);comment:TRX地址" json:"trxadd"`
	Addrid         *int32         `gorm:"column:addrid;type:int" json:"addrid"`
	Money          *string        `gorm:"column:money;type:varchar(255);comment:金额" json:"money"`
	Status         *int32         `gorm:"column:status;type:int;comment:0:创建订单 1:超时 2:支付成功 3:回调成功 4:主动成功" json:"status"`
	Callbackurl    *string        `gorm:"column:callbackurl;type:varchar(255);comment:回调地址" json:"callbackurl"`
	Callbacknum    *int32         `gorm:"column:callbacknum;type:int;comment:回调次数" json:"callbacknum"`
	Callbackresult *string        `gorm:"column:callbackresult;type:text;comment:回调返回信息" json:"callbackresult"`
	MerchantName   *string        `gorm:"column:merchant_name;type:varchar(191);comment:商户名" json:"merchant_name"`
	Ctime          *int32         `gorm:"column:ctime;type:int" json:"ctime"`
	Etime          *int32         `gorm:"column:etime;type:int" json:"etime"`
	Returnurl      *string        `gorm:"column:returnurl;type:varchar(255);comment:跳转地址" json:"returnurl"`
	Rmb            *string        `gorm:"column:rmb;type:varchar(255)" json:"rmb"`
	Rate           *string        `gorm:"column:rate;type:varchar(255);comment:汇率" json:"rate"`
	Realmoney      *string        `gorm:"column:realmoney;type:varchar(255);comment:实收金额" json:"realmoney"`
	Chain          *string        `gorm:"column:chain;type:varchar(191);comment:链" json:"chain"`
	St             *int32         `gorm:"column:st;type:int;default:1" json:"st"`
}

// TableName Order's table name
func (*Order) TableName() string {
	return TableNameOrder
}
