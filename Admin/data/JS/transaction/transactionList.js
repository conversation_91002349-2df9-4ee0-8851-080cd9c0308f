layui.config({
    base: '/layuiadmin/' //静态资源所在路径
}).extend({
    index: 'lib/index' //主入口模块
}).use(['index', 'table','laydate'], function () {
    var $ = layui.$
        , form = layui.form
        , table = layui.table
        , laydate = layui.laydate;

    //监听搜索
    form.on('submit(LAY-user-front-search)', function (data) {
        var field = data.field;
        //执行重载
        table.reload('LAY-user-manage', {
            where: field
        });
    });

    //事件
    var active = {
        batchdel: function () {
            var checkStatus = table.checkStatus('LAY-user-manage')
                , checkData = checkStatus.data; //得到选中的数据
            alert(111);
            if (checkData.length === 0) {
                return layer.msg('请选择数据');
            }

            layer.prompt({
                formType: 1
                , title: '敏感操作，请验证口令'
            }, function (value, index) {
                layer.close(index);


            });
        }
    };

    $('.layui-btn.layuiadmin-btn-useradmin').on('click', function () {
        var type = $(this).data('type');
        active[type] ? active[type].call(this) : '';
    });

    //定义返回格式
    var parseDataFun = function (res) {
        return {
            "code": res.code == 200,
            "message": res.msg,
            "data": res.data.data,
        };
    };
    //渲染表格
    table.render({
        elem: '#LAY-user-manage'
        , url: interfaceUrl + '/Tran/GetTranList'
        , headers: { "Authorization": token}
        , parseData: parseDataFun.data
        , method: 'post'
        , xhrFields: {withCredentials: true}
        // , cellMinWidth: 80 //全局定义常规单元格的最小宽度，layui 2.2.1 新增
        , cols: [[
            {field: 'type', title: '类型',
                templet: function (provider) {
                    if(provider.type == 1){
                        return '订单收入(TRC)';
                    }else if(provider.type == 2){
                        return '手续费支出(TRC)';
                    }else if(provider.type == 3){
                        return '下发支出(TRC)';
                    }else if(provider.type == 4){
                        return '归集支出(TRC)';
                    }else if(provider.type == 5){
                        return '订单收入(ERC)';
                    }else if(provider.type == 6){
                        return '手续费支出(ERC)';
                    }else if(provider.type == 7){
                        return '下发支出(ERC)';
                    }else if(provider.type == 8){
                        return '归集支出(ERC)';
                    }else if(provider.type == 9){
                        return '后台充值余额(TRC)';
                    }else if(provider.type == 10){
                        return '后台充值手续费(TRC)';
                    }else if(provider.type == 11){
                        return '后台充值余额(ERC)';
                    }else if(provider.type == 12){
                        return '后台充值手续费(ERC)';
                    }else if(provider.type == 13){
                        return '归集支出手续费(TRC)';
                    }else if(provider.type == 14){
                        return '归集支出手续费(ERC)';
                    }else if(provider.type == 15){
                        return '订单收入(BSC)';
                    }else if(provider.type == 16){
                        return '手续费支出(BSC)';
                    }else if(provider.type == 17){
                        return '下发支出(BSC)';
                    }else if(provider.type == 18){
                        return '归集支出余额(BSC)';
                    }else if(provider.type == 19){
                        return '归集支出手续费(BSC)';
                    }else if(provider.type == 20){
                        return '后台充值余额(BSC)';
                    }else if(provider.type == 21){
                        return '后台充值手续费(BSC)';
                    }else if(provider.type == 22){
                        return '补充值手续费(TRC)';
                    }else if(provider.type == 23){
                        return '补充值手续费(ERC)';
                    }else if(provider.type == 24){
                        return '补充值手续费(BSC)';
                    }
                }
            }
            , {field: 'username', title: '商户名'}
            , {field: 'money', title: '金额'}
            , {field: 'orderid', title: '系统订单号'}
            , {field: 'rorderid', title: '商户订单号'}
            , {field: 'transactionid', title: '交易ID'}
            , {field: 'remark', title: '备注'}
            , {field: 'ctime', title: '创建时间', templet: '<div>{{ layui.laytpl.toDateString(d.ctime*1000) }}</div>'}
        ]]
        , page: true
        , limit: 30
        , limits: [30, 50, 100]
        , done: function (res) {
            if (res.msg == 'Token无效'){
                window.location.href='/login.html';
                return false;
            }
        }
        // ,layout: ['count', 'prev', 'page', 'next', 'limit', 'refresh', 'skip']
        , jump: function (obj, first) {
            if (!first) {
                layer.msg('第 ' + obj.curr + ' 页');
            }
        }
    });
    //检测行级数据
    table.on('tool(LAY-user-manage)', function (obj) { //注：tool是工具条事件名，test是table原始容器的属性 lay-filter="对应的值"
        //删除数据

    });

    var endDate= laydate.render({
		elem: '#end_time',//选择器结束时间
		type: 'datetime',
		min:"1970-1-1",//设置min默认最小值
		done: function(value,date){
		      $('#end_time').change(); 
			startDate.config.max={
				year:date.year,
				month:date.month-1,//关键
				date: date.date,
				hours: 0,
				minutes: 0,
				seconds : 0
			}
		}
	});
	//日期范围
	var startDate=laydate.render({
		elem: '#start_time',
		type: 'datetime',
		max:"2099-12-31",//设置一个默认最大值
		done: function(value, date){
		    $('#start_time').change(); 
			endDate.config.min ={
				year:date.year,
				month:date.month-1, //关键
				date: date.date,
				hours: 0,
				minutes: 0,
				seconds : 0
			};
		}
	});

});