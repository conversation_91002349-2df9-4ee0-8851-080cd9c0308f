layui.config({
    base: '/layuiadmin/' //静态资源所在路径
}).extend({
    index: 'lib/index' //主入口模块
}).use(['index', 'table'], function () {
    var $ = layui.$
        , form = layui.form
        , table = layui.table
        , laydate = layui.laydate;

    //监听搜索
    form.on('submit(LAY-user-front-search)', function (data) {
        var field = data.field;
        //执行重载
        table.reload('LAY-user-manage', {
            where: field
        });
    });

    //事件
    var active = {
        batchdel: function () {
            var checkStatus = table.checkStatus('LAY-user-manage')
                , checkData = checkStatus.data; //得到选中的数据
            alert(111);
            if (checkData.length === 0) {
                return layer.msg('请选择数据');
            }

            layer.prompt({
                formType: 1
                , title: '敏感操作，请验证口令'
            }, function (value, index) {
                layer.close(index);


            });
        }

        , add: function () {
            layer.open({
                type: 2
                , title: '添加商户'
                , content: 'createMerchant.html'
                , area: ['600px', '600px']
                , btn: ['确定', '取消']
                , yes: function (index, layero) {
                    var iframeWindow = window['layui-layer-iframe' + index]
                        , submit = layero.find('iframe').contents().find("#LAY-createAdmin-submit");

                    //监听提交
                    iframeWindow.layui.form.on('submit(LAY-createAdmin-submit)', function (data) {
                        var field = data.field; //获取提交的字段
                        $.ajax({
                            url: interfaceUrl + '/Merchant/InsertMerchant',
                            headers: { "Authorization": token },
                            type: 'POST',
                            data: field,
                            success: function (res_data) {                                
                                layer.msg(
                                    res_data.msg,
                                    {time : 1000},
                                    function() {
                                        location.reload();
                                    }
                                );
                            },
                        });
                        layer.close(index); //关闭弹层
                    });

                    submit.trigger('click');
                }
            });
        }
    };

    

    $('.layui-btn.layuiadmin-btn-useradmin').on('click', function () {
        var type = $(this).data('type');
        active[type] ? active[type].call(this) : '';
    });

    //定义返回格式
    var parseDataFun = function (res) {
        return {
            "code": res.code == 200,
            "message": res.msg,
            "data": res.data.data,
        };
    };
    //渲染表格
    table.render({
        elem: '#LAY-user-manage'
        , url: interfaceUrl + '/Merchant/GetMerchantList'
        , headers: { "Authorization": token}
        , parseData: parseDataFun.data
        , method: 'post'
        , xhrFields: {withCredentials: true}
        // , cellMinWidth: 80 //全局定义常规单元格的最小宽度，layui 2.2.1 新增
        , cols: [[
            {field: 'id', title: '商户ID'}
            , {field: 'username', title: '用户名'}
            // , {field: 'status', title: '状态',
            //     templet: function (provider) {
            //         if(provider.status == 0){
            //             return '正常';
            //         }else if(provider.status == 1){
            //             return '禁用';
            //         }
            //     }
            // }
            , {field: 'address_usdt', title: '未归集余额'}
            , {field: 'money', title: 'TRC余额'}
            , {field: 'erc_money', title: 'ERC余额'}
            , {field: 'bsc_money', title: 'BSC余额'}
            , {field: 'lock_money', title: 'TRC锁定余额'}
            , {field: 'erc_lockmoney', title: 'ERC锁定余额'}
            , {field: 'bsc_lockmoney', title: 'BSC锁定余额'}
            , {field: 'fee_money', title: 'TRX'}
            , {field: 'eth_feemoney', title: 'ETH'}
            , {field: 'bsc_feemoney', title: 'BNB'}
            , {field: 'trcaddress', title: 'TRX充值地址'}
            , {field: 'ercaddress', title: 'ETH充值地址'}
            // , {field: 'fee', title: 'Trc充值费率', templet: function(row) {
            //         return row.fee + ' ‰';
            //     }}
            // , {field: 'ethfee', title: 'Erc充值费率', templet: function(row) {
            //         return row.ethfee + ' ‰';
            //     }}
            // , {field: 'credits', title: '信用额度'}
            , {field: 'appkey', title: 'API密钥'}
            , {field: 'add', title: '操作', width: 550, toolbar: "#table-useradmin-webuser"}
        ]]
        , page: true
        , limit: 30
        , limits: [30, 50, 100]
        , done: function (res) {
            if (res.msg == 'Token无效'){
                window.location.href='/login.html';
                return false;
            }
        }
        // ,layout: ['count', 'prev', 'page', 'next', 'limit', 'refresh', 'skip']
        , jump: function (obj, first) {
            if (!first) {
                layer.msg('第 ' + obj.curr + ' 页');
            }
        }
    });
    //检测行级数据
    table.on('tool(LAY-user-manage)', function (obj) { //注：tool是工具条事件名，test是table原始容器的属性 lay-filter="对应的值"
        if (obj.event == 'enban') {
            layer.confirm('确定启用此商户？', function (index) {
                var formData = new FormData();
                formData.append('id', obj.data.id);
                formData.append('status', 0);
                $.ajax({
                    type: 'POST',
                    url: interfaceUrl + '/Merchant/UpdateMerchant',
                    beforeSend: function (xhr) {
                        xhr.setRequestHeader("Authorization", token);
                    },
                    data: formData, // 使用 FormData 对象
                    processData: false,
                    contentType: false,
                    success: function (res_data) {
                        if(res_data.code == true){
                            layer.msg(res_data.msg);
                            table.reload('LAY-user-manage');
                        }else{
                            layer.msg(res_data.msg);
                        }
                    },
                });
            });
        }
        if (obj.event == 'ban') {
            layer.confirm('确定禁用此商户？', function (index) {
                var formData = new FormData();
                formData.append('id', obj.data.id);
                formData.append('status', 1);
                $.ajax({
                    type: 'POST',
                    url: interfaceUrl + '/Merchant/UpdateMerchant',
                    beforeSend: function (xhr) {
                        xhr.setRequestHeader("Authorization", token);
                    },
                    data: formData, // 使用 FormData 对象
                    processData: false,
                    contentType: false,
                    success: function (res_data) {
                        if(res_data.code == true){
                            layer.msg(res_data.msg);
                            table.reload('LAY-user-manage');
                        }else{
                            layer.msg(res_data.msg);
                        }
                    },
                });
            });
        }

        if (obj.event == 'edit') {
            layer.open({
                type: 2
                , title: '修改商户'
                , content: 'editMerchant.html?id='+obj.data.id
                , area: ['600px', '600px']
                , btn: ['确定', '取消']
                , yes: function (index, layero) {
                    var iframeWindow = window['layui-layer-iframe' + index]
                        , submit = layero.find('iframe').contents().find("#LAY-createAdmin-submit");

                    //监听提交
                    iframeWindow.layui.form.on('submit(LAY-createAdmin-submit)', function (data) {
                        var field = data.field; //获取提交的字段
                        $.ajax({
                            url: interfaceUrl + '/Merchant/UpdateMerchant',
                            headers: { "Authorization": token },
                            type: 'POST',
                            data: field,
                            success: function (res_data) {
                                layer.msg(
                                    res_data.msg,
                                    {time : 1000},
                                    function() {
                                        location.reload();
                                    }
                                );
                            },
                        });
                        layer.close(index); //关闭弹层
                    });

                    submit.trigger('click');
                }
            });
        }


        if (obj.event == 'pwd') {
            layer.prompt({
                title: '请输入新的密码',
                formType: 1  //密码框
            }, function (value, index) {
                var formData = new FormData();
                formData.append('id', obj.data.id);
                formData.append('password', value);
                $.ajax({
                    type: 'POST',
                    url: interfaceUrl + '/Merchant/UpdateMerchant',
                    beforeSend: function (xhr) {
                        xhr.setRequestHeader("Authorization", token);
                    },
                    data: formData, // 使用 FormData 对象
                    processData: false,
                    contentType: false,
                    success: function (res_data) {
                        if(res_data.code == true){
                            layer.msg(res_data.msg);
                            table.reload('LAY-user-manage');
                        }else{
                            layer.msg(res_data.msg);
                        }
                    },
                });
                layer.close(index);  // 关闭输入框
            });
        }

        if (obj.event == 'money') {
            layer.prompt({
                title: '请输入充值的金额'
            }, function (value, index) {
                var formData = new FormData();
                formData.append('id', obj.data.id);
                formData.append('money', value);
                $.ajax({
                    type: 'POST',
                    url: interfaceUrl + '/Merchant/UpdateMerchant',
                    beforeSend: function (xhr) {
                        xhr.setRequestHeader("Authorization", token);
                    },
                    data: formData, // 使用 FormData 对象
                    processData: false,
                    contentType: false,
                    success: function (res_data) {
                        if(res_data.code == true){
                            layer.msg(res_data.msg);
                            table.reload('LAY-user-manage');
                        }else{
                            layer.msg(res_data.msg);
                        }
                    },
                });
                layer.close(index);  // 关闭输入框
            });
        }

        if (obj.event == 'feemoney') {
            layer.prompt({
                title: '请输入充值的金额'
            }, function (value, index) {
                var formData = new FormData();
                formData.append('id', obj.data.id);
                formData.append('feemoney', value);
                $.ajax({
                    type: 'POST',
                    url: interfaceUrl + '/Merchant/UpdateMerchant',
                    beforeSend: function (xhr) {
                        xhr.setRequestHeader("Authorization", token);
                    },
                    data: formData, // 使用 FormData 对象
                    processData: false,
                    contentType: false,
                    success: function (res_data) {
                        if(res_data.code == true){
                            layer.msg(res_data.msg);
                            table.reload('LAY-user-manage');
                        }else{
                            layer.msg(res_data.msg);
                        }
                    },
                });
                layer.close(index);  // 关闭输入框
            });
        }

        if (obj.event == 'ercmoney') {
            layer.prompt({
                title: '请输入充值的金额'
            }, function (value, index) {
                var formData = new FormData();
                formData.append('id', obj.data.id);
                formData.append('ercmoney', value);
                $.ajax({
                    type: 'POST',
                    url: interfaceUrl + '/Merchant/UpdateMerchant',
                    beforeSend: function (xhr) {
                        xhr.setRequestHeader("Authorization", token);
                    },
                    data: formData, // 使用 FormData 对象
                    processData: false,
                    contentType: false,
                    success: function (res_data) {
                        if(res_data.code == true){
                            layer.msg(res_data.msg);
                            table.reload('LAY-user-manage');
                        }else{
                            layer.msg(res_data.msg);
                        }
                    },
                });
                layer.close(index);  // 关闭输入框
            });
        }

        if (obj.event == 'ercfeemoney') {
            layer.prompt({
                title: '请输入充值的金额'
            }, function (value, index) {
                var formData = new FormData();
                formData.append('id', obj.data.id);
                formData.append('ercfeemoney', value);
                $.ajax({
                    type: 'POST',
                    url: interfaceUrl + '/Merchant/UpdateMerchant',
                    beforeSend: function (xhr) {
                        xhr.setRequestHeader("Authorization", token);
                    },
                    data: formData, // 使用 FormData 对象
                    processData: false,
                    contentType: false,
                    success: function (res_data) {
                        if(res_data.code == true){
                            layer.msg(res_data.msg);
                            table.reload('LAY-user-manage');
                        }else{
                            layer.msg(res_data.msg);
                        }
                    },
                });
                layer.close(index);  // 关闭输入框
            });
        }

        if (obj.event == 'bscmoney') {
            layer.prompt({
                title: '请输入充值的金额'
            }, function (value, index) {
                var formData = new FormData();
                formData.append('id', obj.data.id);
                formData.append('bscmoney', value);
                $.ajax({
                    type: 'POST',
                    url: interfaceUrl + '/Merchant/UpdateMerchant',
                    beforeSend: function (xhr) {
                        xhr.setRequestHeader("Authorization", token);
                    },
                    data: formData, // 使用 FormData 对象
                    processData: false,
                    contentType: false,
                    success: function (res_data) {
                        if(res_data.code == true){
                            layer.msg(res_data.msg);
                            table.reload('LAY-user-manage');
                        }else{
                            layer.msg(res_data.msg);
                        }
                    },
                });
                layer.close(index);  // 关闭输入框
            });
        }

        if (obj.event == 'bscfeemoney') {
            layer.prompt({
                title: '请输入充值的金额'
            }, function (value, index) {
                var formData = new FormData();
                formData.append('id', obj.data.id);
                formData.append('bscfeemoney', value);
                $.ajax({
                    type: 'POST',
                    url: interfaceUrl + '/Merchant/UpdateMerchant',
                    beforeSend: function (xhr) {
                        xhr.setRequestHeader("Authorization", token);
                    },
                    data: formData, // 使用 FormData 对象
                    processData: false,
                    contentType: false,
                    success: function (res_data) {
                        if(res_data.code == true){
                            layer.msg(res_data.msg);
                            table.reload('LAY-user-manage');
                        }else{
                            layer.msg(res_data.msg);
                        }
                    },
                });
                layer.close(index);  // 关闭输入框
            });
        }

        if (obj.event == 'google') {
            layer.confirm('确定重置Google验证吗？', function (index) {
                var formData = new FormData();
                formData.append('id', obj.data.id);
                $.ajax({
                    type: 'POST',
                    url: interfaceUrl + '/Merchant/ResetGoogle',
                    beforeSend: function (xhr) {
                        xhr.setRequestHeader("Authorization", token);
                    },
                    data: formData, // 使用 FormData 对象
                    processData: false,
                    contentType: false,
                    success: function (res_data) {
                        if(res_data.code == true){
                            layer.msg(res_data.msg);
                            table.reload('LAY-user-manage');
                        }else{
                            layer.msg(res_data.msg);
                        }
                    },
                });
            });
        }
    });

    var endDate= laydate.render({
		elem: '#end_time',//选择器结束时间
		type: 'datetime',
		min:"1970-1-1",//设置min默认最小值
		done: function(value,date){
		      $('#end_time').change(); 
			startDate.config.max={
				year:date.year,
				month:date.month-1,//关键
				date: date.date,
				hours: 0,
				minutes: 0,
				seconds : 0
			}
		}
	});
	//日期范围
	var startDate=laydate.render({
		elem: '#start_time',
		type: 'datetime',
		max:"2099-12-31",//设置一个默认最大值
		done: function(value, date){
		    $('#start_time').change(); 
			endDate.config.min ={
				year:date.year,
				month:date.month-1, //关键
				date: date.date,
				hours: 0,
				minutes: 0,
				seconds : 0
			};
		}
	});

});