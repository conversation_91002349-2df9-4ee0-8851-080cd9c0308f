layui.config({
    base: '/layuiadmin/' //静态资源所在路径
}).extend({
    index: 'lib/index' //主入口模块
}).use(['index', 'table'], function () {
    var $ = layui.$
        , form = layui.form
        , table = layui.table
        , laydate = layui.laydate;

    //监听搜索
    form.on('submit(LAY-user-front-search)', function (data) {
        var field = data.field;
        //执行重载
        table.reload('LAY-user-manage', {
            where: field
        });
    });

    //事件
    var active = {
        batchdel: function () {
            var checkStatus = table.checkStatus('LAY-user-manage')
                , checkData = checkStatus.data; //得到选中的数据
            alert(111);
            if (checkData.length === 0) {
                return layer.msg('请选择数据');
            }

            layer.prompt({
                formType: 1
                , title: '敏感操作，请验证口令'
            }, function (value, index) {
                layer.close(index);


            });
        }
    };

    $('.layui-btn.layuiadmin-btn-useradmin').on('click', function () {
        var type = $(this).data('type');
        active[type] ? active[type].call(this) : '';
    });

    //定义返回格式
    var parseDataFun = function (res) {
        return {
            "code": res.code == 200,
            "message": res.msg,
            "data": res.data.data,
        };
    };
    //渲染表格
    table.render({
        elem: '#LAY-user-manage'
        , url: interfaceUrl + '/Role/GetRoleList'
        , headers: { "Authorization": token}
        , parseData: parseDataFun.data
        , method: 'post'
        , xhrFields: {withCredentials: true}
        // , cellMinWidth: 80 //全局定义常规单元格的最小宽度，layui 2.2.1 新增
        , cols: [[
            {field: 'name', title: '角色名'}
            , {field: 'description', title: '描述'}
            , {field: 'ctime', title: '创建时间', templet: '<div>{{ layui.laytpl.toDateString(d.ctime*1000) }}</div>'}
            , {field: 'add', title: '操作', width: 200, toolbar: "#table-useradmin-webuser"}
        ]]
        , page: true
        , limit: 30
        , limits: [30, 50, 100]
        , done: function (res) {
            if (res.msg == 'Token无效'){
                window.location.href='/login.html';
                return false;
            }
        }
        // ,layout: ['count', 'prev', 'page', 'next', 'limit', 'refresh', 'skip']
        , jump: function (obj, first) {
            if (!first) {
                layer.msg('第 ' + obj.curr + ' 页');
            }
        }
    });
    //检测行级数据
    table.on('tool(LAY-user-manage)', function (obj) { //注：tool是工具条事件名，test是table原始容器的属性 lay-filter="对应的值"
        //删除数据
        if (obj.event == 'del') {
            layer.confirm('确定删除此角色吗？', function (index) {
                var formData = new FormData();
                formData.append('id', obj.data.id);
                $.ajax({
                    type: 'POST',
                    url: interfaceUrl + '/Role/DeleteRole',
                    beforeSend: function (xhr) {
                        xhr.setRequestHeader("Authorization", token);
                    },
                    data: formData, // 使用 FormData 对象
                    processData: false,
                    contentType: false,
                    success: function (res_data) {
                        if(res_data.code == true){
                            layer.msg(res_data.msg);
                            table.reload('LAY-user-manage');
                        }else{
                            layer.msg(res_data.msg);
                        }
                    },
                });
            });
        }else if (obj.event == 'per') {
            layer.msg("还没做");
            return false
        }
    });

});