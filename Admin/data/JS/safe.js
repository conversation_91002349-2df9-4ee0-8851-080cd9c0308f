layui.use(['jquery'], function(args){
    var $= layui.jquery;
    window.onload = function(){
      $.ajax({  
                headers: {  
                  Accept: "application/json; charset=utf-8" ,
                  Authorization: token,
                  ID: admin_info.id
                },  
                    type: "get",  
                    url: interfaceUrl + '/Mapi/GetUser?id='+admin_info.id,
                    success: function (res_data) { 
                        console.log(res_data); 
                        if (!res_data.code){
                            layer.msg('服务器发生异常!');
                        }else{
                          console.log(res_data.data.appkey);
                          $('#appkey').html(res_data.data.appkey);
                          $('#money').html(res_data.data.money);
                          $('#lock_money').html(res_data.data.lock_money);
                          $('#fee_money').html(res_data.data.fee_money);
                        }
                    },error:function(error){
                        console.log(error);
                    }

            });
              // $.get(interfaceUrl + '/Mapi/GetUser?id='+admin_info.id,function(res_data){
              //             $('#appkey').html(res_data.data.appkey);
              //             $('#money').html(res_data.data.money);
              // });
    }

    $('#open').click(function(){
      $.ajax({  
                headers: {  
                  Accept: "application/json; charset=utf-8" ,
                  Authorization: token,
                  ID: admin_info.id
                },  
                    type: "get",  
                    url: interfaceUrl + '/Mapi/OpenGoogle?id='+admin_info.id,
                    success: function (data) { 
                        console.log(data); 
                        if (!data.code){
                            layer.msg('服务器发生异常!');
                        }else{
                          $('#miyao').html('密钥:'+data.secret);
                          $('#miyao').attr('data',data.secret);
                          $('#qrCode').attr('src',data.qrCode);
                          $('#anniu').css('display','block');
                          alert('请在您的身份验证器中使用下方密钥或扫描二维码，设置完成后请点击下一步按钮');
                        }
                    },error:function(error){
                        console.log(error);
                    }

            });
      // $.get(interfaceUrl + '/Mapi/OpenGoogle?id='+admin_info.id,function(data){
      //                                 $('#miyao').html('密钥:'+data.secret);
      //                                 $('#miyao').attr('data',data.secret);
      //                                 $('#qrCode').attr('src',data.qrCode);
      //                                 $('#anniu').css('display','block');
      //                                 alert('请在您的身份验证器中使用下方密钥或扫描二维码，设置完成后请点击下一步按钮');
      //                         });
    })

    $('#anniu').click(function(){
      var secret = $('#miyao').attr('data');
      var code;
      code=prompt("请输入谷歌身份验证码"); 
      $.ajax({  
                headers: {  
                  Accept: "application/json; charset=utf-8" ,
                  Authorization: token,
                  ID: admin_info.id
                },  
                    type: "get",  
                    url: interfaceUrl + '/Mapi/CheckGoogle?id='+admin_info.id+"&code="+code+"&secret="+secret,//{'code':code,'secret':secret},
                    success: function (data) { 
                        console.log(data); 
                        if(data.code == true){
                          $.ajax({  
                          headers: {  
                              Accept: "application/json; charset=utf-8" ,
                              Authorization: token,
                              ID: admin_info.id
                          },  
                          type: "get",  
                          url: interfaceUrl + '/Mapi/DoOpenGoogle?id='+admin_info.id+"&secret="+secret,//{'secret':secret},
                          success: function (data) { 
                              console.log(data); 
                              if (!data.code){
                                layer.msg('服务器发生异常!');
                                  
                              }else{
                                alert('谷歌身份验证设置成功');
                                  location.reload();
                              }
                          }
                      });
                        }else{
                          alert('身份验证失败');
                          location.reload();
                        }
                    },error:function(error){
                        console.log(error);
                    }

            });
      // alert(code);
      // $.get(interfaceUrl + '/Mapi/CheckGoogle?id='+admin_info.id,{'code':code,'secret':secret},function(data){
      //                                 if(data.code == true){
      //                                         $.get(interfaceUrl + '/Mapi/DoOpenGoogle?id='+admin_info.id,{'secret':secret},function(data){
      //                                           alert('谷歌身份验证设置成功');
      //                                           location.reload();
      //                                         });
      //                                 }else{
      //                                     alert('身份验证失败');
      //                                     location.reload();
      //                                 }
      //                         });
      
      
    })

    $('#close').click(function(){
      var code;
      code=prompt("请输入谷歌身份验证码"); 
      // alert(code);
      $.ajax({  
              headers: {  
                  Accept: "application/json; charset=utf-8" ,
                  Authorization: token,
                  ID: admin_info.id
              },  
              type: "get",  
              url: interfaceUrl + '/Mapi/CheckGoogle?id='+admin_info.id+"&code="+code,//,{'code':code}
              success: function (data) { 
                  console.log(data); 
                  if(data.success == 1){
                      $.ajax({  
                          headers: {  
                              Accept: "application/json; charset=utf-8" ,
                              Authorization: token,
                              ID: admin_info.id
                          },  
                          type: "get",  
                          url: interfaceUrl + '/Mapi/DoCloseGoogle?id='+admin_info.id,//,{'code':code}
                          success: function (data) { 
                              console.log(data); 
                              if (!data.code){
                                  alert('谷歌身份验证关闭成功');
                                  location.reload();
                              }
                          }
                      });
                  }else{
                      alert('身份验证失败');
                      location.reload();
                  }
                  // if (!data.code){
                  //     localStorage.clear();
                  //     layer.msg('登录超时，请重新登录！');
                  //     window.location.href = '/login.html';
                  // }
              },error:function(error){
                  console.log(error);
              }

          });
      // $.get(interfaceUrl + '/Mapi/CheckGoogle?id='+admin_info.id,{'code':code},function(data){
      //                                 if(data.success == 1){
      //                                   $.get(interfaceUrl + '/Mapi/DoCloseGoogle?id='+admin_info.id,function(data){
      //                                           alert('谷歌身份验证已关闭');
      //                                           location.reload();
      //                                   });
      //                                 }else{
      //                                     alert('身份验证失败');
      //                                     location.reload();
      //                                 }
      //                         });
      
    })
});