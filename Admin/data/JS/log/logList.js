layui.config({
    base: '/layuiadmin/' //静态资源所在路径
}).extend({
    index: 'lib/index' //主入口模块
}).use(['index', 'table'], function () {
    var $ = layui.$
        , form = layui.form
        , table = layui.table
        , laydate = layui.laydate;

    //监听搜索
    form.on('submit(LAY-user-front-search)', function (data) {
        var field = data.field;
        //执行重载
        table.reload('LAY-user-manage', {
            where: field
        });
    });

    //事件
    var active = {
        batchdel: function () {
            var checkStatus = table.checkStatus('LAY-user-manage')
                , checkData = checkStatus.data; //得到选中的数据
            alert(111);
            if (checkData.length === 0) {
                return layer.msg('请选择数据');
            }

            layer.prompt({
                formType: 1
                , title: '敏感操作，请验证口令'
            }, function (value, index) {
                layer.close(index);


            });
        }
    };

    $('.layui-btn.layuiadmin-btn-useradmin').on('click', function () {
        var type = $(this).data('type');
        active[type] ? active[type].call(this) : '';
    });

    //定义返回格式
    var parseDataFun = function (res) {
        return {
            "code": res.code == 200 ? 0 : -1,
            "msg": res.msg,
            "data": res.data.data,
            "count": res.data.total
        };
    };
    //渲染表格
    table.render({
        elem: '#LAY-user-manage'
        , url: interfaceUrl + '/Mapi/GetLogs?id='+admin_info.id
        ,headers: { "Authorization": admin_info.authtoken,"ID":admin_info.id }
        , parseData: parseDataFun.data
        , method: 'get'
        , xhrFields: {withCredentials: true}
        // , cellMinWidth: 80 //全局定义常规单元格的最小宽度，layui 2.2.1 新增
        , cols: [[
            {field: 'type', width: 300, title: '操作类型',
            templet: function (provider) {
                if(provider.type == 1){
                    return '登录';
                }else if(provider.type == 2){
                    return '修改密码';
                }else if(provider.type == 3){
                    return '退出登录';
                }else if(provider.type == 4){
                    return '补发回调';
                }else if(provider.type == 5){
                    return '开启Google验证';
                }else if(provider.type == 6){
                    return '关闭Google验证';
                }else if(provider.type == 7){
                    return '手动成功';
                }else if(provider.type == 8){
                    return '设置汇率';
                }
            }
        }
            , {field: 'content', title: '关联数据'}
            , {field: 'ip', title: 'IP地址'}
            , {field: 'ctime', title: '操作时间', templet: '<div>{{ layui.laytpl.toDateString(d.ctime*1000) }}</div>'}
        ]]
        , page: true
        , limit: 30
        , limits: [30, 50, 100]
        , done: function (res) {
            if (res.msg == 'Token无效'){
                window.location.href='/login.html';
                return false;
            }
        }
        // ,layout: ['count', 'prev', 'page', 'next', 'limit', 'refresh', 'skip']
        , jump: function (obj, first) {
            if (!first) {
                layer.msg('第 ' + obj.curr + ' 页');
            }
        }
    });
    //检测行级数据
    table.on('tool(LAY-user-manage)', function (obj) { //注：tool是工具条事件名，test是table原始容器的属性 lay-filter="对应的值"
        //删除数据
        
    });

});