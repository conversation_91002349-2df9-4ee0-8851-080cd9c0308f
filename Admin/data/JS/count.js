// 基于准备好的dom，初始化echarts实例
var myChart = echarts.init(document.getElementById('main'));

$(function () {
    // 页面初始化生成验证码
    window.onload = count(null,null);
});

layui.config({
    base: '/layuiadmin/' //静态资源所在路径
}).extend({
    index: 'lib/index' //主入口模块
}).use(['index', 'table'], function () {
    var $ = layui.$
        , form = layui.form
        , table = layui.table
        , laydate = layui.laydate;

    //日期范围
    laydate.render({
        elem: '#test6'
        //设置开始日期、日期日期的 input 选择器
        //数组格式为 2.6.6 开始新增，之前版本直接配置 true 或任意分割字符即可
        , range: ['#test-startDate-1', '#test-endDate-1']
    });

    //监听搜索
    form.on('submit(LAY-user-front-search)', function (data) {
        var field = data.field;
        count(field.start_at,field.end_at);
    });
});

function count(start_at,end_at){
    $.ajax({
        url: interfaceUrl + '/api/adminPortal/transactionFlow/getListByConCount',
        type: 'get',
        data: {
            'start_at' : start_at,
            'end_at' : end_at
        },
        headers: {
            "Authorization": token,
        },
        success: function (res_data) {
            option = {
                title: {
                    text: '折线图堆叠'
                },
                tooltip: {
                    trigger: 'axis'
                },
                legend: {
                    data: ['企业', '代理']
                },
                grid: {
                    left: '3%',
                    right: '4%',
                    bottom: '3%',
                    containLabel: true
                },
                toolbox: {
                    feature: {
                        saveAsImage: {}
                    }
                },
                xAxis: {
                    type: 'category',
                    boundaryGap: false,
                    data: res_data.data.date
                },
                yAxis: {
                    type: 'value'
                },
                series: [
                    {
                        name: '企业',
                        type: 'line',
                        stack: '总量',
                        data: res_data.data.enterprise
                    },
                    {
                        name: '代理',
                        type: 'line',
                        stack: '总量',
                        data: res_data.data.agent
                    },

                ]
            };
            // 使用刚指定的配置项和数据显示图表。
            myChart.setOption(option);
        },
    });
}