layui.config({
    base: '/layuiadmin/' //静态资源所在路径
}).extend({
    index: 'lib/index' //主入口模块
}).use(['index', 'table'], function () {
    var $ = layui.$
        , form = layui.form
        , table = layui.table
        , laydate = layui.laydate;

    var ids = new Array();
    var tableIds = new Array();
    //监听搜索
    form.on('submit(LAY-user-front-search)', function (data) {
        var field = data.field;
        //执行重载
        table.reload('LAY-user-manage', {
            where: field
        });
    });

    //事件
    var active = {
        batchdel: function () {
            var checkStatus = table.checkStatus('LAY-user-manage')
                , checkData = checkStatus.data; //得到选中的数据
            alert(111);
            if (checkData.length === 0) {
                return layer.msg('请选择数据');
            }

            layer.prompt({
                formType: 1
                , title: '敏感操作，请验证口令'
            }, function (value, index) {
                layer.close(index);


            });
        }

        , col: function () {
            var str = ids.join(',');
            if (str === ''){
                layer.msg('您没有选中任何数据!');
                return false
            }
            var formData = new FormData();
            formData.append('ids', str);
            $.ajax({
                type: 'POST',
                url: interfaceUrl + '/Address/DoCollection',
                beforeSend: function (xhr) {
                    xhr.setRequestHeader("Authorization", token);
                },
                data: formData, // 使用 FormData 对象
                processData: false,
                contentType: false,
                success: function (res_data) {
                    if(res_data.code == true){
                        layer.msg(res_data.msg);
                        setTimeout(function() {
                            window.location.reload();
                        }, 1000);

                    }else{
                        layer.msg(res_data.msg);
                    }
                },
            });
        }
    };

    // 监听勾选事件
    table.on('checkbox(LAY-user-manage)', function (obj) {
        if (obj.checked == true) {
            if (obj.type == 'one') {
                ids.push(obj.data.id);
            } else {
                for (let i = 0; i < tableIds.length; i++) {
                    //当全选之前选中了部分行进行判断，避免重复
                    if (ids.indexOf(tableIds[i]) == -1) {
                        ids.push(tableIds[i]);
                    }
                }
            }
        } else {
            if (obj.type == 'one') {
                let i = ids.length;
                while (i--) {
                    if (ids[i] == obj.data.id) {
                        ids.splice(i, 1);
                    }
                }
            } else {
                let i = ids.length;
                while (i--) {
                    if (tableIds.indexOf(ids[i]) != -1) {
                        ids.splice(i, 1);
                    }
                }
            }
        }
    });
    $('.layui-btn.layuiadmin-btn-useradmin').on('click', function () {
        var type = $(this).data('type');
        active[type] ? active[type].call(this) : '';
    });

    //定义返回格式
    var parseDataFun = function (res) {
        return {
            "code": res.code == 200,
            "message": res.msg,
            "data": res.data.data,
        };
    };
    //渲染表格
    table.render({
        elem: '#LAY-user-manage'
        , url: interfaceUrl + '/Address/GetAddressList'
        , headers: { "Authorization": token}
        , parseData: parseDataFun.data
        , method: 'post'
        , xhrFields: {withCredentials: true}
        // , cellMinWidth: 80 //全局定义常规单元格的最小宽度，layui 2.2.1 新增
        , cols: [[
            {type: 'checkbox'}
            , {field: 'trxaddr',  title: 'Trc地址',width:400}
            , {field: 'ethaddr', title: 'Erc地址',width:400}
            , {field: 'trx', title: 'TRX余额'}
            , {field: 'usdt_trc', title: 'USDT-Trc'}
            , {field: 'eth', title: 'ETH余额'}
            , {field: 'usdt_erc', title: 'USDT-Erc'}
            , {field: 'merchant_name', title: '所属商户'}
            , {field: 'add', title: '操作', width: 200, toolbar: "#table-useradmin-webuser"}
        ]]
        , page: true
        , limit: 30
        , limits: [30, 50, 100]
        , done: function (res) {
            if (res.msg == 'Token无效'){
                window.location.href='/login.html';
                return false;
            }
            // 设置当前页全部数据id到全局变量
            tableIds = res.data.map(function (value) {
                return value.id;
            });
            // 设置当前页选中项
            $.each(res.data, function (idx, val) {
                if (ids.indexOf(val.id) > -1) {
                    val["LAY_CHECKED"] = 'true';
                    //找到对应数据改变勾选样式，呈现出选中效果
                    let index = val['LAY_TABLE_INDEX'];
                    $('tr[data-index=' + index + '] input[type="checkbox"]').click();
                    form.render('checkbox'); //刷新checkbox选择框渲染
                }
            });
            // 获取表格勾选状态，全选中时设置全选框选中
            let checkStatus = table.checkStatus('test');
            if (checkStatus.isAll) {
                $('.layui-table-header th[data-field="0"] input[type="checkbox"]').prop('checked', true);
                form.render('checkbox'); //刷新checkbox选择框渲染
            }
        }
        // ,layout: ['count', 'prev', 'page', 'next', 'limit', 'refresh', 'skip']
        , jump: function (obj, first) {
            if (!first) {
                layer.msg('第 ' + obj.curr + ' 页');
            }
        }
    });
    //检测行级数据
    table.on('tool(LAY-user-manage)', function (obj) { //注：tool是工具条事件名，test是table原始容器的属性 lay-filter="对应的值"
        //删除数据
        if (obj.event == 'del') {
            layer.confirm('确定删除此地址吗？', function (index) {
                var formData = new FormData();
                formData.append('id', obj.data.id);
                $.ajax({
                    type: 'POST',
                    url: interfaceUrl + '/Address/DeleteAddress',
                    beforeSend: function (xhr) {
                        xhr.setRequestHeader("Authorization", token);
                    },
                    data: formData, // 使用 FormData 对象
                    processData: false,
                    contentType: false,
                    success: function (res_data) {
                        if(res_data.code == true){
                            layer.msg(res_data.msg);
                            table.reload('LAY-user-manage');
                        }else{
                            layer.msg(res_data.msg);
                        }
                    },
                });
            });
        }
    });

});