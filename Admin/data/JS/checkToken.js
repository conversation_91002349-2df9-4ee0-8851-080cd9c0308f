//登录token
var token = localStorage.getItem("Authorization")
// localStorage.removeItem("Authorization");
//登录时间
var time_src = localStorage.getItem("time_src");
//登录信息
var admin_info = JSON.parse(localStorage.getItem("admin_info"));

//token不存在，回到登陆页面
if(token == null){
    window.location.href = '/login.html';
}
//登陆后10分钟无操作，回到登陆页面
var new_time = (time_src*1+60*1000*60);
var date = new Date();
if(new_time <= date.getTime()){
    // alert('长时间无操作，请重新登录！');
    window.location.href = '/login.html';
}


var formData = new FormData();
formData.append('id', 1);
$.ajax({
    type: 'POST',
    url: interfaceUrl + '/Merchant/GetMerchantInfo',
    beforeSend: function (xhr) {
        xhr.setRequestHeader("Authorization", token);
    },
    data: formData, // 使用 FormData 对象
    processData: false,
    contentType: false,
    success: function (res_data) {
        if(res_data.code == true){
            return false;
        }else{
            if (res_data.msg == 'Token无效'){
                window.location.href='/login.html';
                return false;
            }
        }
    },
});