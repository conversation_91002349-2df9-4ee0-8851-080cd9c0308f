$(document).ready(function () {
    $('.i-checks').iCheck({
        radioClass: 'iradio_square-green',
        checkboxClass: 'icheckbox_square-green',
    });
    $.ajax({
        type: "GET", //提交方式
        url: url + "/api/adminPortal/taskTypeType/getListByCon",//路径
        beforeSend: function (xhr) {
            xhr.setRequestHeader("Authorization", Authorization);
        },
        data: {
            'status':1,
            'is_paginate':'false',
        },//数据，这里使用的是Json格式进行传输
        dataType: "json",
        async: false,
        success: function (result) {//返回数据根据结果进行相应的处理
            if (result.code != 200) {
                swal("失败", result.msg, "error");
                setTimeout(function () {
                    $(location).attr("href", "index.html");
                }, 1000);
            }
            var str = "";
            for(var i=0;i<result.data.length;i++){
                str += "<option value='"+result.data[i].id+"'>"+result.data[i].new_name+"</option>"
            }
            $("#type").append(str);
        },
        error: function (res) {

        }
    });
    var id = getUrlParam("id");
    var option_checked
    if (id > 0) {
        $.ajax({
            type: "GET", //提交方式
            url: url + "/api/adminPortal/taskType/getById",//路径
            beforeSend: function (xhr) {
                xhr.setRequestHeader("Authorization", Authorization);
            },
            data: {
                "task_type_id": id
            },//数据，这里使用的是Json格式进行传输
            dataType: "json",
            async: false,
            success: function (result) {//返回数据根据结果进行相应的处理
                if (result.code != 200) {
                    swal("失败", result.msg, "error");
                    setTimeout(function () {
                        $(location).attr("href", "index.html");
                    }, 1000);
                }
                $("#name").val(result.data.name);
                $("#price").val(result.data.price);
                var str = "";
                for(var i=0;i<result.data.task_type.length;i++){
                    if(result.data.task_type[i].id == result.data.type){
                        str += "<option value='"+result.data.task_type[i].id+"' selected>"+result.data.task_type[i].new_name+"</option>"
                    }else{
                        str += "<option value='"+result.data.task_type[i].id+"'>"+result.data.task_type[i].new_name+"</option>"
                    }
                    $("#type").append(str);
                }
            },
            error: function (res) {

            }
        });
    } else {
        $("#title").html("修改类型");
    }
});

function getUrlParam(name) {
    var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)"); //构造一个含有目标参数的正则表达式对象
    var r = window.location.search.substr(1).match(reg);  //匹配目标参数
    if (r != null) return unescape(r[2]);
    return null; //返回参数值
}


function validform() {
    var icon = "<i class='fa fa-times-circle'></i> ";
    /*关键在此增加了一个return，返回的是一个validate对象，这个对象有一个form方法，返回的是是否通过验证*/
    return $("#signupForm").validate({
        rules: {
            name: "required",
            price: "required",
        },
        messages: {
            name: icon + "请输入类型名称",
            price: icon + "请输入每次提交扣除积分",
        }
    });
}

function action_info() {
    if (validform().form()) {
        var id = getUrlParam("id");
        var name = $("#name").val();
        var price = $("#price").val();
        var type = $("#type").val();
        var values = [];
        $('input[name="name_son[]"]').each(function () {
            values.push($(this).val());
        });
        var type_json = Array();
        nums_arr.reverse()
        for(var i = 0;i<=values.length;i++){
            var data = {}
            data.name = values[i]
            data.type = $("input[name='type"+nums_arr[i]+"']:checked").val();
            type_json.push(data);
        }
        var urls;
        var msg;
        if (id > 0) {
            urls = url + "/api/adminPortal/taskType/edit";
            msg = "修改";
        } else {
            urls = url + "/api/adminPortal/taskType/create";
            msg = "添加";
        }
        $.ajax({
            type: "POST", //提交方式
            url: urls,//路径
            beforeSend: function (xhr) {
                xhr.setRequestHeader("Authorization", Authorization);
            },
            data: {
                "task_type_id": id,
                "name": name,
                "price": price,
                "task_json" : type_json,
                "type" : type
            },//数据，这里使用的是Json格式进行传输
            dataType: "json",
            success: function (result) {//返回数据根据结果进行相应的处理
                if (result.code == 200) {
                    swal("成功", result.msg, "success");
                    setTimeout(function () {
                        $(location).attr("href", "index.html");
                    }, 1000);
                } else {
                    swal("请求失败", result.msg, "error");
                }
            },
            error: function (res) {

            }
        });
    } else {
        swal("失败", '请按照规定填写内容', "error");
    }
}

//添加字段
var num = 0;
var nums_arr = Array();

function add_attribute() {
    num++;
    nums_arr.push(num);
    var str = "    <div class=\"form-group\">\n" +
        "                            <label class=\"col-sm-3 control-label\">名称：</label>\n" +
        "                            <div class=\"col-sm-3\" style=\"width: 200px;\">\n" +
        "                                <input  name=\"name_son[]\" class=\"form-control\"\n" +
        "                                       type=\"text\">\n" +
        "                            </div>\n" +
        "\n" +
        "                            <label class=\"col-sm-1 control-label\">类型：</label>\n" +
        "                            <div class=\"radio i-checks\" style=\"width: 150px;float:left;\">\n" +
        "                                <label class=\"\">\n" +
        "                                    <div class=\"iradio_square-green \" style=\"position: relative;\">\n" +
        "                                        <input type=\"radio\" value=\"world\" name=\"type" + num + "\" style=\"position: absolute; opacity: 0;\" checked>\n" +
        "                                        <ins class=\"iCheck-helper\"\n" +
        "                                             style=\"position: absolute; top: 0%; left: 0%; display: block; width: 100%; height: 100%; margin: 0px; padding: 0px; background: rgb(255, 255, 255); border: 0px; opacity: 0;\"></ins>\n" +
        "                                    </div>\n" +
        "                                    <i></i> 文字 </label>\n" +
        "                                <label class=\"\">\n" +
        "                                    <div class=\"iradio_square-green\" style=\"position: relative;\">\n" +
        "                                        <input type=\"radio\" value=\"pic\" name=\"type" + num + "\" style=\"position: absolute; opacity: 0;\">\n" +
        "                                        <ins class=\"iCheck-helper\"\n" +   "                                             style=\"position: absolute; top: 0%; left: 0%; display: block; width: 100%; height: 100%; margin: 0px; padding: 0px; background: rgb(255, 255, 255); border: 0px; opacity: 0;\"></ins>\n" +
        "                                    </div>\n" +
        "                                    <i></i> 图片 </label>\n" +
        "                            </div>\n" +
        "\n" +
        "                            <div class=\"col-sm-1\">\n" +
        "                                <button class=\"btn btn-primary\" type=\"button\" wxh=\"" + num + "\" onclick=\"del_attribute(this)\">删除字段\n" +
        "                                </button>\n" +
        "                            </div>\n" +
        "                        </div>"
    $("#add_attribute").after(str);
    $('.i-checks').iCheck({
        radioClass: 'iradio_square-green',
        checkboxClass: 'icheckbox_square-green',
    });
}

function del_attribute(obj) {
    var del_num = Number($(obj).attr('wxh'));
    var index = nums_arr.indexOf(del_num);
    if (index > -1) {
        nums_arr.splice(index, 1);
    }
    $(obj).parent().parent().remove();
}