layui.config({
    base: '/layuiadmin/' //静态资源所在路径
}).extend({
    index: 'lib/index' //主入口模块
}).use(['index', 'table', 'admin'], function () {
    var $ = layui.$
        , form = layui.form
        , table = layui.table
        , admin = layui.admin
        , laydate = layui.laydate;

    //监听搜索
    form.on('submit(LAY-user-front-search)', function (data) {
        var field = data.field;
        //执行重载
        table.reload('LAY-user-manage', {
            where: field
        });
    });

    //事件
    var active = {
        batchdel: function () {
            var checkStatus = table.checkStatus('LAY-user-manage')
                , checkData = checkStatus.data; //得到选中的数据
            alert(111);
            if (checkData.length === 0) {
                return layer.msg('请选择数据');
            }

            layer.prompt({
                formType: 1
                , title: '敏感操作，请验证口令'
            }, function (value, index) {
                layer.close(index);


            });
        }
    };

    $('.layui-btn.layuiadmin-btn-useradmin').on('click', function () {
        var type = $(this).data('type');
        active[type] ? active[type].call(this) : '';
    });

    //定义返回格式
    var parseDataFun = function (res) {
        return {
            "code": res.code == 200,
            "message": res.msg,
            "data": res.data.data,
        };
    };
    //渲染表格
    table.render({
        elem: '#LAY-user-manage'
        , url: interfaceUrl + '/Order/GetOrderList'
        , headers: { "Authorization": token}
        , parseData: parseDataFun.data
        , method: 'post'
        , xhrFields: {withCredentials: true}
        // , cellMinWidth: 80 //全局定义常规单元格的最小宽度，layui 2.2.1 新增
        , cols: [[
            {field: 'orderid',  title: '系统订单号'}
            , {field: 'rorderid', title: '商户订单号'}
            , {field: 'merchant_name', title: '商户名', width: 100}
            , {field: 'money', title: '充值金额', width: 100}
            , {field: 'realmoney', title: '实收金额', width: 100}
            // , {field: 'trxadd', title: '收款地址', width: 330}
            , {field: 'status', title: '状态',
                templet: function (provider) {
                    if(provider.status == 0){
                        if(provider.realmoney != null && provider.transactionid != null){
                            return '入账待处理';
                        }else{
                            return '创建订单';
                        }
                    }else if(provider.status == 1){
                        return '订单超时';
                    }else if(provider.status == 2){
                        return '支付成功';
                    }else if(provider.status == 3){
                        return '支付成功';
                    }else if(provider.status == 4){
                        return '手动成功';
                    }else if(provider.status == 5){
                        return '已取消';
                    }
                }
                , width: 100}
            , {field: 'status', title: '回调状态',
                templet: function (provider) {
                    if(provider.status == 3){
                        return '回调成功';
                    }else if(provider.status == 2 && provider.callbacknum >= 5){
                        return '回调失败';
                    }else if(provider.status == 2 && provider.callbacknum < 5){
                        return '回调中';
                    }else{
                        return '未回调';
                    }
                }
                , width: 100}
            , {field: 'callbacknum', title: '回调次数', width: 100}
            , {field: 'ctime', title: '下单时间', templet: '<div>{{ layui.laytpl.toDateString(d.ctime*1000) }}</div>', width: 180}
            // , {field: 'etime', title: '完成时间', templet: '<div>{{ layui.laytpl.toDateString(d.etime*1000) }}</div>'}
            , {field: 'add', title: '操作', width: 330, toolbar: "#table-useradmin-webuser"}
        ]]
        , page: true
        , limit: 30
        , limits: [30, 50, 100]
        , done: function (res) {
            console.log(res);
            if (res.msg == 'Token无效'){
                console.log(111);
                window.location.href='/login.html';
            }
        }
        // ,layout: ['count', 'prev', 'page', 'next', 'limit', 'refresh', 'skip']
        , jump: function (obj, first) {
            if (!first) {
                layer.msg('第 ' + obj.curr + ' 页');
            }
        }
    });
    //检测行级数据
    table.on('tool(LAY-user-manage)', function (obj) { //注：tool是工具条事件名，test是table原始容器的属性 lay-filter="对应的值"


        if (obj.event == 'success') {
            layer.prompt({
                title: '请输入实收金额',
                value: obj.data.realmoney
            }, function (value, index) {
                var formData = new FormData();
                formData.append('id', obj.data.id);
                formData.append('status', 2);
                formData.append('realmoney', value);
                $.ajax({
                    type: 'POST',
                    url: interfaceUrl + '/Order/UpdateOrder',
                    beforeSend: function (xhr) {
                        xhr.setRequestHeader("Authorization", token);
                    },
                    data: formData, // 使用 FormData 对象
                    processData: false,
                    contentType: false,
                    success: function (res_data) {
                        if(res_data.code == true){
                            layer.msg(res_data.msg);
                            table.reload('LAY-user-manage');
                        }else{
                            layer.msg(res_data.msg);
                        }
                    },
                });
                layer.close(index);  // 关闭输入框
            });
        }

        if (obj.event == 'new') {
            layer.prompt({
                title: '请注意,空单会直接上分并回调,如操作无误请输入金额'
            }, function (value, index) {
                var formData = new FormData();
                formData.append('id', obj.data.id);
                formData.append('money', value);
                $.ajax({
                    type: 'POST',
                    url: interfaceUrl + '/Order/CreateEmptyOrder',
                    beforeSend: function (xhr) {
                        xhr.setRequestHeader("Authorization", token);
                    },
                    data: formData, // 使用 FormData 对象
                    processData: false,
                    contentType: false,
                    success: function (res_data) {
                        if(res_data.code == true){
                            layer.msg(res_data.msg);
                            table.reload('LAY-user-manage');
                        }else{
                            layer.msg(res_data.msg);
                        }
                    },
                });
                layer.close(index);  // 关闭输入框
            });
        }

        if (obj.event == 'cancel') {
            layer.confirm('确定取消此订单吗？', function (index) {
                var formData = new FormData();
                formData.append('id', obj.data.id);
                formData.append('status', 5);
                $.ajax({
                    type: 'POST',
                    url: interfaceUrl + '/Order/UpdateOrder',
                    beforeSend: function (xhr) {
                        xhr.setRequestHeader("Authorization", token);
                    },
                    data: formData, // 使用 FormData 对象
                    processData: false,
                    contentType: false,
                    success: function (res_data) {
                        if(res_data.code == true){
                            layer.msg(res_data.msg);
                            table.reload('LAY-user-manage');
                        }else{
                            layer.msg(res_data.msg);
                        }
                    },
                });
            });
        }

        if (obj.event == 'reissue') {
            layer.confirm('确定补发回调吗？', function (index) {
                var formData = new FormData();
                formData.append('id', obj.data.id);
                $.ajax({
                    type: 'POST',
                    url: interfaceUrl + '/Order/DoCallbackOrder',
                    beforeSend: function (xhr) {
                        xhr.setRequestHeader("Authorization", token);
                    },
                    data: formData, // 使用 FormData 对象
                    processData: false,
                    contentType: false,
                    success: function (res_data) {
                        if(res_data.code == true){
                            layer.msg(res_data.msg);
                            table.reload('LAY-user-manage');
                        }else{
                            layer.msg(res_data.msg);
                        }
                    },
                });
            });
        }
        
        if (obj.event == 'web') {
            if (obj.data.chain == 'Trc20'){
                window.open('https://tronscan.org/#/transaction/'+obj.data.transactionid)
            }else if (obj.data.chain == 'Erc20'){
                window.open('https://etherscan.io/tx/'+obj.data.transactionid)
            }else{
                window.open('https://bscscan.com/tx/'+obj.data.transactionid)
            }

        }

        if(obj.event === 'detail'){ // 如果点击的是"详情"按钮
            window.location.href = '/order/detail.html?id=' + obj.data.id;
        }
    });

});