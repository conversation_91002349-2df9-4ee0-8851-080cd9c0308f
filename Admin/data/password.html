

<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <title>设置我的密码</title>
  <meta name="renderer" content="webkit">
  <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
  <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
  <link rel="stylesheet" href="/layuiadmin/layui/css/layui.css" media="all">
  <link rel="stylesheet" href="/layuiadmin/style/admin.css" media="all">
</head>
<body>

  <div class="layui-fluid">
    <div class="layui-row layui-col-space15">
      <div class="layui-col-md12">
        <div class="layui-card">
          <div class="layui-card-header">修改密码</div>
          <div class="layui-card-body" pad15>
            
            <div class="layui-form" lay-filter="">
              <div class="layui-form-item">
                <label class="layui-form-label">当前密码</label>
                <div class="layui-input-inline">
                  <input type="password" name="oldPassword" lay-verify="required" lay-verType="tips" id="oldpassword" class="layui-input">
                </div>
              </div>
              <div class="layui-form-item">
                <label class="layui-form-label">新密码</label>
                <div class="layui-input-inline">
                  <input type="password" name="password"  lay-verType="tips" autocomplete="off" id="LAY_password" class="layui-input">
                </div>
                <!-- <div class="layui-form-mid layui-word-aux">6到16个字符</div> -->
              </div>
              <div class="layui-form-item">
                <label class="layui-form-label">确认新密码</label>
                <div class="layui-input-inline">
                  <input type="password" name="repassword" lay-verify="repass" lay-verType="tips" id="repassword" autocomplete="off" class="layui-input">
                </div>
              </div>
              <div class="layui-form-item">
                <div class="layui-input-block">
                  <button class="layui-btn" lay-submit id="tijiao" lay-filter="setmypass">确认修改</button>
                </div>
              </div>
            </div>
            
          </div>
        </div>
      </div>
    </div>
  </div>

  <script src="/layuiadmin/layui/layui.js"></script>
  <script src="/JS/public.js"></script>

  <script type="text/javascript">

  </script>
  <script>
  layui.config({
    base: '/layuiadmin/' //静态资源所在路径
  }).extend({
    index: 'lib/index' //主入口模块
  }).use(['index', 'set' , 'jquery'] , function(){
      var $ = jQuery = layui.$;
      $('#tijiao').click(function(){
        var formData = new FormData();
        formData.append('oldpwd', $('#oldpassword').val());
        formData.append('newpwd', $('#LAY_password').val());
        formData.append('confirmpwd', $('#repassword').val());
        $.ajax({
          type: 'POST',
          url: interfaceUrl + '/Admin/ResetPassword',
          beforeSend: function (xhr) {
            xhr.setRequestHeader("Authorization", token);
          },
          data: formData, // 使用 FormData 对象
          processData: false,
          contentType: false,
          success: function (data) {
            if (data.code == true) {
              layer.msg('修改成功');
              setTimeout(function() {
                location.reload(); // 刷新当前页面
              }, 1000); // 1000 毫秒后执行
            } else {
              layer.msg(data.msg);
            }
          },
        });
        return false;
      })
      
  });
  </script>
</body>
</html>