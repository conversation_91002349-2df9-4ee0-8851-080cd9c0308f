layui.define("form",function(e){var s=layui.$,t=(layui.layer,layui.laytpl,layui.setter,layui.view,layui.admin),i=layui.form,a=s("body");i.verify({nickname:function(e,s){return new RegExp("^[a-zA-Z0-9_\u4e00-\u9fa5\\s\xb7]+$").test(e)?/(^\_)|(\__)|(\_+$)/.test(e)?"\u7528\u6237\u540d\u9996\u5c3e\u4e0d\u80fd\u51fa\u73b0\u4e0b\u5212\u7ebf'_'":/^\d+\d+\d$/.test(e)?"\u7528\u6237\u540d\u4e0d\u80fd\u5168\u4e3a\u6570\u5b57":void 0:"\u7528\u6237\u540d\u4e0d\u80fd\u6709\u7279\u6b8a\u5b57\u7b26"},pass:[/^[\S]{6,12}$/,"\u5bc6\u7801\u5fc5\u987b6\u523012\u4f4d\uff0c\u4e14\u4e0d\u80fd\u51fa\u73b0\u7a7a\u683c"]}),t.sendAuthCode({elem:"#LAY-user-getsmscode",elemPhone:"#LAY-user-login-cellphone",elemVercode:"#LAY-user-login-vercode",ajax:{url:layui.setter.base+"json/user/sms.js"}}),a.on("click","#LAY-user-get-vercode",function(){s(this);this.src="https://www.oschina.net/action/user/captcha?t="+(new Date).getTime()}),e("user",{})});