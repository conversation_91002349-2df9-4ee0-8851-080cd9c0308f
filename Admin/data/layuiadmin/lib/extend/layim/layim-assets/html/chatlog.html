 
 
<!DOCTYPE html>
<html>
<head>
<meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
<title>聊天记录</title>

<link rel="stylesheet" href="//unpkg.com/layui/dist/css/layui.css">
<style>
body .layim-chat-main{height: auto;}
</style>
</head>
<body>

<div class="layim-chat-main">
  <ul id="LAY_view"></ul>
</div>

<div id="LAY_page" style="margin: 0 10px;"></div>


<textarea title="消息模版" id="LAY_tpl" style="display:none;">
{{# layui.each(d.data, function(index, item){
  if(item.id == layui.layim.cache().mine.id){ }}
    <li class="layim-chat-mine"><div class="layim-chat-user"><img src="{{ item.avatar }}"><cite><i>{{ layui.data.date(item.timestamp) }}</i>{{ item.username }}</cite></div><div class="layim-chat-text">{{ layui.layim.content(item.content) }}</div></li>
  {{# } else { }}
    <li><div class="layim-chat-user"><img src="{{ item.avatar }}"><cite>{{ item.username }}<i>{{ layui.data.date(item.timestamp) }}</i></cite></div><div class="layim-chat-text">{{ layui.layim.content(item.content) }}</div></li>
  {{# }
}); }}
</textarea>

<!-- 
上述模版采用了 laytpl 语法

-->


<script src="//unpkg.com/layui/dist/layui.js"></script>
<script>
layui.link('../layim.css', 'skinlayimcss') //加载 css
layui.config({
  layimPath: '../../' //配置 layim.js 所在目录
  ,layimAssetsPath: '../../layim-assets/' //layim 资源文件所在目录
}).use(['jquery'], function(){
  var layim = parent.layui.layim
  ,laytpl = parent.layui.laytpl
  ,$ = layui.jquery
  ,laypage = parent.layui.laypage;
  
  //聊天记录的分页此处不做演示，你可以采用 laypage
  
  //开始请求聊天记录
  var param =  location.search //获得URL参数。该窗口url会携带会话id和type，他们是你请求聊天记录的重要凭据
  
  //实际使用时，下述的res一般是通过 Ajax 获得，而此处仅仅只是演示数据格式
  ,res = {
    code: 0
    ,msg: ''
    ,data: [{
      username: '纸飞机'
      ,id: 100000
      ,avatar: '' || layui.cache.layimAssetsPath + 'images/default.png'
      ,timestamp: 1480897882000
      ,content: '我方模拟记录111'
    }, {
      username: 'test123'
      ,id: 108101
      ,avatar: '' || layui.cache.layimAssetsPath + 'images/default.png'
      ,timestamp: 1480897892000
      ,content: '对方模拟记录111'
    },{
      username: 'test123'
      ,id: 108101
      ,avatar: '' || layui.cache.layimAssetsPath + 'images/default.png'
      ,timestamp: 1480897898000
      ,content: '对方模拟记录222'
    },{
      username: 'test123'
      ,id: 108101
      ,avatar: '' || layui.cache.layimAssetsPath + 'images/default.png'
      ,timestamp: 1480897908000
      ,content: '注意：这些都是模拟数据，实际使用时，需将其中的模拟接口改为你的项目真实接口。\n该模版文件所在目录（相对于layim.js）：\n/layim-assets/html/chatlog.html'
    }]
  }
  
  //console.log(param)
  
  var html = laytpl(LAY_tpl.value).render({
    data: res.data
  });
  $('#LAY_view').html(html);
  
});
</script>
</body>
</html>
