<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>地址列表</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport"
          content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
    <link rel="stylesheet" href="/layuiadmin/layui/css/layui.css" media="all">
    <link rel="stylesheet" href="/layuiadmin/style/admin.css" media="all">
</head>
<body>

<div class="layui-fluid">
    <div class="layui-card">
        <div class="layui-form layui-card-header layuiadmin-card-header-auto">
            <div class="layui-form-item">
              <div class="layui-inline">
                <label class="layui-form-label">角色名</label>
                <div class="layui-input-block">
                  <input type="text" name="name" id="name" placeholder="请输入" autocomplete="off" class="layui-input">
                </div>
              </div>
              <!--<div class="layui-inline">-->
              <!--      <label class="layui-form-label">Hash</label>-->
              <!--      <div class="layui-input-block">-->
              <!--        <input type="text" name="hash" id="hash" placeholder="请输入" autocomplete="off" class="layui-input">-->
              <!--      </div>-->
              <!--    </div>-->
                  <div class="layui-inline">
                    <button class="layui-btn layuiadmin-btn-useradmin" lay-submit lay-filter="LAY-user-front-search">
                      <i class="layui-icon layui-icon-search layuiadmin-button-btn"></i>
                    </button>
                  </div>
            </div>
        </div>

        <div class="layui-card-body">

            <table id="LAY-user-manage" lay-filter="LAY-user-manage"></table>
            <script type="text/html" id="table-useradmin-webuser">
                <a class="layui-btn layui-btn-info layui-btn-xs" lay-event="per"></i>设置权限</a>
                <a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del"></i>删除角色</a>
          </script>
        </div>
    </div>
</div>
<script src='/JS/jquery-3.5.1.min.js'></script>
<script src="/layuiadmin/layui/layui.js"></script>
<script src="/JS/public.js"></script>
<script src="/JS/role/roleList.js"></script>
</body>
</html>
