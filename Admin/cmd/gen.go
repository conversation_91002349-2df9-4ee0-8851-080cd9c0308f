package main

import (
	"Admin/config"
	"Admin/dal"

	"go.uber.org/fx"
	"go.uber.org/zap"
	"gorm.io/gen"
	"gorm.io/gorm"
)

// generate code
func main() {
	var DB *gorm.DB
	_ = fx.New(
		fx.Provide(zap.NewDevelopment),
		fx.Provide(config.NewConfig),
		fx.Provide(dal.NewGormDB),
		fx.Populate(&DB),
	)
	// specify the output directory (default: "./query")
	// ### if you want to query without context constrain, set mode gen.WithoutContext ###
	g := gen.NewGenerator(gen.Config{
		Mode:         gen.WithoutContext | gen.WithDefaultQuery,
		OutPath:      "./dal",
		ModelPkgPath: "./model/entity",
		// Mode:         gen.WithoutContext,
		// if you want the nullable field generation property to be pointer type, set FieldNullable true
		FieldNullable:     true,
		FieldWithIndexTag: true,
		FieldWithTypeTag:  true,
	})

	// reuse the database connection in Project or create a connection here
	// if you want to use GenerateModel/GenerateModelAs, UseDB is necessary, or it will panic
	g.UseDB(DB)

	// apply basic crud api on structs or table models which is specified by table name with function
	// GenerateModel/GenerateModelAs. And generator will generate table models' code when calling Excute.
	// user := g.GenerateModel("user", gen.FieldRelate(field.Many2Many, "Channels", g.GenerateModel("queue"),
	// 	&field.RelateConfig{
	// 		RelateSlice: true,
	// 		GORMTag:     "many2many:channel_users",
	// 	}))
	// queue := g.GenerateModel("queue", gen.FieldRelate(field.Many2Many, "Users", user, &field.RelateConfig{
	// 	RelateSlice: true,
	// 	GORMTag:     "many2many:channel_users",
	// }))
	g.ApplyBasic(g.GenerateAllTable()...)

	// apply diy interfaces on structs or table models
	// g.ApplyInterface(lib(method model.Method) {}, model.User{}, g.GenerateModel("company"))

	// execute the action of code generation
	g.Execute()
}
