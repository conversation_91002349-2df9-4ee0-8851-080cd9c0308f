// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package dal

import (
	"context"
	"database/sql"

	"gorm.io/gorm"

	"gorm.io/gen"

	"gorm.io/plugin/dbresolver"
)

var (
	Q               = new(Query)
	Address         *address
	AddressCopy2    *addressCopy2
	Admin           *admin
	AdminRole       *adminRole
	Adminslog       *adminslog
	Agent           *agent
	AgentTran       *agentTran
	Balance         *balance
	Bscorder        *bscorder
	Cfg             *cfg
	Ercorder        *ercorder
	Lease           *lease
	Lockaddress     *lockaddress
	Merchant        *merchant
	Merchantaddress *merchantaddress
	Merchantlog     *merchantlog
	Order           *order
	Payorder        *payorder
	Permission      *permission
	Role            *role
	RolePermission  *rolePermission
	Settlement      *settlement
	Tran            *tran
	Trcorder        *trcorder
	Uorder          *uorder
	Whitelist       *whitelist
)

func SetDefault(db *gorm.DB, opts ...gen.DOOption) {
	*Q = *Use(db, opts...)
	Address = &Q.Address
	AddressCopy2 = &Q.AddressCopy2
	Admin = &Q.Admin
	AdminRole = &Q.AdminRole
	Adminslog = &Q.Adminslog
	Agent = &Q.Agent
	AgentTran = &Q.AgentTran
	Balance = &Q.Balance
	Bscorder = &Q.Bscorder
	Cfg = &Q.Cfg
	Ercorder = &Q.Ercorder
	Lease = &Q.Lease
	Lockaddress = &Q.Lockaddress
	Merchant = &Q.Merchant
	Merchantaddress = &Q.Merchantaddress
	Merchantlog = &Q.Merchantlog
	Order = &Q.Order
	Payorder = &Q.Payorder
	Permission = &Q.Permission
	Role = &Q.Role
	RolePermission = &Q.RolePermission
	Settlement = &Q.Settlement
	Tran = &Q.Tran
	Trcorder = &Q.Trcorder
	Uorder = &Q.Uorder
	Whitelist = &Q.Whitelist
}

func Use(db *gorm.DB, opts ...gen.DOOption) *Query {
	return &Query{
		db:              db,
		Address:         newAddress(db, opts...),
		AddressCopy2:    newAddressCopy2(db, opts...),
		Admin:           newAdmin(db, opts...),
		AdminRole:       newAdminRole(db, opts...),
		Adminslog:       newAdminslog(db, opts...),
		Agent:           newAgent(db, opts...),
		AgentTran:       newAgentTran(db, opts...),
		Balance:         newBalance(db, opts...),
		Bscorder:        newBscorder(db, opts...),
		Cfg:             newCfg(db, opts...),
		Ercorder:        newErcorder(db, opts...),
		Lease:           newLease(db, opts...),
		Lockaddress:     newLockaddress(db, opts...),
		Merchant:        newMerchant(db, opts...),
		Merchantaddress: newMerchantaddress(db, opts...),
		Merchantlog:     newMerchantlog(db, opts...),
		Order:           newOrder(db, opts...),
		Payorder:        newPayorder(db, opts...),
		Permission:      newPermission(db, opts...),
		Role:            newRole(db, opts...),
		RolePermission:  newRolePermission(db, opts...),
		Settlement:      newSettlement(db, opts...),
		Tran:            newTran(db, opts...),
		Trcorder:        newTrcorder(db, opts...),
		Uorder:          newUorder(db, opts...),
		Whitelist:       newWhitelist(db, opts...),
	}
}

type Query struct {
	db *gorm.DB

	Address         address
	AddressCopy2    addressCopy2
	Admin           admin
	AdminRole       adminRole
	Adminslog       adminslog
	Agent           agent
	AgentTran       agentTran
	Balance         balance
	Bscorder        bscorder
	Cfg             cfg
	Ercorder        ercorder
	Lease           lease
	Lockaddress     lockaddress
	Merchant        merchant
	Merchantaddress merchantaddress
	Merchantlog     merchantlog
	Order           order
	Payorder        payorder
	Permission      permission
	Role            role
	RolePermission  rolePermission
	Settlement      settlement
	Tran            tran
	Trcorder        trcorder
	Uorder          uorder
	Whitelist       whitelist
}

func (q *Query) Available() bool { return q.db != nil }

func (q *Query) clone(db *gorm.DB) *Query {
	return &Query{
		db:              db,
		Address:         q.Address.clone(db),
		AddressCopy2:    q.AddressCopy2.clone(db),
		Admin:           q.Admin.clone(db),
		AdminRole:       q.AdminRole.clone(db),
		Adminslog:       q.Adminslog.clone(db),
		Agent:           q.Agent.clone(db),
		AgentTran:       q.AgentTran.clone(db),
		Balance:         q.Balance.clone(db),
		Bscorder:        q.Bscorder.clone(db),
		Cfg:             q.Cfg.clone(db),
		Ercorder:        q.Ercorder.clone(db),
		Lease:           q.Lease.clone(db),
		Lockaddress:     q.Lockaddress.clone(db),
		Merchant:        q.Merchant.clone(db),
		Merchantaddress: q.Merchantaddress.clone(db),
		Merchantlog:     q.Merchantlog.clone(db),
		Order:           q.Order.clone(db),
		Payorder:        q.Payorder.clone(db),
		Permission:      q.Permission.clone(db),
		Role:            q.Role.clone(db),
		RolePermission:  q.RolePermission.clone(db),
		Settlement:      q.Settlement.clone(db),
		Tran:            q.Tran.clone(db),
		Trcorder:        q.Trcorder.clone(db),
		Uorder:          q.Uorder.clone(db),
		Whitelist:       q.Whitelist.clone(db),
	}
}

func (q *Query) ReadDB() *Query {
	return q.ReplaceDB(q.db.Clauses(dbresolver.Read))
}

func (q *Query) WriteDB() *Query {
	return q.ReplaceDB(q.db.Clauses(dbresolver.Write))
}

func (q *Query) ReplaceDB(db *gorm.DB) *Query {
	return &Query{
		db:              db,
		Address:         q.Address.replaceDB(db),
		AddressCopy2:    q.AddressCopy2.replaceDB(db),
		Admin:           q.Admin.replaceDB(db),
		AdminRole:       q.AdminRole.replaceDB(db),
		Adminslog:       q.Adminslog.replaceDB(db),
		Agent:           q.Agent.replaceDB(db),
		AgentTran:       q.AgentTran.replaceDB(db),
		Balance:         q.Balance.replaceDB(db),
		Bscorder:        q.Bscorder.replaceDB(db),
		Cfg:             q.Cfg.replaceDB(db),
		Ercorder:        q.Ercorder.replaceDB(db),
		Lease:           q.Lease.replaceDB(db),
		Lockaddress:     q.Lockaddress.replaceDB(db),
		Merchant:        q.Merchant.replaceDB(db),
		Merchantaddress: q.Merchantaddress.replaceDB(db),
		Merchantlog:     q.Merchantlog.replaceDB(db),
		Order:           q.Order.replaceDB(db),
		Payorder:        q.Payorder.replaceDB(db),
		Permission:      q.Permission.replaceDB(db),
		Role:            q.Role.replaceDB(db),
		RolePermission:  q.RolePermission.replaceDB(db),
		Settlement:      q.Settlement.replaceDB(db),
		Tran:            q.Tran.replaceDB(db),
		Trcorder:        q.Trcorder.replaceDB(db),
		Uorder:          q.Uorder.replaceDB(db),
		Whitelist:       q.Whitelist.replaceDB(db),
	}
}

type queryCtx struct {
	Address         *addressDo
	AddressCopy2    *addressCopy2Do
	Admin           *adminDo
	AdminRole       *adminRoleDo
	Adminslog       *adminslogDo
	Agent           *agentDo
	AgentTran       *agentTranDo
	Balance         *balanceDo
	Bscorder        *bscorderDo
	Cfg             *cfgDo
	Ercorder        *ercorderDo
	Lease           *leaseDo
	Lockaddress     *lockaddressDo
	Merchant        *merchantDo
	Merchantaddress *merchantaddressDo
	Merchantlog     *merchantlogDo
	Order           *orderDo
	Payorder        *payorderDo
	Permission      *permissionDo
	Role            *roleDo
	RolePermission  *rolePermissionDo
	Settlement      *settlementDo
	Tran            *tranDo
	Trcorder        *trcorderDo
	Uorder          *uorderDo
	Whitelist       *whitelistDo
}

func (q *Query) WithContext(ctx context.Context) *queryCtx {
	return &queryCtx{
		Address:         q.Address.WithContext(ctx),
		AddressCopy2:    q.AddressCopy2.WithContext(ctx),
		Admin:           q.Admin.WithContext(ctx),
		AdminRole:       q.AdminRole.WithContext(ctx),
		Adminslog:       q.Adminslog.WithContext(ctx),
		Agent:           q.Agent.WithContext(ctx),
		AgentTran:       q.AgentTran.WithContext(ctx),
		Balance:         q.Balance.WithContext(ctx),
		Bscorder:        q.Bscorder.WithContext(ctx),
		Cfg:             q.Cfg.WithContext(ctx),
		Ercorder:        q.Ercorder.WithContext(ctx),
		Lease:           q.Lease.WithContext(ctx),
		Lockaddress:     q.Lockaddress.WithContext(ctx),
		Merchant:        q.Merchant.WithContext(ctx),
		Merchantaddress: q.Merchantaddress.WithContext(ctx),
		Merchantlog:     q.Merchantlog.WithContext(ctx),
		Order:           q.Order.WithContext(ctx),
		Payorder:        q.Payorder.WithContext(ctx),
		Permission:      q.Permission.WithContext(ctx),
		Role:            q.Role.WithContext(ctx),
		RolePermission:  q.RolePermission.WithContext(ctx),
		Settlement:      q.Settlement.WithContext(ctx),
		Tran:            q.Tran.WithContext(ctx),
		Trcorder:        q.Trcorder.WithContext(ctx),
		Uorder:          q.Uorder.WithContext(ctx),
		Whitelist:       q.Whitelist.WithContext(ctx),
	}
}

func (q *Query) Transaction(fc func(tx *Query) error, opts ...*sql.TxOptions) error {
	return q.db.Transaction(func(tx *gorm.DB) error { return fc(q.clone(tx)) }, opts...)
}

func (q *Query) Begin(opts ...*sql.TxOptions) *QueryTx {
	tx := q.db.Begin(opts...)
	return &QueryTx{Query: q.clone(tx), Error: tx.Error}
}

type QueryTx struct {
	*Query
	Error error
}

func (q *QueryTx) Commit() error {
	return q.db.Commit().Error
}

func (q *QueryTx) Rollback() error {
	return q.db.Rollback().Error
}

func (q *QueryTx) SavePoint(name string) error {
	return q.db.SavePoint(name).Error
}

func (q *QueryTx) RollbackTo(name string) error {
	return q.db.RollbackTo(name).Error
}
