// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package dal

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"Admin/model/entity"
)

func newLockaddress(db *gorm.DB, opts ...gen.DOOption) lockaddress {
	_lockaddress := lockaddress{}

	_lockaddress.lockaddressDo.UseDB(db, opts...)
	_lockaddress.lockaddressDo.UseModel(&entity.Lockaddress{})

	tableName := _lockaddress.lockaddressDo.TableName()
	_lockaddress.ALL = field.NewAsterisk(tableName)
	_lockaddress.ID = field.NewInt32(tableName, "id")
	_lockaddress.Addrid = field.NewInt32(tableName, "addrid")
	_lockaddress.Money = field.NewString(tableName, "money")
	_lockaddress.Ctime = field.NewInt32(tableName, "ctime")
	_lockaddress.Etime = field.NewInt32(tableName, "etime")
	_lockaddress.Islock = field.NewInt32(tableName, "islock")

	_lockaddress.fillFieldMap()

	return _lockaddress
}

type lockaddress struct {
	lockaddressDo

	ALL    field.Asterisk
	ID     field.Int32
	Addrid field.Int32  // 地址id
	Money  field.String // 金额
	Ctime  field.Int32  // 锁定时间
	Etime  field.Int32  // 解锁时间
	Islock field.Int32  // 锁定状态 0锁定 1解锁

	fieldMap map[string]field.Expr
}

func (l lockaddress) Table(newTableName string) *lockaddress {
	l.lockaddressDo.UseTable(newTableName)
	return l.updateTableName(newTableName)
}

func (l lockaddress) As(alias string) *lockaddress {
	l.lockaddressDo.DO = *(l.lockaddressDo.As(alias).(*gen.DO))
	return l.updateTableName(alias)
}

func (l *lockaddress) updateTableName(table string) *lockaddress {
	l.ALL = field.NewAsterisk(table)
	l.ID = field.NewInt32(table, "id")
	l.Addrid = field.NewInt32(table, "addrid")
	l.Money = field.NewString(table, "money")
	l.Ctime = field.NewInt32(table, "ctime")
	l.Etime = field.NewInt32(table, "etime")
	l.Islock = field.NewInt32(table, "islock")

	l.fillFieldMap()

	return l
}

func (l *lockaddress) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := l.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (l *lockaddress) fillFieldMap() {
	l.fieldMap = make(map[string]field.Expr, 6)
	l.fieldMap["id"] = l.ID
	l.fieldMap["addrid"] = l.Addrid
	l.fieldMap["money"] = l.Money
	l.fieldMap["ctime"] = l.Ctime
	l.fieldMap["etime"] = l.Etime
	l.fieldMap["islock"] = l.Islock
}

func (l lockaddress) clone(db *gorm.DB) lockaddress {
	l.lockaddressDo.ReplaceConnPool(db.Statement.ConnPool)
	return l
}

func (l lockaddress) replaceDB(db *gorm.DB) lockaddress {
	l.lockaddressDo.ReplaceDB(db)
	return l
}

type lockaddressDo struct{ gen.DO }

func (l lockaddressDo) Debug() *lockaddressDo {
	return l.withDO(l.DO.Debug())
}

func (l lockaddressDo) WithContext(ctx context.Context) *lockaddressDo {
	return l.withDO(l.DO.WithContext(ctx))
}

func (l lockaddressDo) ReadDB() *lockaddressDo {
	return l.Clauses(dbresolver.Read)
}

func (l lockaddressDo) WriteDB() *lockaddressDo {
	return l.Clauses(dbresolver.Write)
}

func (l lockaddressDo) Session(config *gorm.Session) *lockaddressDo {
	return l.withDO(l.DO.Session(config))
}

func (l lockaddressDo) Clauses(conds ...clause.Expression) *lockaddressDo {
	return l.withDO(l.DO.Clauses(conds...))
}

func (l lockaddressDo) Returning(value interface{}, columns ...string) *lockaddressDo {
	return l.withDO(l.DO.Returning(value, columns...))
}

func (l lockaddressDo) Not(conds ...gen.Condition) *lockaddressDo {
	return l.withDO(l.DO.Not(conds...))
}

func (l lockaddressDo) Or(conds ...gen.Condition) *lockaddressDo {
	return l.withDO(l.DO.Or(conds...))
}

func (l lockaddressDo) Select(conds ...field.Expr) *lockaddressDo {
	return l.withDO(l.DO.Select(conds...))
}

func (l lockaddressDo) Where(conds ...gen.Condition) *lockaddressDo {
	return l.withDO(l.DO.Where(conds...))
}

func (l lockaddressDo) Exists(subquery interface{ UnderlyingDB() *gorm.DB }) *lockaddressDo {
	return l.Where(field.CompareSubQuery(field.ExistsOp, nil, subquery.UnderlyingDB()))
}

func (l lockaddressDo) Order(conds ...field.Expr) *lockaddressDo {
	return l.withDO(l.DO.Order(conds...))
}

func (l lockaddressDo) Distinct(cols ...field.Expr) *lockaddressDo {
	return l.withDO(l.DO.Distinct(cols...))
}

func (l lockaddressDo) Omit(cols ...field.Expr) *lockaddressDo {
	return l.withDO(l.DO.Omit(cols...))
}

func (l lockaddressDo) Join(table schema.Tabler, on ...field.Expr) *lockaddressDo {
	return l.withDO(l.DO.Join(table, on...))
}

func (l lockaddressDo) LeftJoin(table schema.Tabler, on ...field.Expr) *lockaddressDo {
	return l.withDO(l.DO.LeftJoin(table, on...))
}

func (l lockaddressDo) RightJoin(table schema.Tabler, on ...field.Expr) *lockaddressDo {
	return l.withDO(l.DO.RightJoin(table, on...))
}

func (l lockaddressDo) Group(cols ...field.Expr) *lockaddressDo {
	return l.withDO(l.DO.Group(cols...))
}

func (l lockaddressDo) Having(conds ...gen.Condition) *lockaddressDo {
	return l.withDO(l.DO.Having(conds...))
}

func (l lockaddressDo) Limit(limit int) *lockaddressDo {
	return l.withDO(l.DO.Limit(limit))
}

func (l lockaddressDo) Offset(offset int) *lockaddressDo {
	return l.withDO(l.DO.Offset(offset))
}

func (l lockaddressDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *lockaddressDo {
	return l.withDO(l.DO.Scopes(funcs...))
}

func (l lockaddressDo) Unscoped() *lockaddressDo {
	return l.withDO(l.DO.Unscoped())
}

func (l lockaddressDo) Create(values ...*entity.Lockaddress) error {
	if len(values) == 0 {
		return nil
	}
	return l.DO.Create(values)
}

func (l lockaddressDo) CreateInBatches(values []*entity.Lockaddress, batchSize int) error {
	return l.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (l lockaddressDo) Save(values ...*entity.Lockaddress) error {
	if len(values) == 0 {
		return nil
	}
	return l.DO.Save(values)
}

func (l lockaddressDo) First() (*entity.Lockaddress, error) {
	if result, err := l.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*entity.Lockaddress), nil
	}
}

func (l lockaddressDo) Take() (*entity.Lockaddress, error) {
	if result, err := l.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*entity.Lockaddress), nil
	}
}

func (l lockaddressDo) Last() (*entity.Lockaddress, error) {
	if result, err := l.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*entity.Lockaddress), nil
	}
}

func (l lockaddressDo) Find() ([]*entity.Lockaddress, error) {
	result, err := l.DO.Find()
	return result.([]*entity.Lockaddress), err
}

func (l lockaddressDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*entity.Lockaddress, err error) {
	buf := make([]*entity.Lockaddress, 0, batchSize)
	err = l.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (l lockaddressDo) FindInBatches(result *[]*entity.Lockaddress, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return l.DO.FindInBatches(result, batchSize, fc)
}

func (l lockaddressDo) Attrs(attrs ...field.AssignExpr) *lockaddressDo {
	return l.withDO(l.DO.Attrs(attrs...))
}

func (l lockaddressDo) Assign(attrs ...field.AssignExpr) *lockaddressDo {
	return l.withDO(l.DO.Assign(attrs...))
}

func (l lockaddressDo) Joins(fields ...field.RelationField) *lockaddressDo {
	for _, _f := range fields {
		l = *l.withDO(l.DO.Joins(_f))
	}
	return &l
}

func (l lockaddressDo) Preload(fields ...field.RelationField) *lockaddressDo {
	for _, _f := range fields {
		l = *l.withDO(l.DO.Preload(_f))
	}
	return &l
}

func (l lockaddressDo) FirstOrInit() (*entity.Lockaddress, error) {
	if result, err := l.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*entity.Lockaddress), nil
	}
}

func (l lockaddressDo) FirstOrCreate() (*entity.Lockaddress, error) {
	if result, err := l.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*entity.Lockaddress), nil
	}
}

func (l lockaddressDo) FindByPage(offset int, limit int) (result []*entity.Lockaddress, count int64, err error) {
	result, err = l.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = l.Offset(-1).Limit(-1).Count()
	return
}

func (l lockaddressDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = l.Count()
	if err != nil {
		return
	}

	err = l.Offset(offset).Limit(limit).Scan(result)
	return
}

func (l lockaddressDo) Scan(result interface{}) (err error) {
	return l.DO.Scan(result)
}

func (l lockaddressDo) Delete(models ...*entity.Lockaddress) (result gen.ResultInfo, err error) {
	return l.DO.Delete(models)
}

func (l *lockaddressDo) withDO(do gen.Dao) *lockaddressDo {
	l.DO = *do.(*gen.DO)
	return l
}
