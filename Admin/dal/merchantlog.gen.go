// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package dal

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"Admin/model/entity"
)

func newMerchantlog(db *gorm.DB, opts ...gen.DOOption) merchantlog {
	_merchantlog := merchantlog{}

	_merchantlog.merchantlogDo.UseDB(db, opts...)
	_merchantlog.merchantlogDo.UseModel(&entity.Merchantlog{})

	tableName := _merchantlog.merchantlogDo.TableName()
	_merchantlog.ALL = field.NewAsterisk(tableName)
	_merchantlog.ID = field.NewInt32(tableName, "id")
	_merchantlog.MerchantID = field.NewInt32(tableName, "merchant_id")
	_merchantlog.MerchantName = field.NewString(tableName, "merchant_name")
	_merchantlog.IP = field.NewString(tableName, "ip")
	_merchantlog.Type = field.NewInt32(tableName, "type")
	_merchantlog.Content = field.NewString(tableName, "content")
	_merchantlog.Ctime = field.NewInt32(tableName, "ctime")
	_merchantlog.CreatedAt = field.NewTime(tableName, "created_at")

	_merchantlog.fillFieldMap()

	return _merchantlog
}

type merchantlog struct {
	merchantlogDo

	ALL          field.Asterisk
	ID           field.Int32
	MerchantID   field.Int32  // 商户id
	MerchantName field.String // 商户名
	IP           field.String // ip
	Type         field.Int32  // 操作类型
	Content      field.String // 关联数据
	Ctime        field.Int32
	CreatedAt    field.Time

	fieldMap map[string]field.Expr
}

func (m merchantlog) Table(newTableName string) *merchantlog {
	m.merchantlogDo.UseTable(newTableName)
	return m.updateTableName(newTableName)
}

func (m merchantlog) As(alias string) *merchantlog {
	m.merchantlogDo.DO = *(m.merchantlogDo.As(alias).(*gen.DO))
	return m.updateTableName(alias)
}

func (m *merchantlog) updateTableName(table string) *merchantlog {
	m.ALL = field.NewAsterisk(table)
	m.ID = field.NewInt32(table, "id")
	m.MerchantID = field.NewInt32(table, "merchant_id")
	m.MerchantName = field.NewString(table, "merchant_name")
	m.IP = field.NewString(table, "ip")
	m.Type = field.NewInt32(table, "type")
	m.Content = field.NewString(table, "content")
	m.Ctime = field.NewInt32(table, "ctime")
	m.CreatedAt = field.NewTime(table, "created_at")

	m.fillFieldMap()

	return m
}

func (m *merchantlog) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := m.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (m *merchantlog) fillFieldMap() {
	m.fieldMap = make(map[string]field.Expr, 8)
	m.fieldMap["id"] = m.ID
	m.fieldMap["merchant_id"] = m.MerchantID
	m.fieldMap["merchant_name"] = m.MerchantName
	m.fieldMap["ip"] = m.IP
	m.fieldMap["type"] = m.Type
	m.fieldMap["content"] = m.Content
	m.fieldMap["ctime"] = m.Ctime
	m.fieldMap["created_at"] = m.CreatedAt
}

func (m merchantlog) clone(db *gorm.DB) merchantlog {
	m.merchantlogDo.ReplaceConnPool(db.Statement.ConnPool)
	return m
}

func (m merchantlog) replaceDB(db *gorm.DB) merchantlog {
	m.merchantlogDo.ReplaceDB(db)
	return m
}

type merchantlogDo struct{ gen.DO }

func (m merchantlogDo) Debug() *merchantlogDo {
	return m.withDO(m.DO.Debug())
}

func (m merchantlogDo) WithContext(ctx context.Context) *merchantlogDo {
	return m.withDO(m.DO.WithContext(ctx))
}

func (m merchantlogDo) ReadDB() *merchantlogDo {
	return m.Clauses(dbresolver.Read)
}

func (m merchantlogDo) WriteDB() *merchantlogDo {
	return m.Clauses(dbresolver.Write)
}

func (m merchantlogDo) Session(config *gorm.Session) *merchantlogDo {
	return m.withDO(m.DO.Session(config))
}

func (m merchantlogDo) Clauses(conds ...clause.Expression) *merchantlogDo {
	return m.withDO(m.DO.Clauses(conds...))
}

func (m merchantlogDo) Returning(value interface{}, columns ...string) *merchantlogDo {
	return m.withDO(m.DO.Returning(value, columns...))
}

func (m merchantlogDo) Not(conds ...gen.Condition) *merchantlogDo {
	return m.withDO(m.DO.Not(conds...))
}

func (m merchantlogDo) Or(conds ...gen.Condition) *merchantlogDo {
	return m.withDO(m.DO.Or(conds...))
}

func (m merchantlogDo) Select(conds ...field.Expr) *merchantlogDo {
	return m.withDO(m.DO.Select(conds...))
}

func (m merchantlogDo) Where(conds ...gen.Condition) *merchantlogDo {
	return m.withDO(m.DO.Where(conds...))
}

func (m merchantlogDo) Exists(subquery interface{ UnderlyingDB() *gorm.DB }) *merchantlogDo {
	return m.Where(field.CompareSubQuery(field.ExistsOp, nil, subquery.UnderlyingDB()))
}

func (m merchantlogDo) Order(conds ...field.Expr) *merchantlogDo {
	return m.withDO(m.DO.Order(conds...))
}

func (m merchantlogDo) Distinct(cols ...field.Expr) *merchantlogDo {
	return m.withDO(m.DO.Distinct(cols...))
}

func (m merchantlogDo) Omit(cols ...field.Expr) *merchantlogDo {
	return m.withDO(m.DO.Omit(cols...))
}

func (m merchantlogDo) Join(table schema.Tabler, on ...field.Expr) *merchantlogDo {
	return m.withDO(m.DO.Join(table, on...))
}

func (m merchantlogDo) LeftJoin(table schema.Tabler, on ...field.Expr) *merchantlogDo {
	return m.withDO(m.DO.LeftJoin(table, on...))
}

func (m merchantlogDo) RightJoin(table schema.Tabler, on ...field.Expr) *merchantlogDo {
	return m.withDO(m.DO.RightJoin(table, on...))
}

func (m merchantlogDo) Group(cols ...field.Expr) *merchantlogDo {
	return m.withDO(m.DO.Group(cols...))
}

func (m merchantlogDo) Having(conds ...gen.Condition) *merchantlogDo {
	return m.withDO(m.DO.Having(conds...))
}

func (m merchantlogDo) Limit(limit int) *merchantlogDo {
	return m.withDO(m.DO.Limit(limit))
}

func (m merchantlogDo) Offset(offset int) *merchantlogDo {
	return m.withDO(m.DO.Offset(offset))
}

func (m merchantlogDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *merchantlogDo {
	return m.withDO(m.DO.Scopes(funcs...))
}

func (m merchantlogDo) Unscoped() *merchantlogDo {
	return m.withDO(m.DO.Unscoped())
}

func (m merchantlogDo) Create(values ...*entity.Merchantlog) error {
	if len(values) == 0 {
		return nil
	}
	return m.DO.Create(values)
}

func (m merchantlogDo) CreateInBatches(values []*entity.Merchantlog, batchSize int) error {
	return m.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (m merchantlogDo) Save(values ...*entity.Merchantlog) error {
	if len(values) == 0 {
		return nil
	}
	return m.DO.Save(values)
}

func (m merchantlogDo) First() (*entity.Merchantlog, error) {
	if result, err := m.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*entity.Merchantlog), nil
	}
}

func (m merchantlogDo) Take() (*entity.Merchantlog, error) {
	if result, err := m.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*entity.Merchantlog), nil
	}
}

func (m merchantlogDo) Last() (*entity.Merchantlog, error) {
	if result, err := m.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*entity.Merchantlog), nil
	}
}

func (m merchantlogDo) Find() ([]*entity.Merchantlog, error) {
	result, err := m.DO.Find()
	return result.([]*entity.Merchantlog), err
}

func (m merchantlogDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*entity.Merchantlog, err error) {
	buf := make([]*entity.Merchantlog, 0, batchSize)
	err = m.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (m merchantlogDo) FindInBatches(result *[]*entity.Merchantlog, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return m.DO.FindInBatches(result, batchSize, fc)
}

func (m merchantlogDo) Attrs(attrs ...field.AssignExpr) *merchantlogDo {
	return m.withDO(m.DO.Attrs(attrs...))
}

func (m merchantlogDo) Assign(attrs ...field.AssignExpr) *merchantlogDo {
	return m.withDO(m.DO.Assign(attrs...))
}

func (m merchantlogDo) Joins(fields ...field.RelationField) *merchantlogDo {
	for _, _f := range fields {
		m = *m.withDO(m.DO.Joins(_f))
	}
	return &m
}

func (m merchantlogDo) Preload(fields ...field.RelationField) *merchantlogDo {
	for _, _f := range fields {
		m = *m.withDO(m.DO.Preload(_f))
	}
	return &m
}

func (m merchantlogDo) FirstOrInit() (*entity.Merchantlog, error) {
	if result, err := m.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*entity.Merchantlog), nil
	}
}

func (m merchantlogDo) FirstOrCreate() (*entity.Merchantlog, error) {
	if result, err := m.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*entity.Merchantlog), nil
	}
}

func (m merchantlogDo) FindByPage(offset int, limit int) (result []*entity.Merchantlog, count int64, err error) {
	result, err = m.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = m.Offset(-1).Limit(-1).Count()
	return
}

func (m merchantlogDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = m.Count()
	if err != nil {
		return
	}

	err = m.Offset(offset).Limit(limit).Scan(result)
	return
}

func (m merchantlogDo) Scan(result interface{}) (err error) {
	return m.DO.Scan(result)
}

func (m merchantlogDo) Delete(models ...*entity.Merchantlog) (result gen.ResultInfo, err error) {
	return m.DO.Delete(models)
}

func (m *merchantlogDo) withDO(do gen.Dao) *merchantlogDo {
	m.DO = *do.(*gen.DO)
	return m
}
