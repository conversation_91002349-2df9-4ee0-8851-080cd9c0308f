// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package dal

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"Admin/model/entity"
)

func newBalance(db *gorm.DB, opts ...gen.DOOption) balance {
	_balance := balance{}

	_balance.balanceDo.UseDB(db, opts...)
	_balance.balanceDo.UseModel(&entity.Balance{})

	tableName := _balance.balanceDo.TableName()
	_balance.ALL = field.NewAsterisk(tableName)
	_balance.ID = field.NewInt32(tableName, "id")
	_balance.MerchantID = field.NewInt32(tableName, "merchant_id")
	_balance.MerchantName = field.NewString(tableName, "merchant_name")
	_balance.Trc = field.NewFloat64(tableName, "trc")
	_balance.Erc = field.NewFloat64(tableName, "erc")
	_balance.Bsc = field.NewFloat64(tableName, "bsc")
	_balance.Ctime = field.NewInt32(tableName, "ctime")

	_balance.fillFieldMap()

	return _balance
}

type balance struct {
	balanceDo

	ALL          field.Asterisk
	ID           field.Int32
	MerchantID   field.Int32
	MerchantName field.String
	Trc          field.Float64
	Erc          field.Float64
	Bsc          field.Float64
	Ctime        field.Int32

	fieldMap map[string]field.Expr
}

func (b balance) Table(newTableName string) *balance {
	b.balanceDo.UseTable(newTableName)
	return b.updateTableName(newTableName)
}

func (b balance) As(alias string) *balance {
	b.balanceDo.DO = *(b.balanceDo.As(alias).(*gen.DO))
	return b.updateTableName(alias)
}

func (b *balance) updateTableName(table string) *balance {
	b.ALL = field.NewAsterisk(table)
	b.ID = field.NewInt32(table, "id")
	b.MerchantID = field.NewInt32(table, "merchant_id")
	b.MerchantName = field.NewString(table, "merchant_name")
	b.Trc = field.NewFloat64(table, "trc")
	b.Erc = field.NewFloat64(table, "erc")
	b.Bsc = field.NewFloat64(table, "bsc")
	b.Ctime = field.NewInt32(table, "ctime")

	b.fillFieldMap()

	return b
}

func (b *balance) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := b.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (b *balance) fillFieldMap() {
	b.fieldMap = make(map[string]field.Expr, 7)
	b.fieldMap["id"] = b.ID
	b.fieldMap["merchant_id"] = b.MerchantID
	b.fieldMap["merchant_name"] = b.MerchantName
	b.fieldMap["trc"] = b.Trc
	b.fieldMap["erc"] = b.Erc
	b.fieldMap["bsc"] = b.Bsc
	b.fieldMap["ctime"] = b.Ctime
}

func (b balance) clone(db *gorm.DB) balance {
	b.balanceDo.ReplaceConnPool(db.Statement.ConnPool)
	return b
}

func (b balance) replaceDB(db *gorm.DB) balance {
	b.balanceDo.ReplaceDB(db)
	return b
}

type balanceDo struct{ gen.DO }

func (b balanceDo) Debug() *balanceDo {
	return b.withDO(b.DO.Debug())
}

func (b balanceDo) WithContext(ctx context.Context) *balanceDo {
	return b.withDO(b.DO.WithContext(ctx))
}

func (b balanceDo) ReadDB() *balanceDo {
	return b.Clauses(dbresolver.Read)
}

func (b balanceDo) WriteDB() *balanceDo {
	return b.Clauses(dbresolver.Write)
}

func (b balanceDo) Session(config *gorm.Session) *balanceDo {
	return b.withDO(b.DO.Session(config))
}

func (b balanceDo) Clauses(conds ...clause.Expression) *balanceDo {
	return b.withDO(b.DO.Clauses(conds...))
}

func (b balanceDo) Returning(value interface{}, columns ...string) *balanceDo {
	return b.withDO(b.DO.Returning(value, columns...))
}

func (b balanceDo) Not(conds ...gen.Condition) *balanceDo {
	return b.withDO(b.DO.Not(conds...))
}

func (b balanceDo) Or(conds ...gen.Condition) *balanceDo {
	return b.withDO(b.DO.Or(conds...))
}

func (b balanceDo) Select(conds ...field.Expr) *balanceDo {
	return b.withDO(b.DO.Select(conds...))
}

func (b balanceDo) Where(conds ...gen.Condition) *balanceDo {
	return b.withDO(b.DO.Where(conds...))
}

func (b balanceDo) Exists(subquery interface{ UnderlyingDB() *gorm.DB }) *balanceDo {
	return b.Where(field.CompareSubQuery(field.ExistsOp, nil, subquery.UnderlyingDB()))
}

func (b balanceDo) Order(conds ...field.Expr) *balanceDo {
	return b.withDO(b.DO.Order(conds...))
}

func (b balanceDo) Distinct(cols ...field.Expr) *balanceDo {
	return b.withDO(b.DO.Distinct(cols...))
}

func (b balanceDo) Omit(cols ...field.Expr) *balanceDo {
	return b.withDO(b.DO.Omit(cols...))
}

func (b balanceDo) Join(table schema.Tabler, on ...field.Expr) *balanceDo {
	return b.withDO(b.DO.Join(table, on...))
}

func (b balanceDo) LeftJoin(table schema.Tabler, on ...field.Expr) *balanceDo {
	return b.withDO(b.DO.LeftJoin(table, on...))
}

func (b balanceDo) RightJoin(table schema.Tabler, on ...field.Expr) *balanceDo {
	return b.withDO(b.DO.RightJoin(table, on...))
}

func (b balanceDo) Group(cols ...field.Expr) *balanceDo {
	return b.withDO(b.DO.Group(cols...))
}

func (b balanceDo) Having(conds ...gen.Condition) *balanceDo {
	return b.withDO(b.DO.Having(conds...))
}

func (b balanceDo) Limit(limit int) *balanceDo {
	return b.withDO(b.DO.Limit(limit))
}

func (b balanceDo) Offset(offset int) *balanceDo {
	return b.withDO(b.DO.Offset(offset))
}

func (b balanceDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *balanceDo {
	return b.withDO(b.DO.Scopes(funcs...))
}

func (b balanceDo) Unscoped() *balanceDo {
	return b.withDO(b.DO.Unscoped())
}

func (b balanceDo) Create(values ...*entity.Balance) error {
	if len(values) == 0 {
		return nil
	}
	return b.DO.Create(values)
}

func (b balanceDo) CreateInBatches(values []*entity.Balance, batchSize int) error {
	return b.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (b balanceDo) Save(values ...*entity.Balance) error {
	if len(values) == 0 {
		return nil
	}
	return b.DO.Save(values)
}

func (b balanceDo) First() (*entity.Balance, error) {
	if result, err := b.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*entity.Balance), nil
	}
}

func (b balanceDo) Take() (*entity.Balance, error) {
	if result, err := b.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*entity.Balance), nil
	}
}

func (b balanceDo) Last() (*entity.Balance, error) {
	if result, err := b.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*entity.Balance), nil
	}
}

func (b balanceDo) Find() ([]*entity.Balance, error) {
	result, err := b.DO.Find()
	return result.([]*entity.Balance), err
}

func (b balanceDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*entity.Balance, err error) {
	buf := make([]*entity.Balance, 0, batchSize)
	err = b.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (b balanceDo) FindInBatches(result *[]*entity.Balance, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return b.DO.FindInBatches(result, batchSize, fc)
}

func (b balanceDo) Attrs(attrs ...field.AssignExpr) *balanceDo {
	return b.withDO(b.DO.Attrs(attrs...))
}

func (b balanceDo) Assign(attrs ...field.AssignExpr) *balanceDo {
	return b.withDO(b.DO.Assign(attrs...))
}

func (b balanceDo) Joins(fields ...field.RelationField) *balanceDo {
	for _, _f := range fields {
		b = *b.withDO(b.DO.Joins(_f))
	}
	return &b
}

func (b balanceDo) Preload(fields ...field.RelationField) *balanceDo {
	for _, _f := range fields {
		b = *b.withDO(b.DO.Preload(_f))
	}
	return &b
}

func (b balanceDo) FirstOrInit() (*entity.Balance, error) {
	if result, err := b.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*entity.Balance), nil
	}
}

func (b balanceDo) FirstOrCreate() (*entity.Balance, error) {
	if result, err := b.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*entity.Balance), nil
	}
}

func (b balanceDo) FindByPage(offset int, limit int) (result []*entity.Balance, count int64, err error) {
	result, err = b.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = b.Offset(-1).Limit(-1).Count()
	return
}

func (b balanceDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = b.Count()
	if err != nil {
		return
	}

	err = b.Offset(offset).Limit(limit).Scan(result)
	return
}

func (b balanceDo) Scan(result interface{}) (err error) {
	return b.DO.Scan(result)
}

func (b balanceDo) Delete(models ...*entity.Balance) (result gen.ResultInfo, err error) {
	return b.DO.Delete(models)
}

func (b *balanceDo) withDO(do gen.Dao) *balanceDo {
	b.DO = *do.(*gen.DO)
	return b
}
