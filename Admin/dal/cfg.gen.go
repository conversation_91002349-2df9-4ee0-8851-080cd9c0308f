// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package dal

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"Admin/model/entity"
)

func newCfg(db *gorm.DB, opts ...gen.DOOption) cfg {
	_cfg := cfg{}

	_cfg.cfgDo.UseDB(db, opts...)
	_cfg.cfgDo.UseModel(&entity.Cfg{})

	tableName := _cfg.cfgDo.TableName()
	_cfg.ALL = field.NewAsterisk(tableName)
	_cfg.Func = field.NewString(tableName, "Func")
	_cfg.Value = field.NewString(tableName, "Value")

	_cfg.fillFieldMap()

	return _cfg
}

type cfg struct {
	cfgDo

	ALL   field.Asterisk
	Func  field.String // 配置名称
	Value field.String // 值

	fieldMap map[string]field.Expr
}

func (c cfg) Table(newTableName string) *cfg {
	c.cfgDo.UseTable(newTableName)
	return c.updateTableName(newTableName)
}

func (c cfg) As(alias string) *cfg {
	c.cfgDo.DO = *(c.cfgDo.As(alias).(*gen.DO))
	return c.updateTableName(alias)
}

func (c *cfg) updateTableName(table string) *cfg {
	c.ALL = field.NewAsterisk(table)
	c.Func = field.NewString(table, "Func")
	c.Value = field.NewString(table, "Value")

	c.fillFieldMap()

	return c
}

func (c *cfg) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := c.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (c *cfg) fillFieldMap() {
	c.fieldMap = make(map[string]field.Expr, 2)
	c.fieldMap["Func"] = c.Func
	c.fieldMap["Value"] = c.Value
}

func (c cfg) clone(db *gorm.DB) cfg {
	c.cfgDo.ReplaceConnPool(db.Statement.ConnPool)
	return c
}

func (c cfg) replaceDB(db *gorm.DB) cfg {
	c.cfgDo.ReplaceDB(db)
	return c
}

type cfgDo struct{ gen.DO }

func (c cfgDo) Debug() *cfgDo {
	return c.withDO(c.DO.Debug())
}

func (c cfgDo) WithContext(ctx context.Context) *cfgDo {
	return c.withDO(c.DO.WithContext(ctx))
}

func (c cfgDo) ReadDB() *cfgDo {
	return c.Clauses(dbresolver.Read)
}

func (c cfgDo) WriteDB() *cfgDo {
	return c.Clauses(dbresolver.Write)
}

func (c cfgDo) Session(config *gorm.Session) *cfgDo {
	return c.withDO(c.DO.Session(config))
}

func (c cfgDo) Clauses(conds ...clause.Expression) *cfgDo {
	return c.withDO(c.DO.Clauses(conds...))
}

func (c cfgDo) Returning(value interface{}, columns ...string) *cfgDo {
	return c.withDO(c.DO.Returning(value, columns...))
}

func (c cfgDo) Not(conds ...gen.Condition) *cfgDo {
	return c.withDO(c.DO.Not(conds...))
}

func (c cfgDo) Or(conds ...gen.Condition) *cfgDo {
	return c.withDO(c.DO.Or(conds...))
}

func (c cfgDo) Select(conds ...field.Expr) *cfgDo {
	return c.withDO(c.DO.Select(conds...))
}

func (c cfgDo) Where(conds ...gen.Condition) *cfgDo {
	return c.withDO(c.DO.Where(conds...))
}

func (c cfgDo) Exists(subquery interface{ UnderlyingDB() *gorm.DB }) *cfgDo {
	return c.Where(field.CompareSubQuery(field.ExistsOp, nil, subquery.UnderlyingDB()))
}

func (c cfgDo) Order(conds ...field.Expr) *cfgDo {
	return c.withDO(c.DO.Order(conds...))
}

func (c cfgDo) Distinct(cols ...field.Expr) *cfgDo {
	return c.withDO(c.DO.Distinct(cols...))
}

func (c cfgDo) Omit(cols ...field.Expr) *cfgDo {
	return c.withDO(c.DO.Omit(cols...))
}

func (c cfgDo) Join(table schema.Tabler, on ...field.Expr) *cfgDo {
	return c.withDO(c.DO.Join(table, on...))
}

func (c cfgDo) LeftJoin(table schema.Tabler, on ...field.Expr) *cfgDo {
	return c.withDO(c.DO.LeftJoin(table, on...))
}

func (c cfgDo) RightJoin(table schema.Tabler, on ...field.Expr) *cfgDo {
	return c.withDO(c.DO.RightJoin(table, on...))
}

func (c cfgDo) Group(cols ...field.Expr) *cfgDo {
	return c.withDO(c.DO.Group(cols...))
}

func (c cfgDo) Having(conds ...gen.Condition) *cfgDo {
	return c.withDO(c.DO.Having(conds...))
}

func (c cfgDo) Limit(limit int) *cfgDo {
	return c.withDO(c.DO.Limit(limit))
}

func (c cfgDo) Offset(offset int) *cfgDo {
	return c.withDO(c.DO.Offset(offset))
}

func (c cfgDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *cfgDo {
	return c.withDO(c.DO.Scopes(funcs...))
}

func (c cfgDo) Unscoped() *cfgDo {
	return c.withDO(c.DO.Unscoped())
}

func (c cfgDo) Create(values ...*entity.Cfg) error {
	if len(values) == 0 {
		return nil
	}
	return c.DO.Create(values)
}

func (c cfgDo) CreateInBatches(values []*entity.Cfg, batchSize int) error {
	return c.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (c cfgDo) Save(values ...*entity.Cfg) error {
	if len(values) == 0 {
		return nil
	}
	return c.DO.Save(values)
}

func (c cfgDo) First() (*entity.Cfg, error) {
	if result, err := c.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*entity.Cfg), nil
	}
}

func (c cfgDo) Take() (*entity.Cfg, error) {
	if result, err := c.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*entity.Cfg), nil
	}
}

func (c cfgDo) Last() (*entity.Cfg, error) {
	if result, err := c.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*entity.Cfg), nil
	}
}

func (c cfgDo) Find() ([]*entity.Cfg, error) {
	result, err := c.DO.Find()
	return result.([]*entity.Cfg), err
}

func (c cfgDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*entity.Cfg, err error) {
	buf := make([]*entity.Cfg, 0, batchSize)
	err = c.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (c cfgDo) FindInBatches(result *[]*entity.Cfg, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return c.DO.FindInBatches(result, batchSize, fc)
}

func (c cfgDo) Attrs(attrs ...field.AssignExpr) *cfgDo {
	return c.withDO(c.DO.Attrs(attrs...))
}

func (c cfgDo) Assign(attrs ...field.AssignExpr) *cfgDo {
	return c.withDO(c.DO.Assign(attrs...))
}

func (c cfgDo) Joins(fields ...field.RelationField) *cfgDo {
	for _, _f := range fields {
		c = *c.withDO(c.DO.Joins(_f))
	}
	return &c
}

func (c cfgDo) Preload(fields ...field.RelationField) *cfgDo {
	for _, _f := range fields {
		c = *c.withDO(c.DO.Preload(_f))
	}
	return &c
}

func (c cfgDo) FirstOrInit() (*entity.Cfg, error) {
	if result, err := c.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*entity.Cfg), nil
	}
}

func (c cfgDo) FirstOrCreate() (*entity.Cfg, error) {
	if result, err := c.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*entity.Cfg), nil
	}
}

func (c cfgDo) FindByPage(offset int, limit int) (result []*entity.Cfg, count int64, err error) {
	result, err = c.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = c.Offset(-1).Limit(-1).Count()
	return
}

func (c cfgDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = c.Count()
	if err != nil {
		return
	}

	err = c.Offset(offset).Limit(limit).Scan(result)
	return
}

func (c cfgDo) Scan(result interface{}) (err error) {
	return c.DO.Scan(result)
}

func (c cfgDo) Delete(models ...*entity.Cfg) (result gen.ResultInfo, err error) {
	return c.DO.Delete(models)
}

func (c *cfgDo) withDO(do gen.Dao) *cfgDo {
	c.DO = *do.(*gen.DO)
	return c
}
