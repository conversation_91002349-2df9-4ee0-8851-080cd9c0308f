// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package dal

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"Admin/model/entity"
)

func newPayorder(db *gorm.DB, opts ...gen.DOOption) payorder {
	_payorder := payorder{}

	_payorder.payorderDo.UseDB(db, opts...)
	_payorder.payorderDo.UseModel(&entity.Payorder{})

	tableName := _payorder.payorderDo.TableName()
	_payorder.ALL = field.NewAsterisk(tableName)
	_payorder.ID = field.NewInt64(tableName, "id")
	_payorder.MerchantID = field.NewString(tableName, "merchant_id")
	_payorder.Appkey = field.NewString(tableName, "appkey")
	_payorder.Transactionid = field.NewString(tableName, "transactionid")
	_payorder.Orderid = field.NewString(tableName, "orderid")
	_payorder.Rorderid = field.NewString(tableName, "rorderid")
	_payorder.Addr = field.NewString(tableName, "addr")
	_payorder.Money = field.NewString(tableName, "money")
	_payorder.Coin = field.NewString(tableName, "coin")
	_payorder.TranFee = field.NewFloat64(tableName, "tran_fee")
	_payorder.RealTranFee = field.NewFloat64(tableName, "real_tran_fee")
	_payorder.Status = field.NewInt32(tableName, "status")
	_payorder.Callbackurl = field.NewString(tableName, "callbackurl")
	_payorder.Callbacknum = field.NewInt32(tableName, "callbacknum")
	_payorder.Callbackresult = field.NewString(tableName, "callbackresult")
	_payorder.MerchantName = field.NewString(tableName, "merchant_name")
	_payorder.Ctime = field.NewInt32(tableName, "ctime")
	_payorder.Etime = field.NewInt32(tableName, "etime")
	_payorder.Returnurl = field.NewString(tableName, "returnurl")
	_payorder.Rmb = field.NewString(tableName, "rmb")
	_payorder.Rate = field.NewString(tableName, "rate")
	_payorder.Chain = field.NewString(tableName, "chain")
	_payorder.Lease = field.NewInt32(tableName, "lease")
	_payorder.Leaseid = field.NewString(tableName, "leaseid")

	_payorder.fillFieldMap()

	return _payorder
}

type payorder struct {
	payorderDo

	ALL            field.Asterisk
	ID             field.Int64
	MerchantID     field.String // 商户id
	Appkey         field.String // 商户密钥
	Transactionid  field.String
	Orderid        field.String  // 订单号
	Rorderid       field.String  // 商户订单id
	Addr           field.String  // 收款地址
	Money          field.String  // 金额
	Coin           field.String  // 币种
	TranFee        field.Float64 // 预估矿工费
	RealTranFee    field.Float64 // 实际花费矿工费
	Status         field.Int32   // 0:创建订单 1:超时 2:转账成功 3:回调成功 4:主动成功 5:转账失败
	Callbackurl    field.String  // 回调地址
	Callbacknum    field.Int32   // 回调次数
	Callbackresult field.String  // 回调返回信息
	MerchantName   field.String  // 商户名
	Ctime          field.Int32
	Etime          field.Int32
	Returnurl      field.String // 跳转地址
	Rmb            field.String
	Rate           field.String // 汇率
	Chain          field.String // 链
	Lease          field.Int32  // 能量是否到账 0 未到账 1 已到账
	Leaseid        field.String // 租赁订单号

	fieldMap map[string]field.Expr
}

func (p payorder) Table(newTableName string) *payorder {
	p.payorderDo.UseTable(newTableName)
	return p.updateTableName(newTableName)
}

func (p payorder) As(alias string) *payorder {
	p.payorderDo.DO = *(p.payorderDo.As(alias).(*gen.DO))
	return p.updateTableName(alias)
}

func (p *payorder) updateTableName(table string) *payorder {
	p.ALL = field.NewAsterisk(table)
	p.ID = field.NewInt64(table, "id")
	p.MerchantID = field.NewString(table, "merchant_id")
	p.Appkey = field.NewString(table, "appkey")
	p.Transactionid = field.NewString(table, "transactionid")
	p.Orderid = field.NewString(table, "orderid")
	p.Rorderid = field.NewString(table, "rorderid")
	p.Addr = field.NewString(table, "addr")
	p.Money = field.NewString(table, "money")
	p.Coin = field.NewString(table, "coin")
	p.TranFee = field.NewFloat64(table, "tran_fee")
	p.RealTranFee = field.NewFloat64(table, "real_tran_fee")
	p.Status = field.NewInt32(table, "status")
	p.Callbackurl = field.NewString(table, "callbackurl")
	p.Callbacknum = field.NewInt32(table, "callbacknum")
	p.Callbackresult = field.NewString(table, "callbackresult")
	p.MerchantName = field.NewString(table, "merchant_name")
	p.Ctime = field.NewInt32(table, "ctime")
	p.Etime = field.NewInt32(table, "etime")
	p.Returnurl = field.NewString(table, "returnurl")
	p.Rmb = field.NewString(table, "rmb")
	p.Rate = field.NewString(table, "rate")
	p.Chain = field.NewString(table, "chain")
	p.Lease = field.NewInt32(table, "lease")
	p.Leaseid = field.NewString(table, "leaseid")

	p.fillFieldMap()

	return p
}

func (p *payorder) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := p.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (p *payorder) fillFieldMap() {
	p.fieldMap = make(map[string]field.Expr, 24)
	p.fieldMap["id"] = p.ID
	p.fieldMap["merchant_id"] = p.MerchantID
	p.fieldMap["appkey"] = p.Appkey
	p.fieldMap["transactionid"] = p.Transactionid
	p.fieldMap["orderid"] = p.Orderid
	p.fieldMap["rorderid"] = p.Rorderid
	p.fieldMap["addr"] = p.Addr
	p.fieldMap["money"] = p.Money
	p.fieldMap["coin"] = p.Coin
	p.fieldMap["tran_fee"] = p.TranFee
	p.fieldMap["real_tran_fee"] = p.RealTranFee
	p.fieldMap["status"] = p.Status
	p.fieldMap["callbackurl"] = p.Callbackurl
	p.fieldMap["callbacknum"] = p.Callbacknum
	p.fieldMap["callbackresult"] = p.Callbackresult
	p.fieldMap["merchant_name"] = p.MerchantName
	p.fieldMap["ctime"] = p.Ctime
	p.fieldMap["etime"] = p.Etime
	p.fieldMap["returnurl"] = p.Returnurl
	p.fieldMap["rmb"] = p.Rmb
	p.fieldMap["rate"] = p.Rate
	p.fieldMap["chain"] = p.Chain
	p.fieldMap["lease"] = p.Lease
	p.fieldMap["leaseid"] = p.Leaseid
}

func (p payorder) clone(db *gorm.DB) payorder {
	p.payorderDo.ReplaceConnPool(db.Statement.ConnPool)
	return p
}

func (p payorder) replaceDB(db *gorm.DB) payorder {
	p.payorderDo.ReplaceDB(db)
	return p
}

type payorderDo struct{ gen.DO }

func (p payorderDo) Debug() *payorderDo {
	return p.withDO(p.DO.Debug())
}

func (p payorderDo) WithContext(ctx context.Context) *payorderDo {
	return p.withDO(p.DO.WithContext(ctx))
}

func (p payorderDo) ReadDB() *payorderDo {
	return p.Clauses(dbresolver.Read)
}

func (p payorderDo) WriteDB() *payorderDo {
	return p.Clauses(dbresolver.Write)
}

func (p payorderDo) Session(config *gorm.Session) *payorderDo {
	return p.withDO(p.DO.Session(config))
}

func (p payorderDo) Clauses(conds ...clause.Expression) *payorderDo {
	return p.withDO(p.DO.Clauses(conds...))
}

func (p payorderDo) Returning(value interface{}, columns ...string) *payorderDo {
	return p.withDO(p.DO.Returning(value, columns...))
}

func (p payorderDo) Not(conds ...gen.Condition) *payorderDo {
	return p.withDO(p.DO.Not(conds...))
}

func (p payorderDo) Or(conds ...gen.Condition) *payorderDo {
	return p.withDO(p.DO.Or(conds...))
}

func (p payorderDo) Select(conds ...field.Expr) *payorderDo {
	return p.withDO(p.DO.Select(conds...))
}

func (p payorderDo) Where(conds ...gen.Condition) *payorderDo {
	return p.withDO(p.DO.Where(conds...))
}

func (p payorderDo) Exists(subquery interface{ UnderlyingDB() *gorm.DB }) *payorderDo {
	return p.Where(field.CompareSubQuery(field.ExistsOp, nil, subquery.UnderlyingDB()))
}

func (p payorderDo) Order(conds ...field.Expr) *payorderDo {
	return p.withDO(p.DO.Order(conds...))
}

func (p payorderDo) Distinct(cols ...field.Expr) *payorderDo {
	return p.withDO(p.DO.Distinct(cols...))
}

func (p payorderDo) Omit(cols ...field.Expr) *payorderDo {
	return p.withDO(p.DO.Omit(cols...))
}

func (p payorderDo) Join(table schema.Tabler, on ...field.Expr) *payorderDo {
	return p.withDO(p.DO.Join(table, on...))
}

func (p payorderDo) LeftJoin(table schema.Tabler, on ...field.Expr) *payorderDo {
	return p.withDO(p.DO.LeftJoin(table, on...))
}

func (p payorderDo) RightJoin(table schema.Tabler, on ...field.Expr) *payorderDo {
	return p.withDO(p.DO.RightJoin(table, on...))
}

func (p payorderDo) Group(cols ...field.Expr) *payorderDo {
	return p.withDO(p.DO.Group(cols...))
}

func (p payorderDo) Having(conds ...gen.Condition) *payorderDo {
	return p.withDO(p.DO.Having(conds...))
}

func (p payorderDo) Limit(limit int) *payorderDo {
	return p.withDO(p.DO.Limit(limit))
}

func (p payorderDo) Offset(offset int) *payorderDo {
	return p.withDO(p.DO.Offset(offset))
}

func (p payorderDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *payorderDo {
	return p.withDO(p.DO.Scopes(funcs...))
}

func (p payorderDo) Unscoped() *payorderDo {
	return p.withDO(p.DO.Unscoped())
}

func (p payorderDo) Create(values ...*entity.Payorder) error {
	if len(values) == 0 {
		return nil
	}
	return p.DO.Create(values)
}

func (p payorderDo) CreateInBatches(values []*entity.Payorder, batchSize int) error {
	return p.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (p payorderDo) Save(values ...*entity.Payorder) error {
	if len(values) == 0 {
		return nil
	}
	return p.DO.Save(values)
}

func (p payorderDo) First() (*entity.Payorder, error) {
	if result, err := p.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*entity.Payorder), nil
	}
}

func (p payorderDo) Take() (*entity.Payorder, error) {
	if result, err := p.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*entity.Payorder), nil
	}
}

func (p payorderDo) Last() (*entity.Payorder, error) {
	if result, err := p.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*entity.Payorder), nil
	}
}

func (p payorderDo) Find() ([]*entity.Payorder, error) {
	result, err := p.DO.Find()
	return result.([]*entity.Payorder), err
}

func (p payorderDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*entity.Payorder, err error) {
	buf := make([]*entity.Payorder, 0, batchSize)
	err = p.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (p payorderDo) FindInBatches(result *[]*entity.Payorder, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return p.DO.FindInBatches(result, batchSize, fc)
}

func (p payorderDo) Attrs(attrs ...field.AssignExpr) *payorderDo {
	return p.withDO(p.DO.Attrs(attrs...))
}

func (p payorderDo) Assign(attrs ...field.AssignExpr) *payorderDo {
	return p.withDO(p.DO.Assign(attrs...))
}

func (p payorderDo) Joins(fields ...field.RelationField) *payorderDo {
	for _, _f := range fields {
		p = *p.withDO(p.DO.Joins(_f))
	}
	return &p
}

func (p payorderDo) Preload(fields ...field.RelationField) *payorderDo {
	for _, _f := range fields {
		p = *p.withDO(p.DO.Preload(_f))
	}
	return &p
}

func (p payorderDo) FirstOrInit() (*entity.Payorder, error) {
	if result, err := p.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*entity.Payorder), nil
	}
}

func (p payorderDo) FirstOrCreate() (*entity.Payorder, error) {
	if result, err := p.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*entity.Payorder), nil
	}
}

func (p payorderDo) FindByPage(offset int, limit int) (result []*entity.Payorder, count int64, err error) {
	result, err = p.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = p.Offset(-1).Limit(-1).Count()
	return
}

func (p payorderDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = p.Count()
	if err != nil {
		return
	}

	err = p.Offset(offset).Limit(limit).Scan(result)
	return
}

func (p payorderDo) Scan(result interface{}) (err error) {
	return p.DO.Scan(result)
}

func (p payorderDo) Delete(models ...*entity.Payorder) (result gen.ResultInfo, err error) {
	return p.DO.Delete(models)
}

func (p *payorderDo) withDO(do gen.Dao) *payorderDo {
	p.DO = *do.(*gen.DO)
	return p
}
