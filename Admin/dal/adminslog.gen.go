// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package dal

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"Admin/model/entity"
)

func newAdminslog(db *gorm.DB, opts ...gen.DOOption) adminslog {
	_adminslog := adminslog{}

	_adminslog.adminslogDo.UseDB(db, opts...)
	_adminslog.adminslogDo.UseModel(&entity.Adminslog{})

	tableName := _adminslog.adminslogDo.TableName()
	_adminslog.ALL = field.NewAsterisk(tableName)
	_adminslog.ID = field.NewInt32(tableName, "id")
	_adminslog.Adminid = field.NewInt32(tableName, "adminid")
	_adminslog.Username = field.NewString(tableName, "username")
	_adminslog.Action = field.NewString(tableName, "action")
	_adminslog.Route = field.NewString(tableName, "route")
	_adminslog.Method = field.NewString(tableName, "method")
	_adminslog.Content = field.NewString(tableName, "content")
	_adminslog.IP = field.NewString(tableName, "ip")
	_adminslog.Ctime = field.NewInt32(tableName, "ctime")
	_adminslog.DelFlg = field.NewInt32(tableName, "del_flg")

	_adminslog.fillFieldMap()

	return _adminslog
}

type adminslog struct {
	adminslogDo

	ALL      field.Asterisk
	ID       field.Int32
	Adminid  field.Int32
	Username field.String
	Action   field.String // 操作
	Route    field.String // 路由
	Method   field.String // GET POST
	Content  field.String // 请求数据
	IP       field.String // ip地址
	Ctime    field.Int32
	DelFlg   field.Int32

	fieldMap map[string]field.Expr
}

func (a adminslog) Table(newTableName string) *adminslog {
	a.adminslogDo.UseTable(newTableName)
	return a.updateTableName(newTableName)
}

func (a adminslog) As(alias string) *adminslog {
	a.adminslogDo.DO = *(a.adminslogDo.As(alias).(*gen.DO))
	return a.updateTableName(alias)
}

func (a *adminslog) updateTableName(table string) *adminslog {
	a.ALL = field.NewAsterisk(table)
	a.ID = field.NewInt32(table, "id")
	a.Adminid = field.NewInt32(table, "adminid")
	a.Username = field.NewString(table, "username")
	a.Action = field.NewString(table, "action")
	a.Route = field.NewString(table, "route")
	a.Method = field.NewString(table, "method")
	a.Content = field.NewString(table, "content")
	a.IP = field.NewString(table, "ip")
	a.Ctime = field.NewInt32(table, "ctime")
	a.DelFlg = field.NewInt32(table, "del_flg")

	a.fillFieldMap()

	return a
}

func (a *adminslog) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := a.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (a *adminslog) fillFieldMap() {
	a.fieldMap = make(map[string]field.Expr, 10)
	a.fieldMap["id"] = a.ID
	a.fieldMap["adminid"] = a.Adminid
	a.fieldMap["username"] = a.Username
	a.fieldMap["action"] = a.Action
	a.fieldMap["route"] = a.Route
	a.fieldMap["method"] = a.Method
	a.fieldMap["content"] = a.Content
	a.fieldMap["ip"] = a.IP
	a.fieldMap["ctime"] = a.Ctime
	a.fieldMap["del_flg"] = a.DelFlg
}

func (a adminslog) clone(db *gorm.DB) adminslog {
	a.adminslogDo.ReplaceConnPool(db.Statement.ConnPool)
	return a
}

func (a adminslog) replaceDB(db *gorm.DB) adminslog {
	a.adminslogDo.ReplaceDB(db)
	return a
}

type adminslogDo struct{ gen.DO }

func (a adminslogDo) Debug() *adminslogDo {
	return a.withDO(a.DO.Debug())
}

func (a adminslogDo) WithContext(ctx context.Context) *adminslogDo {
	return a.withDO(a.DO.WithContext(ctx))
}

func (a adminslogDo) ReadDB() *adminslogDo {
	return a.Clauses(dbresolver.Read)
}

func (a adminslogDo) WriteDB() *adminslogDo {
	return a.Clauses(dbresolver.Write)
}

func (a adminslogDo) Session(config *gorm.Session) *adminslogDo {
	return a.withDO(a.DO.Session(config))
}

func (a adminslogDo) Clauses(conds ...clause.Expression) *adminslogDo {
	return a.withDO(a.DO.Clauses(conds...))
}

func (a adminslogDo) Returning(value interface{}, columns ...string) *adminslogDo {
	return a.withDO(a.DO.Returning(value, columns...))
}

func (a adminslogDo) Not(conds ...gen.Condition) *adminslogDo {
	return a.withDO(a.DO.Not(conds...))
}

func (a adminslogDo) Or(conds ...gen.Condition) *adminslogDo {
	return a.withDO(a.DO.Or(conds...))
}

func (a adminslogDo) Select(conds ...field.Expr) *adminslogDo {
	return a.withDO(a.DO.Select(conds...))
}

func (a adminslogDo) Where(conds ...gen.Condition) *adminslogDo {
	return a.withDO(a.DO.Where(conds...))
}

func (a adminslogDo) Exists(subquery interface{ UnderlyingDB() *gorm.DB }) *adminslogDo {
	return a.Where(field.CompareSubQuery(field.ExistsOp, nil, subquery.UnderlyingDB()))
}

func (a adminslogDo) Order(conds ...field.Expr) *adminslogDo {
	return a.withDO(a.DO.Order(conds...))
}

func (a adminslogDo) Distinct(cols ...field.Expr) *adminslogDo {
	return a.withDO(a.DO.Distinct(cols...))
}

func (a adminslogDo) Omit(cols ...field.Expr) *adminslogDo {
	return a.withDO(a.DO.Omit(cols...))
}

func (a adminslogDo) Join(table schema.Tabler, on ...field.Expr) *adminslogDo {
	return a.withDO(a.DO.Join(table, on...))
}

func (a adminslogDo) LeftJoin(table schema.Tabler, on ...field.Expr) *adminslogDo {
	return a.withDO(a.DO.LeftJoin(table, on...))
}

func (a adminslogDo) RightJoin(table schema.Tabler, on ...field.Expr) *adminslogDo {
	return a.withDO(a.DO.RightJoin(table, on...))
}

func (a adminslogDo) Group(cols ...field.Expr) *adminslogDo {
	return a.withDO(a.DO.Group(cols...))
}

func (a adminslogDo) Having(conds ...gen.Condition) *adminslogDo {
	return a.withDO(a.DO.Having(conds...))
}

func (a adminslogDo) Limit(limit int) *adminslogDo {
	return a.withDO(a.DO.Limit(limit))
}

func (a adminslogDo) Offset(offset int) *adminslogDo {
	return a.withDO(a.DO.Offset(offset))
}

func (a adminslogDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *adminslogDo {
	return a.withDO(a.DO.Scopes(funcs...))
}

func (a adminslogDo) Unscoped() *adminslogDo {
	return a.withDO(a.DO.Unscoped())
}

func (a adminslogDo) Create(values ...*entity.Adminslog) error {
	if len(values) == 0 {
		return nil
	}
	return a.DO.Create(values)
}

func (a adminslogDo) CreateInBatches(values []*entity.Adminslog, batchSize int) error {
	return a.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (a adminslogDo) Save(values ...*entity.Adminslog) error {
	if len(values) == 0 {
		return nil
	}
	return a.DO.Save(values)
}

func (a adminslogDo) First() (*entity.Adminslog, error) {
	if result, err := a.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*entity.Adminslog), nil
	}
}

func (a adminslogDo) Take() (*entity.Adminslog, error) {
	if result, err := a.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*entity.Adminslog), nil
	}
}

func (a adminslogDo) Last() (*entity.Adminslog, error) {
	if result, err := a.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*entity.Adminslog), nil
	}
}

func (a adminslogDo) Find() ([]*entity.Adminslog, error) {
	result, err := a.DO.Find()
	return result.([]*entity.Adminslog), err
}

func (a adminslogDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*entity.Adminslog, err error) {
	buf := make([]*entity.Adminslog, 0, batchSize)
	err = a.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (a adminslogDo) FindInBatches(result *[]*entity.Adminslog, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return a.DO.FindInBatches(result, batchSize, fc)
}

func (a adminslogDo) Attrs(attrs ...field.AssignExpr) *adminslogDo {
	return a.withDO(a.DO.Attrs(attrs...))
}

func (a adminslogDo) Assign(attrs ...field.AssignExpr) *adminslogDo {
	return a.withDO(a.DO.Assign(attrs...))
}

func (a adminslogDo) Joins(fields ...field.RelationField) *adminslogDo {
	for _, _f := range fields {
		a = *a.withDO(a.DO.Joins(_f))
	}
	return &a
}

func (a adminslogDo) Preload(fields ...field.RelationField) *adminslogDo {
	for _, _f := range fields {
		a = *a.withDO(a.DO.Preload(_f))
	}
	return &a
}

func (a adminslogDo) FirstOrInit() (*entity.Adminslog, error) {
	if result, err := a.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*entity.Adminslog), nil
	}
}

func (a adminslogDo) FirstOrCreate() (*entity.Adminslog, error) {
	if result, err := a.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*entity.Adminslog), nil
	}
}

func (a adminslogDo) FindByPage(offset int, limit int) (result []*entity.Adminslog, count int64, err error) {
	result, err = a.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = a.Offset(-1).Limit(-1).Count()
	return
}

func (a adminslogDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = a.Count()
	if err != nil {
		return
	}

	err = a.Offset(offset).Limit(limit).Scan(result)
	return
}

func (a adminslogDo) Scan(result interface{}) (err error) {
	return a.DO.Scan(result)
}

func (a adminslogDo) Delete(models ...*entity.Adminslog) (result gen.ResultInfo, err error) {
	return a.DO.Delete(models)
}

func (a *adminslogDo) withDO(do gen.Dao) *adminslogDo {
	a.DO = *do.(*gen.DO)
	return a
}
