package config

import (
	"log"

	"github.com/spf13/viper"
)

type Config struct {
	Log struct {
		Path       string `yaml:"path"`
		MaxSize    int    `yaml:"max_size"`
		MaxBackups int    `yaml:"max_backups"`
		MaxAge     int    `yaml:"max_age"`
	} `yaml:"log"`
	Db struct {
		Host     string `yaml:"host"`
		Port     string `yaml:"port"`
		Username string `yaml:"username"`
		Password string `yaml:"password"`
		Database string `yaml:"database"`
	} `yaml:"db"`
	Cashier struct {
		Cashierurl string `yaml:"cashierurl"`
	} `yaml:"cashier"`
	Jwt struct {
		Secret string `yaml:"secret"`
	} `yaml:"jwt"`
	Redis struct {
		Url      string `yaml:"url"`
		Password string `yaml:"password"`
	} `yaml:"redis"`
	Tg struct {
		Token string `yaml:"token"`
		Tgid  string `yaml:"tgid"`
	} `yaml:"tg"`
	Eth struct {
		Api      string  `yaml:"api"`
		Token    string  `yaml:"token"`
		Sun      float64 `yaml:"sun"`
		TokenSun float64 `yaml:"tokensun"`
	} `yaml:"eth"`
	Bsc struct {
		Api      string  `yaml:"api"`
		Token    string  `yaml:"token"`
		Sun      float64 `yaml:"sun"`
		TokenSun float64 `yaml:"tokensun"`
	} `yaml:"bsc"`
	Tron struct {
		Api    string  `yaml:"api"`
		Token  string  `yaml:"token"`
		Sun    float64 `yaml:"sun"`
		AppKey string  `yaml:"appkey"`
	} `yaml:"tron"`
	Addr struct {
		Trc string `yaml:"trc"`
		Erc string `yaml:"erc"`
		Key string `yaml:"key"`
	} `yaml:"addr"`
	LeaseApi struct {
		Url string `yaml:"url"`
		Key string `yaml:"key"`
	} `yaml:"leaseapi"`
}

func NewConfig() *Config {
	config := Config{}
	viper.SetConfigName("config")
	viper.SetConfigType("yaml")
	viper.AddConfigPath("local")
	viper.AddConfigPath(".")

	err := viper.ReadInConfig()
	if err != nil {
		log.Fatalln(err)
		panic("读取配置错误")
	}
	err = viper.Unmarshal(&config)
	if err != nil {
		log.Fatalln(err)
		panic("解析配置错误")
	}
	return &config
}
