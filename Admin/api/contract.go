package api

type TriggerContractRequest struct{
  ContractAddress string `json:"contract_address"`
  FunctionSelector string `json:"function_selector"`
  Parameter string `json:"parameter"`
  FeeLimit int32 `json:"fee_limit"`
  CallValue int32 `json:"call_value"`
  OwnerAddress string `json:"owner_address"`
  PermissionId int32 `json:"permission_id"`
  Visible bool `json:"visible"`
}

type TriggerContractResponse struct{
  Result ApiResult `json:"result"`
  ConstantResult []string `json:"constant_result"`
  Transaction ContractTransaction `json:"transaction"`
}

func NewTriggerContractRequest(contractAddress, functionSelector, parameter, ownerAddress string) *TriggerContractRequest {
  return &TriggerContractRequest{
    ContractAddress: contractAddress,
    FunctionSelector: functionSelector,
    Parameter: parameter,
    FeeLimit: 1000000000,
    CallValue: 0,
    OwnerAddress: ownerAddress,
    PermissionId: 0,
    Visible: false,
  }
}