package tron

import (
	"bytes"
	"crypto/sha256"
	"encoding/hex"

	"github.com/ethereum/go-ethereum/common"
	"github.com/mr-tron/base58"
)

func EncodeBytesAddress(input []byte) string {
	hash0 := sha256.Sum256(input)
	hash1 := sha256.Sum256(hash0[:])
	checksum := hash1[:4]
	output := bytes.Join([][]byte{input, checksum}, []byte{})

	b58Addr := base58.Encode(output)

	return b58Addr
}

func EncodeHexAddress(hexAddr string) (string, error) {
	input, err := hex.DecodeString(hexAddr)
	if err != nil {
		return "", err
	}
	return EncodeBytesAddress(input), nil
}

func DecodeBase58Address(b58Addr string) (string, error) {
	b58, err := base58.Decode(b58Addr)
	if err != nil {
		return "", err
	}
	hexAddr := hex.EncodeToString(b58[:21])
	return hexAddr, nil
}

func Base58ToEthAddress(b58Addr string) (common.Address, error) {
	hexAddr, err := DecodeBase58Address(b58Addr)
	if err != nil {
		return common.Address{}, err
	}
	return common.HexToAddress(hexAddr[2:]), nil
}

func Base58FromEthAddress(ethAddr common.Address) string {
	ethBytes := ethAddr.Bytes()
	tronBytes := bytes.Join([][]byte{{0x41}, ethBytes}, []byte{})
	return EncodeBytesAddress(tronBytes)
}
