// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package dal

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"Mapi/model/entity"
)

func newOrder(db *gorm.DB, opts ...gen.DOOption) order {
	_order := order{}

	_order.orderDo.UseDB(db, opts...)
	_order.orderDo.UseModel(&entity.Order{})

	tableName := _order.orderDo.TableName()
	_order.ALL = field.NewAsterisk(tableName)
	_order.ID = field.NewInt64(tableName, "id")
	_order.CreatedAt = field.NewTime(tableName, "created_at")
	_order.UpdatedAt = field.NewTime(tableName, "updated_at")
	_order.DeletedAt = field.NewField(tableName, "deleted_at")
	_order.MerchantID = field.NewString(tableName, "merchant_id")
	_order.Appkey = field.NewString(tableName, "appkey")
	_order.Transactionid = field.NewString(tableName, "transactionid")
	_order.Orderid = field.NewString(tableName, "orderid")
	_order.Rorderid = field.NewString(tableName, "rorderid")
	_order.Fromaddr = field.NewString(tableName, "fromaddr")
	_order.Score = field.NewString(tableName, "score")
	_order.Trxadd = field.NewString(tableName, "trxadd")
	_order.Addrid = field.NewInt32(tableName, "addrid")
	_order.Money = field.NewString(tableName, "money")
	_order.Status = field.NewInt32(tableName, "status")
	_order.Callbackurl = field.NewString(tableName, "callbackurl")
	_order.Callbacknum = field.NewInt32(tableName, "callbacknum")
	_order.Callbackresult = field.NewString(tableName, "callbackresult")
	_order.MerchantName = field.NewString(tableName, "merchant_name")
	_order.Ctime = field.NewInt32(tableName, "ctime")
	_order.Etime = field.NewInt32(tableName, "etime")
	_order.Returnurl = field.NewString(tableName, "returnurl")
	_order.Rmb = field.NewString(tableName, "rmb")
	_order.Rate = field.NewString(tableName, "rate")
	_order.Realmoney = field.NewString(tableName, "realmoney")
	_order.Chain = field.NewString(tableName, "chain")
	_order.St = field.NewInt32(tableName, "st")

	_order.fillFieldMap()

	return _order
}

type order struct {
	orderDo

	ALL            field.Asterisk
	ID             field.Int64
	CreatedAt      field.Time
	UpdatedAt      field.Time
	DeletedAt      field.Field
	MerchantID     field.String // 商户id
	Appkey         field.String // 商户密钥
	Transactionid  field.String
	Orderid        field.String // 订单号
	Rorderid       field.String // 商户订单id
	Fromaddr       field.String // 付款地址
	Score          field.String // 付款地址风险评分
	Trxadd         field.String // TRX地址
	Addrid         field.Int32
	Money          field.String // 金额
	Status         field.Int32  // 0:创建订单 1:超时 2:支付成功 3:回调成功 4:主动成功
	Callbackurl    field.String // 回调地址
	Callbacknum    field.Int32  // 回调次数
	Callbackresult field.String // 回调返回信息
	MerchantName   field.String // 商户名
	Ctime          field.Int32
	Etime          field.Int32
	Returnurl      field.String // 跳转地址
	Rmb            field.String
	Rate           field.String // 汇率
	Realmoney      field.String // 实收金额
	Chain          field.String // 链
	St             field.Int32

	fieldMap map[string]field.Expr
}

func (o order) Table(newTableName string) *order {
	o.orderDo.UseTable(newTableName)
	return o.updateTableName(newTableName)
}

func (o order) As(alias string) *order {
	o.orderDo.DO = *(o.orderDo.As(alias).(*gen.DO))
	return o.updateTableName(alias)
}

func (o *order) updateTableName(table string) *order {
	o.ALL = field.NewAsterisk(table)
	o.ID = field.NewInt64(table, "id")
	o.CreatedAt = field.NewTime(table, "created_at")
	o.UpdatedAt = field.NewTime(table, "updated_at")
	o.DeletedAt = field.NewField(table, "deleted_at")
	o.MerchantID = field.NewString(table, "merchant_id")
	o.Appkey = field.NewString(table, "appkey")
	o.Transactionid = field.NewString(table, "transactionid")
	o.Orderid = field.NewString(table, "orderid")
	o.Rorderid = field.NewString(table, "rorderid")
	o.Fromaddr = field.NewString(table, "fromaddr")
	o.Score = field.NewString(table, "score")
	o.Trxadd = field.NewString(table, "trxadd")
	o.Addrid = field.NewInt32(table, "addrid")
	o.Money = field.NewString(table, "money")
	o.Status = field.NewInt32(table, "status")
	o.Callbackurl = field.NewString(table, "callbackurl")
	o.Callbacknum = field.NewInt32(table, "callbacknum")
	o.Callbackresult = field.NewString(table, "callbackresult")
	o.MerchantName = field.NewString(table, "merchant_name")
	o.Ctime = field.NewInt32(table, "ctime")
	o.Etime = field.NewInt32(table, "etime")
	o.Returnurl = field.NewString(table, "returnurl")
	o.Rmb = field.NewString(table, "rmb")
	o.Rate = field.NewString(table, "rate")
	o.Realmoney = field.NewString(table, "realmoney")
	o.Chain = field.NewString(table, "chain")
	o.St = field.NewInt32(table, "st")

	o.fillFieldMap()

	return o
}

func (o *order) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := o.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (o *order) fillFieldMap() {
	o.fieldMap = make(map[string]field.Expr, 27)
	o.fieldMap["id"] = o.ID
	o.fieldMap["created_at"] = o.CreatedAt
	o.fieldMap["updated_at"] = o.UpdatedAt
	o.fieldMap["deleted_at"] = o.DeletedAt
	o.fieldMap["merchant_id"] = o.MerchantID
	o.fieldMap["appkey"] = o.Appkey
	o.fieldMap["transactionid"] = o.Transactionid
	o.fieldMap["orderid"] = o.Orderid
	o.fieldMap["rorderid"] = o.Rorderid
	o.fieldMap["fromaddr"] = o.Fromaddr
	o.fieldMap["score"] = o.Score
	o.fieldMap["trxadd"] = o.Trxadd
	o.fieldMap["addrid"] = o.Addrid
	o.fieldMap["money"] = o.Money
	o.fieldMap["status"] = o.Status
	o.fieldMap["callbackurl"] = o.Callbackurl
	o.fieldMap["callbacknum"] = o.Callbacknum
	o.fieldMap["callbackresult"] = o.Callbackresult
	o.fieldMap["merchant_name"] = o.MerchantName
	o.fieldMap["ctime"] = o.Ctime
	o.fieldMap["etime"] = o.Etime
	o.fieldMap["returnurl"] = o.Returnurl
	o.fieldMap["rmb"] = o.Rmb
	o.fieldMap["rate"] = o.Rate
	o.fieldMap["realmoney"] = o.Realmoney
	o.fieldMap["chain"] = o.Chain
	o.fieldMap["st"] = o.St
}

func (o order) clone(db *gorm.DB) order {
	o.orderDo.ReplaceConnPool(db.Statement.ConnPool)
	return o
}

func (o order) replaceDB(db *gorm.DB) order {
	o.orderDo.ReplaceDB(db)
	return o
}

type orderDo struct{ gen.DO }

func (o orderDo) Debug() *orderDo {
	return o.withDO(o.DO.Debug())
}

func (o orderDo) WithContext(ctx context.Context) *orderDo {
	return o.withDO(o.DO.WithContext(ctx))
}

func (o orderDo) ReadDB() *orderDo {
	return o.Clauses(dbresolver.Read)
}

func (o orderDo) WriteDB() *orderDo {
	return o.Clauses(dbresolver.Write)
}

func (o orderDo) Session(config *gorm.Session) *orderDo {
	return o.withDO(o.DO.Session(config))
}

func (o orderDo) Clauses(conds ...clause.Expression) *orderDo {
	return o.withDO(o.DO.Clauses(conds...))
}

func (o orderDo) Returning(value interface{}, columns ...string) *orderDo {
	return o.withDO(o.DO.Returning(value, columns...))
}

func (o orderDo) Not(conds ...gen.Condition) *orderDo {
	return o.withDO(o.DO.Not(conds...))
}

func (o orderDo) Or(conds ...gen.Condition) *orderDo {
	return o.withDO(o.DO.Or(conds...))
}

func (o orderDo) Select(conds ...field.Expr) *orderDo {
	return o.withDO(o.DO.Select(conds...))
}

func (o orderDo) Where(conds ...gen.Condition) *orderDo {
	return o.withDO(o.DO.Where(conds...))
}

func (o orderDo) Exists(subquery interface{ UnderlyingDB() *gorm.DB }) *orderDo {
	return o.Where(field.CompareSubQuery(field.ExistsOp, nil, subquery.UnderlyingDB()))
}

func (o orderDo) Order(conds ...field.Expr) *orderDo {
	return o.withDO(o.DO.Order(conds...))
}

func (o orderDo) Distinct(cols ...field.Expr) *orderDo {
	return o.withDO(o.DO.Distinct(cols...))
}

func (o orderDo) Omit(cols ...field.Expr) *orderDo {
	return o.withDO(o.DO.Omit(cols...))
}

func (o orderDo) Join(table schema.Tabler, on ...field.Expr) *orderDo {
	return o.withDO(o.DO.Join(table, on...))
}

func (o orderDo) LeftJoin(table schema.Tabler, on ...field.Expr) *orderDo {
	return o.withDO(o.DO.LeftJoin(table, on...))
}

func (o orderDo) RightJoin(table schema.Tabler, on ...field.Expr) *orderDo {
	return o.withDO(o.DO.RightJoin(table, on...))
}

func (o orderDo) Group(cols ...field.Expr) *orderDo {
	return o.withDO(o.DO.Group(cols...))
}

func (o orderDo) Having(conds ...gen.Condition) *orderDo {
	return o.withDO(o.DO.Having(conds...))
}

func (o orderDo) Limit(limit int) *orderDo {
	return o.withDO(o.DO.Limit(limit))
}

func (o orderDo) Offset(offset int) *orderDo {
	return o.withDO(o.DO.Offset(offset))
}

func (o orderDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *orderDo {
	return o.withDO(o.DO.Scopes(funcs...))
}

func (o orderDo) Unscoped() *orderDo {
	return o.withDO(o.DO.Unscoped())
}

func (o orderDo) Create(values ...*entity.Order) error {
	if len(values) == 0 {
		return nil
	}
	return o.DO.Create(values)
}

func (o orderDo) CreateInBatches(values []*entity.Order, batchSize int) error {
	return o.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (o orderDo) Save(values ...*entity.Order) error {
	if len(values) == 0 {
		return nil
	}
	return o.DO.Save(values)
}

func (o orderDo) First() (*entity.Order, error) {
	if result, err := o.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*entity.Order), nil
	}
}

func (o orderDo) Take() (*entity.Order, error) {
	if result, err := o.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*entity.Order), nil
	}
}

func (o orderDo) Last() (*entity.Order, error) {
	if result, err := o.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*entity.Order), nil
	}
}

func (o orderDo) Find() ([]*entity.Order, error) {
	result, err := o.DO.Find()
	return result.([]*entity.Order), err
}

func (o orderDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*entity.Order, err error) {
	buf := make([]*entity.Order, 0, batchSize)
	err = o.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (o orderDo) FindInBatches(result *[]*entity.Order, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return o.DO.FindInBatches(result, batchSize, fc)
}

func (o orderDo) Attrs(attrs ...field.AssignExpr) *orderDo {
	return o.withDO(o.DO.Attrs(attrs...))
}

func (o orderDo) Assign(attrs ...field.AssignExpr) *orderDo {
	return o.withDO(o.DO.Assign(attrs...))
}

func (o orderDo) Joins(fields ...field.RelationField) *orderDo {
	for _, _f := range fields {
		o = *o.withDO(o.DO.Joins(_f))
	}
	return &o
}

func (o orderDo) Preload(fields ...field.RelationField) *orderDo {
	for _, _f := range fields {
		o = *o.withDO(o.DO.Preload(_f))
	}
	return &o
}

func (o orderDo) FirstOrInit() (*entity.Order, error) {
	if result, err := o.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*entity.Order), nil
	}
}

func (o orderDo) FirstOrCreate() (*entity.Order, error) {
	if result, err := o.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*entity.Order), nil
	}
}

func (o orderDo) FindByPage(offset int, limit int) (result []*entity.Order, count int64, err error) {
	result, err = o.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = o.Offset(-1).Limit(-1).Count()
	return
}

func (o orderDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = o.Count()
	if err != nil {
		return
	}

	err = o.Offset(offset).Limit(limit).Scan(result)
	return
}

func (o orderDo) Scan(result interface{}) (err error) {
	return o.DO.Scan(result)
}

func (o orderDo) Delete(models ...*entity.Order) (result gen.ResultInfo, err error) {
	return o.DO.Delete(models)
}

func (o *orderDo) withDO(do gen.Dao) *orderDo {
	o.DO = *do.(*gen.DO)
	return o
}
