// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package dal

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"Mapi/model/entity"
)

func newTran(db *gorm.DB, opts ...gen.DOOption) tran {
	_tran := tran{}

	_tran.tranDo.UseDB(db, opts...)
	_tran.tranDo.UseModel(&entity.Tran{})

	tableName := _tran.tranDo.TableName()
	_tran.ALL = field.NewAsterisk(tableName)
	_tran.ID = field.NewInt32(tableName, "id")
	_tran.UserID = field.NewInt32(tableName, "user_id")
	_tran.UserType = field.NewInt32(tableName, "user_type")
	_tran.Type = field.NewInt32(tableName, "type")
	_tran.Money = field.NewFloat64(tableName, "money")
	_tran.Orderid = field.NewString(tableName, "orderid")
	_tran.Rorderid = field.NewString(tableName, "rorderid")
	_tran.Transactionid = field.NewString(tableName, "transactionid")
	_tran.Username = field.NewString(tableName, "username")
	_tran.Remark = field.NewString(tableName, "remark")
	_tran.Ctime = field.NewInt32(tableName, "ctime")
	_tran.St = field.NewInt32(tableName, "st")

	_tran.fillFieldMap()

	return _tran
}

type tran struct {
	tranDo

	ALL           field.Asterisk
	ID            field.Int32
	UserID        field.Int32   // 用户id
	UserType      field.Int32   // 1商户 2代理
	Type          field.Int32   // 1商户订单收入(TRC) 2商户手续费扣除(TRC) 3商户下发支出(TRC) 4归集支出(TRC) 5商户订单收入(ERC) 6商户手续费扣除(ERC) 7商户下发支出(ERC) 8归集支出(ERC)
	Money         field.Float64 // 金额
	Orderid       field.String  // 平台订单id
	Rorderid      field.String  // 商户订单id
	Transactionid field.String  // hash
	Username      field.String  // 用户名
	Remark        field.String  // 备注
	Ctime         field.Int32
	St            field.Int32

	fieldMap map[string]field.Expr
}

func (t tran) Table(newTableName string) *tran {
	t.tranDo.UseTable(newTableName)
	return t.updateTableName(newTableName)
}

func (t tran) As(alias string) *tran {
	t.tranDo.DO = *(t.tranDo.As(alias).(*gen.DO))
	return t.updateTableName(alias)
}

func (t *tran) updateTableName(table string) *tran {
	t.ALL = field.NewAsterisk(table)
	t.ID = field.NewInt32(table, "id")
	t.UserID = field.NewInt32(table, "user_id")
	t.UserType = field.NewInt32(table, "user_type")
	t.Type = field.NewInt32(table, "type")
	t.Money = field.NewFloat64(table, "money")
	t.Orderid = field.NewString(table, "orderid")
	t.Rorderid = field.NewString(table, "rorderid")
	t.Transactionid = field.NewString(table, "transactionid")
	t.Username = field.NewString(table, "username")
	t.Remark = field.NewString(table, "remark")
	t.Ctime = field.NewInt32(table, "ctime")
	t.St = field.NewInt32(table, "st")

	t.fillFieldMap()

	return t
}

func (t *tran) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := t.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (t *tran) fillFieldMap() {
	t.fieldMap = make(map[string]field.Expr, 12)
	t.fieldMap["id"] = t.ID
	t.fieldMap["user_id"] = t.UserID
	t.fieldMap["user_type"] = t.UserType
	t.fieldMap["type"] = t.Type
	t.fieldMap["money"] = t.Money
	t.fieldMap["orderid"] = t.Orderid
	t.fieldMap["rorderid"] = t.Rorderid
	t.fieldMap["transactionid"] = t.Transactionid
	t.fieldMap["username"] = t.Username
	t.fieldMap["remark"] = t.Remark
	t.fieldMap["ctime"] = t.Ctime
	t.fieldMap["st"] = t.St
}

func (t tran) clone(db *gorm.DB) tran {
	t.tranDo.ReplaceConnPool(db.Statement.ConnPool)
	return t
}

func (t tran) replaceDB(db *gorm.DB) tran {
	t.tranDo.ReplaceDB(db)
	return t
}

type tranDo struct{ gen.DO }

func (t tranDo) Debug() *tranDo {
	return t.withDO(t.DO.Debug())
}

func (t tranDo) WithContext(ctx context.Context) *tranDo {
	return t.withDO(t.DO.WithContext(ctx))
}

func (t tranDo) ReadDB() *tranDo {
	return t.Clauses(dbresolver.Read)
}

func (t tranDo) WriteDB() *tranDo {
	return t.Clauses(dbresolver.Write)
}

func (t tranDo) Session(config *gorm.Session) *tranDo {
	return t.withDO(t.DO.Session(config))
}

func (t tranDo) Clauses(conds ...clause.Expression) *tranDo {
	return t.withDO(t.DO.Clauses(conds...))
}

func (t tranDo) Returning(value interface{}, columns ...string) *tranDo {
	return t.withDO(t.DO.Returning(value, columns...))
}

func (t tranDo) Not(conds ...gen.Condition) *tranDo {
	return t.withDO(t.DO.Not(conds...))
}

func (t tranDo) Or(conds ...gen.Condition) *tranDo {
	return t.withDO(t.DO.Or(conds...))
}

func (t tranDo) Select(conds ...field.Expr) *tranDo {
	return t.withDO(t.DO.Select(conds...))
}

func (t tranDo) Where(conds ...gen.Condition) *tranDo {
	return t.withDO(t.DO.Where(conds...))
}

func (t tranDo) Exists(subquery interface{ UnderlyingDB() *gorm.DB }) *tranDo {
	return t.Where(field.CompareSubQuery(field.ExistsOp, nil, subquery.UnderlyingDB()))
}

func (t tranDo) Order(conds ...field.Expr) *tranDo {
	return t.withDO(t.DO.Order(conds...))
}

func (t tranDo) Distinct(cols ...field.Expr) *tranDo {
	return t.withDO(t.DO.Distinct(cols...))
}

func (t tranDo) Omit(cols ...field.Expr) *tranDo {
	return t.withDO(t.DO.Omit(cols...))
}

func (t tranDo) Join(table schema.Tabler, on ...field.Expr) *tranDo {
	return t.withDO(t.DO.Join(table, on...))
}

func (t tranDo) LeftJoin(table schema.Tabler, on ...field.Expr) *tranDo {
	return t.withDO(t.DO.LeftJoin(table, on...))
}

func (t tranDo) RightJoin(table schema.Tabler, on ...field.Expr) *tranDo {
	return t.withDO(t.DO.RightJoin(table, on...))
}

func (t tranDo) Group(cols ...field.Expr) *tranDo {
	return t.withDO(t.DO.Group(cols...))
}

func (t tranDo) Having(conds ...gen.Condition) *tranDo {
	return t.withDO(t.DO.Having(conds...))
}

func (t tranDo) Limit(limit int) *tranDo {
	return t.withDO(t.DO.Limit(limit))
}

func (t tranDo) Offset(offset int) *tranDo {
	return t.withDO(t.DO.Offset(offset))
}

func (t tranDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *tranDo {
	return t.withDO(t.DO.Scopes(funcs...))
}

func (t tranDo) Unscoped() *tranDo {
	return t.withDO(t.DO.Unscoped())
}

func (t tranDo) Create(values ...*entity.Tran) error {
	if len(values) == 0 {
		return nil
	}
	return t.DO.Create(values)
}

func (t tranDo) CreateInBatches(values []*entity.Tran, batchSize int) error {
	return t.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (t tranDo) Save(values ...*entity.Tran) error {
	if len(values) == 0 {
		return nil
	}
	return t.DO.Save(values)
}

func (t tranDo) First() (*entity.Tran, error) {
	if result, err := t.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*entity.Tran), nil
	}
}

func (t tranDo) Take() (*entity.Tran, error) {
	if result, err := t.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*entity.Tran), nil
	}
}

func (t tranDo) Last() (*entity.Tran, error) {
	if result, err := t.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*entity.Tran), nil
	}
}

func (t tranDo) Find() ([]*entity.Tran, error) {
	result, err := t.DO.Find()
	return result.([]*entity.Tran), err
}

func (t tranDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*entity.Tran, err error) {
	buf := make([]*entity.Tran, 0, batchSize)
	err = t.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (t tranDo) FindInBatches(result *[]*entity.Tran, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return t.DO.FindInBatches(result, batchSize, fc)
}

func (t tranDo) Attrs(attrs ...field.AssignExpr) *tranDo {
	return t.withDO(t.DO.Attrs(attrs...))
}

func (t tranDo) Assign(attrs ...field.AssignExpr) *tranDo {
	return t.withDO(t.DO.Assign(attrs...))
}

func (t tranDo) Joins(fields ...field.RelationField) *tranDo {
	for _, _f := range fields {
		t = *t.withDO(t.DO.Joins(_f))
	}
	return &t
}

func (t tranDo) Preload(fields ...field.RelationField) *tranDo {
	for _, _f := range fields {
		t = *t.withDO(t.DO.Preload(_f))
	}
	return &t
}

func (t tranDo) FirstOrInit() (*entity.Tran, error) {
	if result, err := t.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*entity.Tran), nil
	}
}

func (t tranDo) FirstOrCreate() (*entity.Tran, error) {
	if result, err := t.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*entity.Tran), nil
	}
}

func (t tranDo) FindByPage(offset int, limit int) (result []*entity.Tran, count int64, err error) {
	result, err = t.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = t.Offset(-1).Limit(-1).Count()
	return
}

func (t tranDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = t.Count()
	if err != nil {
		return
	}

	err = t.Offset(offset).Limit(limit).Scan(result)
	return
}

func (t tranDo) Scan(result interface{}) (err error) {
	return t.DO.Scan(result)
}

func (t tranDo) Delete(models ...*entity.Tran) (result gen.ResultInfo, err error) {
	return t.DO.Delete(models)
}

func (t *tranDo) withDO(do gen.Dao) *tranDo {
	t.DO = *do.(*gen.DO)
	return t
}
