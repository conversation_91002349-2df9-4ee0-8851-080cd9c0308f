// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package dal

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"Mapi/model/entity"
)

func newAgentTran(db *gorm.DB, opts ...gen.DOOption) agentTran {
	_agentTran := agentTran{}

	_agentTran.agentTranDo.UseDB(db, opts...)
	_agentTran.agentTranDo.UseModel(&entity.AgentTran{})

	tableName := _agentTran.agentTranDo.TableName()
	_agentTran.ALL = field.NewAsterisk(tableName)
	_agentTran.ID = field.NewInt32(tableName, "id")
	_agentTran.UserID = field.NewInt32(tableName, "user_id")
	_agentTran.Type = field.NewInt32(tableName, "type")
	_agentTran.Money = field.NewFloat64(tableName, "money")
	_agentTran.Orderid = field.NewString(tableName, "orderid")
	_agentTran.Rorderid = field.NewString(tableName, "rorderid")
	_agentTran.Transactionid = field.NewString(tableName, "transactionid")
	_agentTran.Username = field.NewString(tableName, "username")
	_agentTran.Remark = field.NewString(tableName, "remark")
	_agentTran.Ctime = field.NewInt32(tableName, "ctime")

	_agentTran.fillFieldMap()

	return _agentTran
}

type agentTran struct {
	agentTranDo

	ALL           field.Asterisk
	ID            field.Int32
	UserID        field.Int32   // 用户id
	Type          field.Int32   // 1代理提成(TRC) 2代理提成(ERC) 3代理提成(BSC) 4提现支出(TRC) 5提现支出(ERC) 6提现支出(BSC)
	Money         field.Float64 // 金额
	Orderid       field.String  // 平台订单id
	Rorderid      field.String  // 商户订单id
	Transactionid field.String  // hash
	Username      field.String  // 用户名
	Remark        field.String  // 备注
	Ctime         field.Int32

	fieldMap map[string]field.Expr
}

func (a agentTran) Table(newTableName string) *agentTran {
	a.agentTranDo.UseTable(newTableName)
	return a.updateTableName(newTableName)
}

func (a agentTran) As(alias string) *agentTran {
	a.agentTranDo.DO = *(a.agentTranDo.As(alias).(*gen.DO))
	return a.updateTableName(alias)
}

func (a *agentTran) updateTableName(table string) *agentTran {
	a.ALL = field.NewAsterisk(table)
	a.ID = field.NewInt32(table, "id")
	a.UserID = field.NewInt32(table, "user_id")
	a.Type = field.NewInt32(table, "type")
	a.Money = field.NewFloat64(table, "money")
	a.Orderid = field.NewString(table, "orderid")
	a.Rorderid = field.NewString(table, "rorderid")
	a.Transactionid = field.NewString(table, "transactionid")
	a.Username = field.NewString(table, "username")
	a.Remark = field.NewString(table, "remark")
	a.Ctime = field.NewInt32(table, "ctime")

	a.fillFieldMap()

	return a
}

func (a *agentTran) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := a.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (a *agentTran) fillFieldMap() {
	a.fieldMap = make(map[string]field.Expr, 10)
	a.fieldMap["id"] = a.ID
	a.fieldMap["user_id"] = a.UserID
	a.fieldMap["type"] = a.Type
	a.fieldMap["money"] = a.Money
	a.fieldMap["orderid"] = a.Orderid
	a.fieldMap["rorderid"] = a.Rorderid
	a.fieldMap["transactionid"] = a.Transactionid
	a.fieldMap["username"] = a.Username
	a.fieldMap["remark"] = a.Remark
	a.fieldMap["ctime"] = a.Ctime
}

func (a agentTran) clone(db *gorm.DB) agentTran {
	a.agentTranDo.ReplaceConnPool(db.Statement.ConnPool)
	return a
}

func (a agentTran) replaceDB(db *gorm.DB) agentTran {
	a.agentTranDo.ReplaceDB(db)
	return a
}

type agentTranDo struct{ gen.DO }

func (a agentTranDo) Debug() *agentTranDo {
	return a.withDO(a.DO.Debug())
}

func (a agentTranDo) WithContext(ctx context.Context) *agentTranDo {
	return a.withDO(a.DO.WithContext(ctx))
}

func (a agentTranDo) ReadDB() *agentTranDo {
	return a.Clauses(dbresolver.Read)
}

func (a agentTranDo) WriteDB() *agentTranDo {
	return a.Clauses(dbresolver.Write)
}

func (a agentTranDo) Session(config *gorm.Session) *agentTranDo {
	return a.withDO(a.DO.Session(config))
}

func (a agentTranDo) Clauses(conds ...clause.Expression) *agentTranDo {
	return a.withDO(a.DO.Clauses(conds...))
}

func (a agentTranDo) Returning(value interface{}, columns ...string) *agentTranDo {
	return a.withDO(a.DO.Returning(value, columns...))
}

func (a agentTranDo) Not(conds ...gen.Condition) *agentTranDo {
	return a.withDO(a.DO.Not(conds...))
}

func (a agentTranDo) Or(conds ...gen.Condition) *agentTranDo {
	return a.withDO(a.DO.Or(conds...))
}

func (a agentTranDo) Select(conds ...field.Expr) *agentTranDo {
	return a.withDO(a.DO.Select(conds...))
}

func (a agentTranDo) Where(conds ...gen.Condition) *agentTranDo {
	return a.withDO(a.DO.Where(conds...))
}

func (a agentTranDo) Exists(subquery interface{ UnderlyingDB() *gorm.DB }) *agentTranDo {
	return a.Where(field.CompareSubQuery(field.ExistsOp, nil, subquery.UnderlyingDB()))
}

func (a agentTranDo) Order(conds ...field.Expr) *agentTranDo {
	return a.withDO(a.DO.Order(conds...))
}

func (a agentTranDo) Distinct(cols ...field.Expr) *agentTranDo {
	return a.withDO(a.DO.Distinct(cols...))
}

func (a agentTranDo) Omit(cols ...field.Expr) *agentTranDo {
	return a.withDO(a.DO.Omit(cols...))
}

func (a agentTranDo) Join(table schema.Tabler, on ...field.Expr) *agentTranDo {
	return a.withDO(a.DO.Join(table, on...))
}

func (a agentTranDo) LeftJoin(table schema.Tabler, on ...field.Expr) *agentTranDo {
	return a.withDO(a.DO.LeftJoin(table, on...))
}

func (a agentTranDo) RightJoin(table schema.Tabler, on ...field.Expr) *agentTranDo {
	return a.withDO(a.DO.RightJoin(table, on...))
}

func (a agentTranDo) Group(cols ...field.Expr) *agentTranDo {
	return a.withDO(a.DO.Group(cols...))
}

func (a agentTranDo) Having(conds ...gen.Condition) *agentTranDo {
	return a.withDO(a.DO.Having(conds...))
}

func (a agentTranDo) Limit(limit int) *agentTranDo {
	return a.withDO(a.DO.Limit(limit))
}

func (a agentTranDo) Offset(offset int) *agentTranDo {
	return a.withDO(a.DO.Offset(offset))
}

func (a agentTranDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *agentTranDo {
	return a.withDO(a.DO.Scopes(funcs...))
}

func (a agentTranDo) Unscoped() *agentTranDo {
	return a.withDO(a.DO.Unscoped())
}

func (a agentTranDo) Create(values ...*entity.AgentTran) error {
	if len(values) == 0 {
		return nil
	}
	return a.DO.Create(values)
}

func (a agentTranDo) CreateInBatches(values []*entity.AgentTran, batchSize int) error {
	return a.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (a agentTranDo) Save(values ...*entity.AgentTran) error {
	if len(values) == 0 {
		return nil
	}
	return a.DO.Save(values)
}

func (a agentTranDo) First() (*entity.AgentTran, error) {
	if result, err := a.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*entity.AgentTran), nil
	}
}

func (a agentTranDo) Take() (*entity.AgentTran, error) {
	if result, err := a.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*entity.AgentTran), nil
	}
}

func (a agentTranDo) Last() (*entity.AgentTran, error) {
	if result, err := a.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*entity.AgentTran), nil
	}
}

func (a agentTranDo) Find() ([]*entity.AgentTran, error) {
	result, err := a.DO.Find()
	return result.([]*entity.AgentTran), err
}

func (a agentTranDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*entity.AgentTran, err error) {
	buf := make([]*entity.AgentTran, 0, batchSize)
	err = a.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (a agentTranDo) FindInBatches(result *[]*entity.AgentTran, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return a.DO.FindInBatches(result, batchSize, fc)
}

func (a agentTranDo) Attrs(attrs ...field.AssignExpr) *agentTranDo {
	return a.withDO(a.DO.Attrs(attrs...))
}

func (a agentTranDo) Assign(attrs ...field.AssignExpr) *agentTranDo {
	return a.withDO(a.DO.Assign(attrs...))
}

func (a agentTranDo) Joins(fields ...field.RelationField) *agentTranDo {
	for _, _f := range fields {
		a = *a.withDO(a.DO.Joins(_f))
	}
	return &a
}

func (a agentTranDo) Preload(fields ...field.RelationField) *agentTranDo {
	for _, _f := range fields {
		a = *a.withDO(a.DO.Preload(_f))
	}
	return &a
}

func (a agentTranDo) FirstOrInit() (*entity.AgentTran, error) {
	if result, err := a.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*entity.AgentTran), nil
	}
}

func (a agentTranDo) FirstOrCreate() (*entity.AgentTran, error) {
	if result, err := a.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*entity.AgentTran), nil
	}
}

func (a agentTranDo) FindByPage(offset int, limit int) (result []*entity.AgentTran, count int64, err error) {
	result, err = a.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = a.Offset(-1).Limit(-1).Count()
	return
}

func (a agentTranDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = a.Count()
	if err != nil {
		return
	}

	err = a.Offset(offset).Limit(limit).Scan(result)
	return
}

func (a agentTranDo) Scan(result interface{}) (err error) {
	return a.DO.Scan(result)
}

func (a agentTranDo) Delete(models ...*entity.AgentTran) (result gen.ResultInfo, err error) {
	return a.DO.Delete(models)
}

func (a *agentTranDo) withDO(do gen.Dao) *agentTranDo {
	a.DO = *do.(*gen.DO)
	return a
}
