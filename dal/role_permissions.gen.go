// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package dal

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"Mapi/model/entity"
)

func newRolePermission(db *gorm.DB, opts ...gen.DOOption) rolePermission {
	_rolePermission := rolePermission{}

	_rolePermission.rolePermissionDo.UseDB(db, opts...)
	_rolePermission.rolePermissionDo.UseModel(&entity.RolePermission{})

	tableName := _rolePermission.rolePermissionDo.TableName()
	_rolePermission.ALL = field.NewAsterisk(tableName)
	_rolePermission.ID = field.NewInt32(tableName, "id")
	_rolePermission.Roleid = field.NewInt32(tableName, "roleid")
	_rolePermission.Permissionsid = field.NewInt32(tableName, "permissionsid")
	_rolePermission.Ctime = field.NewInt32(tableName, "ctime")
	_rolePermission.DelFlg = field.NewInt32(tableName, "del_flg")

	_rolePermission.fillFieldMap()

	return _rolePermission
}

type rolePermission struct {
	rolePermissionDo

	ALL           field.Asterisk
	ID            field.Int32
	Roleid        field.Int32 // 角色表id
	Permissionsid field.Int32 // 权限表id
	Ctime         field.Int32
	DelFlg        field.Int32

	fieldMap map[string]field.Expr
}

func (r rolePermission) Table(newTableName string) *rolePermission {
	r.rolePermissionDo.UseTable(newTableName)
	return r.updateTableName(newTableName)
}

func (r rolePermission) As(alias string) *rolePermission {
	r.rolePermissionDo.DO = *(r.rolePermissionDo.As(alias).(*gen.DO))
	return r.updateTableName(alias)
}

func (r *rolePermission) updateTableName(table string) *rolePermission {
	r.ALL = field.NewAsterisk(table)
	r.ID = field.NewInt32(table, "id")
	r.Roleid = field.NewInt32(table, "roleid")
	r.Permissionsid = field.NewInt32(table, "permissionsid")
	r.Ctime = field.NewInt32(table, "ctime")
	r.DelFlg = field.NewInt32(table, "del_flg")

	r.fillFieldMap()

	return r
}

func (r *rolePermission) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := r.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (r *rolePermission) fillFieldMap() {
	r.fieldMap = make(map[string]field.Expr, 5)
	r.fieldMap["id"] = r.ID
	r.fieldMap["roleid"] = r.Roleid
	r.fieldMap["permissionsid"] = r.Permissionsid
	r.fieldMap["ctime"] = r.Ctime
	r.fieldMap["del_flg"] = r.DelFlg
}

func (r rolePermission) clone(db *gorm.DB) rolePermission {
	r.rolePermissionDo.ReplaceConnPool(db.Statement.ConnPool)
	return r
}

func (r rolePermission) replaceDB(db *gorm.DB) rolePermission {
	r.rolePermissionDo.ReplaceDB(db)
	return r
}

type rolePermissionDo struct{ gen.DO }

func (r rolePermissionDo) Debug() *rolePermissionDo {
	return r.withDO(r.DO.Debug())
}

func (r rolePermissionDo) WithContext(ctx context.Context) *rolePermissionDo {
	return r.withDO(r.DO.WithContext(ctx))
}

func (r rolePermissionDo) ReadDB() *rolePermissionDo {
	return r.Clauses(dbresolver.Read)
}

func (r rolePermissionDo) WriteDB() *rolePermissionDo {
	return r.Clauses(dbresolver.Write)
}

func (r rolePermissionDo) Session(config *gorm.Session) *rolePermissionDo {
	return r.withDO(r.DO.Session(config))
}

func (r rolePermissionDo) Clauses(conds ...clause.Expression) *rolePermissionDo {
	return r.withDO(r.DO.Clauses(conds...))
}

func (r rolePermissionDo) Returning(value interface{}, columns ...string) *rolePermissionDo {
	return r.withDO(r.DO.Returning(value, columns...))
}

func (r rolePermissionDo) Not(conds ...gen.Condition) *rolePermissionDo {
	return r.withDO(r.DO.Not(conds...))
}

func (r rolePermissionDo) Or(conds ...gen.Condition) *rolePermissionDo {
	return r.withDO(r.DO.Or(conds...))
}

func (r rolePermissionDo) Select(conds ...field.Expr) *rolePermissionDo {
	return r.withDO(r.DO.Select(conds...))
}

func (r rolePermissionDo) Where(conds ...gen.Condition) *rolePermissionDo {
	return r.withDO(r.DO.Where(conds...))
}

func (r rolePermissionDo) Exists(subquery interface{ UnderlyingDB() *gorm.DB }) *rolePermissionDo {
	return r.Where(field.CompareSubQuery(field.ExistsOp, nil, subquery.UnderlyingDB()))
}

func (r rolePermissionDo) Order(conds ...field.Expr) *rolePermissionDo {
	return r.withDO(r.DO.Order(conds...))
}

func (r rolePermissionDo) Distinct(cols ...field.Expr) *rolePermissionDo {
	return r.withDO(r.DO.Distinct(cols...))
}

func (r rolePermissionDo) Omit(cols ...field.Expr) *rolePermissionDo {
	return r.withDO(r.DO.Omit(cols...))
}

func (r rolePermissionDo) Join(table schema.Tabler, on ...field.Expr) *rolePermissionDo {
	return r.withDO(r.DO.Join(table, on...))
}

func (r rolePermissionDo) LeftJoin(table schema.Tabler, on ...field.Expr) *rolePermissionDo {
	return r.withDO(r.DO.LeftJoin(table, on...))
}

func (r rolePermissionDo) RightJoin(table schema.Tabler, on ...field.Expr) *rolePermissionDo {
	return r.withDO(r.DO.RightJoin(table, on...))
}

func (r rolePermissionDo) Group(cols ...field.Expr) *rolePermissionDo {
	return r.withDO(r.DO.Group(cols...))
}

func (r rolePermissionDo) Having(conds ...gen.Condition) *rolePermissionDo {
	return r.withDO(r.DO.Having(conds...))
}

func (r rolePermissionDo) Limit(limit int) *rolePermissionDo {
	return r.withDO(r.DO.Limit(limit))
}

func (r rolePermissionDo) Offset(offset int) *rolePermissionDo {
	return r.withDO(r.DO.Offset(offset))
}

func (r rolePermissionDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *rolePermissionDo {
	return r.withDO(r.DO.Scopes(funcs...))
}

func (r rolePermissionDo) Unscoped() *rolePermissionDo {
	return r.withDO(r.DO.Unscoped())
}

func (r rolePermissionDo) Create(values ...*entity.RolePermission) error {
	if len(values) == 0 {
		return nil
	}
	return r.DO.Create(values)
}

func (r rolePermissionDo) CreateInBatches(values []*entity.RolePermission, batchSize int) error {
	return r.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (r rolePermissionDo) Save(values ...*entity.RolePermission) error {
	if len(values) == 0 {
		return nil
	}
	return r.DO.Save(values)
}

func (r rolePermissionDo) First() (*entity.RolePermission, error) {
	if result, err := r.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*entity.RolePermission), nil
	}
}

func (r rolePermissionDo) Take() (*entity.RolePermission, error) {
	if result, err := r.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*entity.RolePermission), nil
	}
}

func (r rolePermissionDo) Last() (*entity.RolePermission, error) {
	if result, err := r.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*entity.RolePermission), nil
	}
}

func (r rolePermissionDo) Find() ([]*entity.RolePermission, error) {
	result, err := r.DO.Find()
	return result.([]*entity.RolePermission), err
}

func (r rolePermissionDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*entity.RolePermission, err error) {
	buf := make([]*entity.RolePermission, 0, batchSize)
	err = r.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (r rolePermissionDo) FindInBatches(result *[]*entity.RolePermission, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return r.DO.FindInBatches(result, batchSize, fc)
}

func (r rolePermissionDo) Attrs(attrs ...field.AssignExpr) *rolePermissionDo {
	return r.withDO(r.DO.Attrs(attrs...))
}

func (r rolePermissionDo) Assign(attrs ...field.AssignExpr) *rolePermissionDo {
	return r.withDO(r.DO.Assign(attrs...))
}

func (r rolePermissionDo) Joins(fields ...field.RelationField) *rolePermissionDo {
	for _, _f := range fields {
		r = *r.withDO(r.DO.Joins(_f))
	}
	return &r
}

func (r rolePermissionDo) Preload(fields ...field.RelationField) *rolePermissionDo {
	for _, _f := range fields {
		r = *r.withDO(r.DO.Preload(_f))
	}
	return &r
}

func (r rolePermissionDo) FirstOrInit() (*entity.RolePermission, error) {
	if result, err := r.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*entity.RolePermission), nil
	}
}

func (r rolePermissionDo) FirstOrCreate() (*entity.RolePermission, error) {
	if result, err := r.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*entity.RolePermission), nil
	}
}

func (r rolePermissionDo) FindByPage(offset int, limit int) (result []*entity.RolePermission, count int64, err error) {
	result, err = r.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = r.Offset(-1).Limit(-1).Count()
	return
}

func (r rolePermissionDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = r.Count()
	if err != nil {
		return
	}

	err = r.Offset(offset).Limit(limit).Scan(result)
	return
}

func (r rolePermissionDo) Scan(result interface{}) (err error) {
	return r.DO.Scan(result)
}

func (r rolePermissionDo) Delete(models ...*entity.RolePermission) (result gen.ResultInfo, err error) {
	return r.DO.Delete(models)
}

func (r *rolePermissionDo) withDO(do gen.Dao) *rolePermissionDo {
	r.DO = *do.(*gen.DO)
	return r
}
