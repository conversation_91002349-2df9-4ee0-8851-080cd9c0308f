<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>USDT代收代付API接口文档</title>
    <style>
        :root {
            --primary-color: #4a6ee0;
            --secondary-color: #6c757d;
            --success-color: #28a745;
            --info-color: #17a2b8;
            --warning-color: #ffc107;
            --danger-color: #dc3545;
            --light-color: #f8f9fa;
            --dark-color: #343a40;
            --border-color: #e9ecef;
            --code-bg: #f5f7ff;
            --hover-color: #eaefff;
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f8f9fa;
            display: flex;
            min-height: 100vh;
        }
        
        .sidebar {
            width: 280px;
            background-color: #fff;
            border-right: 1px solid var(--border-color);
            position: fixed;
            left: 0;
            top: 0;
            bottom: 0;
            overflow-y: auto;
            z-index: 100;
            box-shadow: 0 0 10px rgba(0,0,0,0.05);
        }
        
        .sidebar-header {
            padding: 20px;
            color: white;
        }
        
        .content {
            flex-grow: 1;
            margin-left: 280px;
            padding: 30px;
            max-width: 1200px;
        }
        
        @media (max-width: 768px) {
            body {
                flex-direction: column;
            }
            
            .sidebar {
                width: 100%;
                position: relative;
                height: auto;
                border-right: none;
                border-bottom: 1px solid var(--border-color);
            }
            
            .content {
                margin-left: 0;
                padding: 20px;
            }
        }
        
        .nav-menu {
            list-style-type: none;
            padding: 0;
        }
        
        .nav-menu li {
            padding: 10px 20px;
            border-bottom: 1px solid var(--border-color);
        }
        
        .nav-menu li a {
            color: #333;
            text-decoration: none;
            display: block;
            transition: color 0.2s;
        }
        
        .nav-menu li a:hover {
            color: var(--primary-color);
        }
        
        .nav-menu .sub-menu {
            list-style-type: none;
            padding-left: 20px;
            margin-top: 10px;
        }
        
        .nav-menu .sub-menu li {
            padding: 5px 0;
            border-bottom: none;
        }
        
        h1, h2, h3, h4, h5, h6 {
            margin-top: 24px;
            margin-bottom: 16px;
            font-weight: 600;
            line-height: 1.25;
            color: #333;
        }
        
        h1 {
            font-size: 2em;
            padding-bottom: 0.3em;
            border-bottom: 1px solid var(--border-color);
        }
        
        h2 {
            font-size: 1.5em;
            padding-bottom: 0.3em;
            border-bottom: 1px solid var(--border-color);
        }
        
        h3 {
            font-size: 1.25em;
        }
        
        p {
            margin-bottom: 16px;
        }
        
        .section {
            margin-bottom: 40px;
            background-color: white;
            padding: 25px;
            border-radius: 5px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.05);
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        
        table, th, td {
            border: 1px solid var(--border-color);
        }
        
        th, td {
            padding: 12px 15px;
            text-align: left;
        }
        
        th {
            background-color: var(--light-color);
            font-weight: 600;
        }
        
        tr:nth-child(even) {
            background-color: #fafafa;
        }
        
        tr:hover {
            background-color: var(--hover-color);
        }
        
        pre {
            background-color: var(--code-bg);
            border-radius: 5px;
            padding: 15px;
            overflow: auto;
            margin: 20px 0;
            font-family: SFMono-Regular, Consolas, "Liberation Mono", Menlo, monospace;
            font-size: 0.9em;
        }
        
        code {
            font-family: SFMono-Regular, Consolas, "Liberation Mono", Menlo, monospace;
            background-color: var(--code-bg);
            padding: 2px 4px;
            border-radius: 3px;
            font-size: 0.9em;
        }
        
        pre code {
            background-color: transparent;
            padding: 0;
        }
        
        .note {
            padding: 15px;
            margin: 20px 0;
            border-left: 4px solid var(--primary-color);
            background-color: #f8f9fa;
        }
        
        .warning {
            padding: 15px;
            margin: 20px 0;
            border-left: 4px solid var(--warning-color);
            background-color: #fff8e6;
        }
        
        .api-example {
            margin: 20px 0;
            border: 1px solid var(--border-color);
            border-radius: 5px;
            overflow: hidden;
        }
        
        .api-example-header {
            padding: 10px 15px;
            background-color: var(--code-bg);
            border-bottom: 1px solid var(--border-color);
            font-weight: 600;
        }
        
        .api-example-body {
            padding: 15px;
            background-color: white;
        }
        
        .badge {
            display: inline-block;
            padding: 3px 7px;
            font-size: 0.75em;
            font-weight: 600;
            border-radius: 3px;
            margin-right: 5px;
            color: white;
        }
        
        .badge-primary {
            background-color: var(--primary-color);
        }
        
        .badge-success {
            background-color: var(--success-color);
        }
        
        .badge-warning {
            background-color: var(--warning-color);
            color: #333;
        }
        
        .badge-info {
            background-color: var(--info-color);
        }
        
        .back-to-top {
            position: fixed;
            bottom: 20px;
            right: 20px;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background-color: var(--primary-color);
            color: white;
            text-align: center;
            line-height: 40px;
            cursor: pointer;
            display: none;
            box-shadow: 0 2px 5px rgba(0,0,0,0.2);
            z-index: 1000;
            text-decoration: none;
        }
        
        .back-to-top:hover {
            background-color: #3958c7;
        }
    </style>
</head>
<body>
    <div class="sidebar">
        <div class="sidebar-header">
            <h1>USDT代收代付</h1>
            <h2>API接口文档</h2>
        </div>
        <ul class="nav-menu">
            <li><a href="#intro">接口概述</a></li>
            <li>
                <a href="#payment">收款接口</a>
                <ul class="sub-menu">
                    <li><a href="#create-payment">收款下单接口</a></li>
                    <li><a href="#query-payment">查询收款订单</a></li>
                </ul>
            </li>
            <li>
                <a href="#payout">下发接口</a>
                <ul class="sub-menu">
                    <li><a href="#create-payout">下发下单接口</a></li>
                    <li><a href="#query-payout">查询下发订单</a></li>
                </ul>
            </li>
        </ul>
    </div>
    
    <div class="content">
        <section id="intro" class="section">
            <h1>接口概述</h1>
            <h2>1. 接口请求方式</h2>
            <ul>
                <li>GET方式提交数据，仅支持HTTPS</li>
                <li>Appid和appkey在后台获取</li>
                <li>接口地址: <code>https://api.paygram.pro</code></li>
            </ul>
        </section>
        
        <section id="payment" class="section">
            <h1>收款接口</h1>
            
            <div id="create-payment">
                <h2>2. 收款下单接口</h2>
                <p>请求URL: <code>/Api/Order</code></p>
                
                <h3>请求报文:</h3>
                <table>
                    <thead>
                        <tr>
                            <th>字段名称</th>
                            <th>字段描述</th>
                            <th>必填</th>
                            <th>备注</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>memberid</td>
                            <td>商户ID</td>
                            <td>√</td>
                            <td>平台分配商户ID</td>
                        </tr>
                        <tr>
                            <td>money</td>
                            <td>金额</td>
                            <td>√</td>
                            <td>最低10U起</td>
                        </tr>
                        <tr>
                            <td>rorderid</td>
                            <td>商户订单号</td>
                            <td>√</td>
                            <td></td>
                        </tr>
                        <tr>
                            <td>callbackurl</td>
                            <td>回调地址</td>
                            <td></td>
                            <td></td>
                        </tr>
                        <tr>
                            <td>returnurl</td>
                            <td>收银台跳转地址</td>
                            <td></td>
                            <td></td>
                        </tr>
                        <tr>
                            <td>chain</td>
                            <td>链</td>
                            <td></td>
                            <td>Trc,Erc,Bsc 默认Trc</td>
                        </tr>
                        <tr>
                            <td>sign</td>
                            <td>签名</td>
                            <td>√</td>
                            <td></td>
                        </tr>
                    </tbody>
                </table>
                
                <p>验签方式: <code>Md5(memberid + money + rorderid + appkey)</code></p>
                
                <div class="api-example">
                    <div class="api-example-header">返回例子：</div>
                    <div class="api-example-body">
                        <pre><code>{
    "code": true,
    "data": {
        "cashierurl": "https://gateway.paygram.pro/cashier?orderid=C-20230617172952-Trc HYPERLINK "https://gateway.paygram.pro/cashier?orderid=C-20230617172952-Trc&chain=Trc20"& HYPERLINK "https://gateway.paygram.pro/cashier?orderid=C-20230617172952-Trc&chain=Trc20"chain=Trc20",
        "money": "10",
        "orderid": "C-20230617172952-Trc",
        "rorderid": "T68",
        "trxaddr": "TWvNAx474kX1fvus9x2LYLtnfKguVrpft9"
    },
    "msg": "创建订单成功"
}</code></pre>
                    </div>
                </div>
                
                <p><code>code</code> 为 <code>true</code> 表示成功</p>
                
                <h3>data包含参数:</h3>
                <table>
                    <thead>
                        <tr>
                            <th>字段名称</th>
                            <th>字段描述</th>
                            <th>备注</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>cashierurl</td>
                            <td>收银台地址</td>
                            <td>收银台url</td>
                        </tr>
                        <tr>
                            <td>orderid</td>
                            <td>系统订单号</td>
                            <td>系统唯一订单号</td>
                        </tr>
                        <tr>
                            <td>rorderid</td>
                            <td>商户订单号</td>
                            <td>下单提交的订单号</td>
                        </tr>
                        <tr>
                            <td>trxaddr</td>
                            <td>收款地址</td>
                            <td></td>
                        </tr>
                    </tbody>
                </table>
                
                <h3>成功回调返回参数</h3>
                <table>
                    <thead>
                        <tr>
                            <th>字段名称</th>
                            <th>字段描述</th>
                            <th>备注</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>memberid</td>
                            <td>商户ID</td>
                            <td>平台分配商户ID</td>
                        </tr>
                        <tr>
                            <td>orderid</td>
                            <td>系统订单号</td>
                            <td>系统唯一订单号</td>
                        </tr>
                        <tr>
                            <td>rorderid</td>
                            <td>商户订单号</td>
                            <td>下单提交的订单号</td>
                        </tr>
                        <tr>
                            <td>money</td>
                            <td>金额</td>
                            <td></td>
                        </tr>
                        <tr>
                            <td>sign</td>
                            <td>签名</td>
                            <td></td>
                        </tr>
                    </tbody>
                </table>
                
                <p>验签方式：<code>Md5(memberid + orderid + rorderid + money + appkey)</code></p>
                
                <div class="note">
                    <p>回调通过GET发出，成功返回ok，验证失败返回任意内容。回调默认回调5次，超过5次不会自动回调，商户后台可手动补发回调。</p>
                    <p>订单超时不会发出回调。</p>
                </div>
            </div>
            
            <div id="query-payment">
                <h2>3. 查询收款订单</h2>
                <p>请求URL: <code>/Api/Query</code></p>
                
                <h3>请求报文:</h3>
                <table>
                    <thead>
                        <tr>
                            <th>字段名称</th>
                            <th>字段描述</th>
                            <th>必填</th>
                            <th>备注</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>memberid</td>
                            <td>商户ID</td>
                            <td>√</td>
                            <td>平台分配商户ID</td>
                        </tr>
                        <tr>
                            <td>orderid</td>
                            <td>系统订单号</td>
                            <td>√</td>
                            <td>平台分配的唯一订单号</td>
                        </tr>
                        <tr>
                            <td>sign</td>
                            <td>签名</td>
                            <td>√</td>
                            <td></td>
                        </tr>
                    </tbody>
                </table>
                
                <p>验签方式: <code>Md5(memberid + orderid + appkey)</code></p>
                
                <div class="api-example">
                    <div class="api-example-header">返回例子：</div>
                    <div class="api-example-body">
                        <pre><code>{
    "code": true,
    "data": {
        "ctime": 1686994193,
        "etime": null,
        "orderid": "C-20230617172952-Trc",
        "rorderid": "T68",
        "status": "创建订单成功",
        "trxaddr": "TWvNAx474kX1fvus9x2LYLtnfKguVrpft9"
    },
    "msg": "查询成功"
}</code></pre>
                    </div>
                </div>
                
                <p><code>code</code> 为 <code>true</code> 表示成功</p>
                
                <h3>data包含参数:</h3>
                <table>
                    <thead>
                        <tr>
                            <th>字段名称</th>
                            <th>字段描述</th>
                            <th>备注</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>ctime</td>
                            <td>订单创建时间戳</td>
                            <td></td>
                        </tr>
                        <tr>
                            <td>etime</td>
                            <td>订单完成时间戳</td>
                            <td></td>
                        </tr>
                        <tr>
                            <td>orderid</td>
                            <td>系统订单号</td>
                            <td>系统唯一订单号</td>
                        </tr>
                        <tr>
                            <td>rorderid</td>
                            <td>商户订单号</td>
                            <td>下单提交的订单号</td>
                        </tr>
                        <tr>
                            <td>trxaddr</td>
                            <td>收款地址</td>
                            <td></td>
                        </tr>
                        <tr>
                            <td>status</td>
                            <td>订单状态</td>
                            <td>创建订单成功,订单超时,支付成功,回调成功,手动成功</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </section>
        
        <section id="payout" class="section">
            <h1>下发接口</h1>
            
            <div id="create-payout">
                <h2>4. 下发下单接口</h2>
                <p>请求URL: <code>/Api/PayOrder</code></p>
                
                <h3>请求报文:</h3>
                <table>
                    <thead>
                        <tr>
                            <th>字段名称</th>
                            <th>字段描述</th>
                            <th>必填</th>
                            <th>备注</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>memberid</td>
                            <td>商户ID</td>
                            <td>√</td>
                            <td>平台分配商户ID</td>
                        </tr>
                        <tr>
                            <td>money</td>
                            <td>金额</td>
                            <td>√</td>
                            <td>最低10U起</td>
                        </tr>
                        <tr>
                            <td>rorderid</td>
                            <td>商户订单号</td>
                            <td>√</td>
                            <td></td>
                        </tr>
                        <tr>
                            <td>callbackurl</td>
                            <td>回调地址</td>
                            <td></td>
                            <td></td>
                        </tr>
                        <tr>
                            <td>address</td>
                            <td>收款地址</td>
                            <td>√</td>
                            <td></td>
                        </tr>
                        <tr>
                            <td>chain</td>
                            <td>链</td>
                            <td></td>
                            <td>Trc,Erc,Bsc,如需下发Bsc链则必填</td>
                        </tr>
                        <tr>
                            <td>sign</td>
                            <td>签名</td>
                            <td>√</td>
                            <td></td>
                        </tr>
                    </tbody>
                </table>
                
                <p>验签方式: <code>Md5(address + memberid + money + rorderid + appkey)</code></p>
                
                <div class="api-example">
                    <div class="api-example-header">返回例子：</div>
                    <div class="api-example-body">
                        <pre><code>{
    "code": true,
    "data": {
        "address": "TVd3neGbe2Ef3eqQGMyJbZbqxitAQkQdQB",
        "money": "10",
        "orderid": "P-20230617171053-Trc",
        "rorderid": "T67"
    },
    "msg": "创建订单成功"
}</code></pre>
                    </div>
                </div>
                
                <p><code>code</code> 为 <code>true</code> 表示成功</p>
                
                <h3>data包含参数:</h3>
                <table>
                    <thead>
                        <tr>
                            <th>字段名称</th>
                            <th>字段描述</th>
                            <th>备注</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>address</td>
                            <td>收款地址</td>
                            <td></td>
                        </tr>
                        <tr>
                            <td>orderid</td>
                            <td>系统订单号</td>
                            <td>系统唯一订单号</td>
                        </tr>
                        <tr>
                            <td>rorderid</td>
                            <td>商户订单号</td>
                            <td>下单提交的订单号</td>
                        </tr>
                        <tr>
                            <td>money</td>
                            <td>下发金额</td>
                            <td>单位:U</td>
                        </tr>
                    </tbody>
                </table>
                
                <h3>成功回调返回参数</h3>
                <table>
                    <thead>
                        <tr>
                            <th>字段名称</th>
                            <th>字段描述</th>
                            <th>备注</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>memberid</td>
                            <td>商户ID</td>
                            <td>平台分配商户ID</td>
                        </tr>
                        <tr>
                            <td>orderid</td>
                            <td>系统订单号</td>
                            <td>系统唯一订单号</td>
                        </tr>
                        <tr>
                            <td>rorderid</td>
                            <td>商户订单号</td>
                            <td>下单提交的订单号</td>
                        </tr>
                        <tr>
                            <td>money</td>
                            <td>金额</td>
                            <td></td>
                        </tr>
                        <tr>
                            <td>sign</td>
                            <td>签名</td>
                            <td></td>
                        </tr>
                    </tbody>
                </table>
                
                <p>验签方式：<code>Md5(memberid + orderid + rorderid + money + appkey)</code></p>
                
                <div class="warning">
                    <p>回调通过GET发出，成功返回ok，验证失败返回任意内容。回调默认回调5次，超过5次不会自动回调，商户后台可手动补发回调。</p>
                    <p>转账失败或订单超时不发出回调。</p>
                </div>
            </div>
            
            <div id="query-payout">
                <h2>5. 查询下发订单</h2>
                <p>请求URL: <code>/Api/PayQuery</code></p>
                
                <h3>请求报文:</h3>
                <table>
                    <thead>
                        <tr>
                            <th>字段名称</th>
                            <th>字段描述</th>
                            <th>必填</th>
                            <th>备注</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>memberid</td>
                            <td>商户ID</td>
                            <td>√</td>
                            <td>平台分配商户ID</td>
                        </tr>
                        <tr>
                            <td>orderid</td>
                            <td>系统订单号</td>
                            <td>√</td>
                            <td>平台分配的唯一订单号</td>
                        </tr>
                        <tr>
                            <td>sign</td>
                            <td>签名</td>
                            <td>√</td>
                            <td></td>
                        </tr>
                    </tbody>
                </table>
                
                <p>验签方式: <code>Md5(memberid + orderid + appkey)</code></p>
                
                <div class="api-example">
                    <div class="api-example-header">返回例子：</div>
                    <div class="api-example-body">
                        <pre><code>{
    "code": true,
    "data": {
        "address": "TVd3neGbe2Ef3eqQGMyJbZbqxitAQkQdQB",
        "ctime": 1686993053,
        "etime": 1686993058,
        "orderid": "P-20230617171053-Trc",
        "rorderid": "T67",
        "status": "转账成功"
    },
    "msg": "查询成功"
}</code></pre>
                    </div>
                </div>
                
                <p><code>code</code> 为 <code>true</code> 表示成功</p>
                
                <h3>data包含参数:</h3>
                <table>
                    <thead>
                        <tr>
                            <th>字段名称</th>
                            <th>字段描述</th>
                            <th>备注</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>ctime</td>
                            <td>订单创建时间戳</td>
                            <td></td>
                        </tr>
                        <tr>
                            <td>etime</td>
                            <td>订单完成时间戳</td>
                            <td></td>
                        </tr>
                        <tr>
                            <td>orderid</td>
                            <td>系统订单号</td>
                            <td>系统唯一订单号</td>
                        </tr>
                        <tr>
                            <td>rorderid</td>
                            <td>商户订单号</td>
                            <td>下单提交的订单号</td>
                        </tr>
                        <tr>
                            <td>status</td>
                            <td>订单状态</td>
                            <td>创建订单成功,订单超时,转账成功,回调成功,手动成功,转账失败</td>
                        </tr>
                        <tr>
                            <td>address</td>
                            <td>收款地址</td>
                            <td></td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </section>
    </div>
    
    <a href="#" class="back-to-top">↑</a>
    
    <script>
        // 返回顶部按钮
        window.onscroll = function() {
            if (document.body.scrollTop > 20 || document.documentElement.scrollTop > 20) {
                document.querySelector('.back-to-top').style.display = "block";
            } else {
                document.querySelector('.back-to-top').style.display = "none";
            }
        };
        
        document.querySelector('.back-to-top').addEventListener('click', function(e) {
            e.preventDefault();
            document.body.scrollTop = 0; // For Safari
            document.documentElement.scrollTop = 0; // For Chrome, Firefox, IE and Opera
        });
        
        // 侧边栏切换
        document.querySelectorAll('.nav-menu a').forEach(function(link) {
            link.addEventListener('click', function(e) {
                // 在小屏幕上，点击菜单项后折叠侧边栏
                if (window.innerWidth <= 768) {
                    document.querySelector('.sidebar').classList.toggle('collapsed');
                }
            });
        });
    </script>
</body>
</html> 