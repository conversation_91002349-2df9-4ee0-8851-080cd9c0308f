package main

import (
	"encoding/json"
	"fmt"
	"net/http"
	"sync"
	"time"
	"trontool/lib"
	"trontool/tron"

	"github.com/gorilla/websocket"
)

type Client struct {
	ID   string
	Conn *websocket.Conn
	Lock sync.Mutex
}

var upgrader = websocket.Upgrader{
	// 自定义 CheckOrigin 函数，允许所有跨域请求
	CheckOrigin: func(r *http.Request) bool {
		return true
	},
}

var clients = make(map[string]*Client)
var clientsMutex sync.Mutex

func WsServer() {
	http.HandleFunc("/ws", wsHandler)
	fmt.Println("WebSocket server is running on :8800")
	http.ListenAndServe(":8800", nil)
}

func wsHandler(w http.ResponseWriter, r *http.Request) {
	conn, err := upgrader.Upgrade(w, r, nil)
	if err != nil {
		fmt.Println("Upgrade error:", err)
		return
	}
	defer conn.Close()

	clientID := r.URL.Query().Get("id")
	client := &Client{ID: clientID, Conn: conn}
	clientsMutex.Lock()
	clients[clientID] = client
	clientsMutex.Unlock()

	for {
		_, message, err := conn.ReadMessage()
		if err != nil {
			break
		}
		var data map[string]string
		fmt.Println("收到消息:" + string(message))
		json.Unmarshal(message, &data)
		if data["type"] == "" || data["address"] == "" {
			continue
		}
		switch data["type"] {
		case "addtrc":
			addr := data["address"]
			check, _ := lib.PDO().Select("select trxaddr from trxaddr where trxaddr=? and userid=? and del_flg=0;", addr, clientID)
			if check["trxaddr"] != nil {
				continue
			}
			hexaddr, e := tron.DecodeBase58Address(addr)
			if e != nil {
				continue
			}
			lib.PDO().Exec("INSERT INTO `trxaddr` (`trxaddr`,`hextrxaddr`,`userid`,`remark`,`status`,`ctime`,`del_flg`) VALUES (?,?,?,?,0,?,0);", addr, hexaddr, clientID, "", time.Now().Unix())
			if lib.SliceContains(lib.Addr, hexaddr) == false {
				lib.Addr = append(lib.Addr, hexaddr)
			}
		case "adderc":
			addr := data["address"]
			check, _ := lib.PDO().Select("select trxaddr from trxaddr where trxaddr=? and userid=? and del_flg=0;", addr, clientID)
			if check["trxaddr"] != nil {
				continue
			}
			lib.PDO().Exec("INSERT INTO `trxaddr` (`trxaddr`,`hextrxaddr`,`userid`,`remark`,`status`,`ctime`,`del_flg`) VALUES (?,?,?,?,0,?,0);", addr, addr, clientID, "", time.Now().Unix())
			if lib.SliceContains(lib.Addr, addr) == false {
				lib.Addr = append(lib.Addr, addr)
			}
		case "del":
			addr := data["address"]
			tokenInfo, _ := lib.PDO().Select("select id,trxaddr,hextrxaddr,status,remark,id from trxaddr where trxaddr=? and userid=? and del_flg=0", addr, clientID)
			if tokenInfo["trxaddr"] == nil {
				continue
			}
			lib.PDO().Exec("update trxaddr set `del_flg`=1 where id=?;", tokenInfo["id"].(int64))
			checkall, _ := lib.PDO().Select("select trxaddr,id from trxaddr where trxaddr=? and del_flg=0 and userid!=?;", tokenInfo["trxaddr"].(string), clientID)
			if checkall["trxaddr"] == nil {
				hexaddr, _ := tokenInfo["hextrxaddr"].(string)
				lib.Addr = removeAddr(lib.Addr, hexaddr)
			}
		}

	}
	clientsMutex.Lock()
	delete(clients, clientID)
	clientsMutex.Unlock()
}

func sendMessageToClient(clientID, message string) error {
	clientsMutex.Lock()
	client, ok := clients[clientID]
	clientsMutex.Unlock()

	if !ok {
		return fmt.Errorf("client not found")
	}

	client.Lock.Lock()
	defer client.Lock.Unlock()

	fmt.Println("发送消息:" + message)
	return client.Conn.WriteMessage(websocket.TextMessage, []byte(message))
}
