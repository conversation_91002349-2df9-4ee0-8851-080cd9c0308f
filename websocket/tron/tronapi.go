package tron

import (
	_ "errors"
	"fmt"
	"trontool/api"
)

type TronApi struct {
	fullNode     *NodeClient
	solidityNode *NodeClient
	eventNode    *NodeClient
}

func NewTronApi(fullNodeUrl, solidityNodeUrl, eventNodeUrl string) *Tron<PERSON>pi {
	return &TronApi{
		fullNode:     NewNodeClient(fullNodeUrl),
		solidityNode: NewNodeClient(solidityNodeUrl),
		eventNode:    NewNodeClient(eventNodeUrl),
	}
}

func MainNetTronApi() *TronApi {
	url := "https://api.shasta.trongrid.io"
	return NewTronApi(url, url, url)
}

func TestNetTronApi() *TronApi {
	url := "https://api.shasta.trongrid.io"
	return NewTronApi(url, url, url)
}

func (ta *TronApi) GetAccount(address string) (*api.Account, error) {
	_address, err := DecodeBase58Address(address)
	if err != nil {
		return nil, err
	}
	req := &api.AccountRequest{_address, false}
	var account api.Account
	err = ta.fullNode.Get(&account, "/wallet/getaccount", req)
	return &account, err
}

func (ta *TronApi) CreateTransaction(to string, amount int64, owner string) (*api.TransferTransaction, error) {
	_to, err := DecodeBase58Address(to)
	if err != nil {
		return nil, err
	}
	_owner, err := DecodeBase58Address(owner)
	if err != nil {
		return nil, err
	}
	req := api.NewCreateTransactionRequest(_to, amount, _owner)
	var tx api.TransferTransaction
	err = ta.fullNode.Post(&tx, "/wallet/createtransaction", req)
	return &tx, err
}

func (ta *TronApi) BroadcastTransaction(tx interface{}) (bool, error) {
	var ret api.ApiResult
	err := ta.fullNode.Post(&ret, "/wallet/broadcasttransaction", tx)
	if err != nil {
		return false, err
	}
	return ret.Result, nil
}

func (ta *TronApi) TriggerConstantContract(contractAddress, functionSelector, parameter, ownerAddress string) (*api.TriggerContractResponse, error) {
	_contractAddress, err := DecodeBase58Address(contractAddress)
	if err != nil {
		return nil, err
	}
	_ownerAddress, err := DecodeBase58Address(ownerAddress)
	if err != nil {
		return nil, err
	}
	req := api.NewTriggerContractRequest(_contractAddress, functionSelector, parameter, _ownerAddress)
	var rsp api.TriggerContractResponse
	err = ta.fullNode.Post(&rsp, "/wallet/triggerconstantcontract", req)
	return &rsp, err
}

func (ta *TronApi) TriggerSmartContract(contractAddress, functionSelector, parameter, ownerAddress string) (*api.TriggerContractResponse, error) {
	_contractAddress, err := DecodeBase58Address(contractAddress)
	if err != nil {
		return nil, err
	}
	_ownerAddress, err := DecodeBase58Address(ownerAddress)
	if err != nil {
		return nil, err
	}

	req := api.NewTriggerContractRequest(_contractAddress, functionSelector, parameter, _ownerAddress)
	var rsp api.TriggerContractResponse
	err = ta.fullNode.Post(&rsp, "/wallet/triggersmartcontract", req)
	return &rsp, err
}

func (ta *TronApi) GetContractEvents(contractAddress string, since int64) ([]api.ContractEvent, error) {
	uri := fmt.Sprintf("/event/contract/%s", contractAddress)
	params := &api.ContractEventFilter{
		Since: since,
		Sort:  "block_timestamp",
	}
	var events []api.ContractEvent
	err := ta.eventNode.Get(&events, uri, params)
	if err != nil {
		return events, err
	}
	//fmt.Println(events)
	fmt.Println(len(events))
	for _, event := range events {
		for k, v := range event.ResultType {
			if v == "address" {
				_addr, err := EncodeHexAddress("41" + event.Result[k].(string)[2:])
				if err != nil {
					return events, err
				}
				event.Result[k] = _addr
			}
		}
	}
	return events, err
}
