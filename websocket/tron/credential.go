package tron

import (
	"bytes"
	"crypto/ecdsa"
	"encoding/hex"
	"fmt"
	"github.com/ethereum/go-ethereum/accounts/abi/bind"
	"github.com/ethereum/go-ethereum/common"
	"github.com/ethereum/go-ethereum/common/hexutil"
	_ "github.com/ethereum/go-ethereum/core/types"
	"github.com/ethereum/go-ethereum/crypto"
	_ "math/big"
)

type Credential struct {
	PrivateKey *ecdsa.PrivateKey
	PublicKey  *ecdsa.PublicKey
	Address    common.Address
}

func KeyToCredential(privateKey *ecdsa.PrivateKey) (*Credential, error) {
	publicKey, ok := privateKey.Public().(*ecdsa.PublicKey)
	if !ok {
		return nil, fmt.Errorf("Public key cast error.")
	}
	address := crypto.PubkeyToAddress(*publicKey)

	credential := &Credential{
		PrivateKey: privateKey,
		PublicKey:  publicKey,
		Address:    address,
	}
	return credential, nil
}

func HexKeyToCredential(hexkey string) (*Credential, error) {
	privateKey, err := crypto.HexToECDSA(hexkey)
	if err != nil {
		return nil, err
	}
	return KeyToCredential(privateKey)
}

func NewCredential() (*Credential, error) {
	privateKey, err := crypto.GenerateKey()
	if err != nil {
		return nil, err
	}
	return KeyToCredential(privateKey)
}

func (c *Credential) PrivateKeyHex() string {
	return hexutil.Encode(crypto.FromECDSA(c.PrivateKey))
}

func (c *Credential) PublicKeyHex() string {
	return hexutil.Encode(crypto.FromECDSAPub(c.PublicKey))
}

func (c *Credential) AddressHex() string {
	bz := bytes.Join([][]byte{[]byte{0x41}, c.Address.Bytes()}, []byte{})
	return hex.EncodeToString(bz)
}

func (c *Credential) AddressBase58() string {
	bz := bytes.Join([][]byte{[]byte{0x41}, c.Address.Bytes()}, []byte{})
	return EncodeBytesAddress(bz)
}

func (c *Credential) Sign(hash []byte) ([]byte, error) {
	//  hash := crypto.Keccak256(data)
	return crypto.Sign(hash, c.PrivateKey)
}

func (c *Credential) SignHex(hash string) (string, error) {
	bz, err := hex.DecodeString(hash)
	if err != nil {
		return "", err
	}
	sig, err := crypto.Sign(bz, c.PrivateKey)
	if err != nil {
		return "", err
	}
	return hex.EncodeToString(sig), nil
}

func (c *Credential) Verify(data, signature []byte) bool {
	hash := crypto.Keccak256(data)
	pubkey := crypto.CompressPubkey(c.PublicKey)
	return crypto.VerifySignature(pubkey, hash, signature)
}

func (c *Credential) GetTransactOpts() *bind.TransactOpts {
	return bind.NewKeyedTransactor(c.PrivateKey)
}
