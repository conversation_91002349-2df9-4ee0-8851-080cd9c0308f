package main

import (
	"bytes"
	"context"
	"encoding/hex"
	"encoding/json"
	"errors"
	"fmt"
	"github.com/ethereum/go-ethereum/common"
	"github.com/ethereum/go-ethereum/core"
	"github.com/ethereum/go-ethereum/core/types"
	"github.com/ethereum/go-ethereum/ethclient"
	"github.com/shopspring/decimal"
	"github.com/spf13/cast"
	"io/ioutil"
	"log"
	"math/big"
	"net/http"
	"os"
	"regexp"
	"strings"
	"time"
	"trontool/lib"
	"trontool/tron"
)

var (
	Trc20Api *tron.TronApi = tron.NewTronApi(
		"https://api.trongrid.io", //全节点URL
		"https://api.trongrid.io", //合约节点URL
		"https://api.trongrid.io", //事件节点URL
	)
	locktime             = 60 * 30
	SUN            int64 = 1000000                              //单元 SUN
	USDTToken            = "TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t" //USDTToken网关
	USDTAddress          = "41a614f803b6fd780986a42c78ec9c7f77e6ded13c"
	TRANSFERMETHOD       = "ddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef"
	QueryKey             = "1000000000000000000000000000000000000000000000000000000000000000" //查询密钥
)

type Block struct {
	BlockID     string `json:"blockID"`
	BlockHeader struct {
		RawData struct {
			Number     int64  `json:"number"`
			ParentHash string `json:"parentHash"`
			Timestamp  int64  `json:"timestamp"`
		} `json:"raw_data"`
	} `json:"block_header"`
}

type Data struct {
	Block []struct {
		BlockID     string `json:"blockID"`
		BlockHeader struct {
			RawData struct {
				Number         int    `json:"number"`
				TxTrieRoot     string `json:"txTrieRoot"`
				WitnessAddress string `json:"witness_address"`
				ParentHash     string `json:"parentHash"`
				Version        int    `json:"version"`
				Timestamp      int64  `json:"timestamp"`
			} `json:"raw_data"`
			WitnessSignature string `json:"witness_signature"`
		} `json:"block_header"`
		Transactions []struct {
			Ret []struct {
				ContractRet string `json:"contractRet"`
			} `json:"ret"`
			Signature []string `json:"signature"`
			TxID      string   `json:"txID"`
			RawData   struct {
				Contract []struct {
					Parameter struct {
						Value struct {
							Amount          int    `json:"amount"`
							OwnerAddress    string `json:"owner_address"`
							ToAddress       string `json:"to_address"`
							ContractAddress string `json:"contract_address"`
							Data            string `json:"data"`
						} `json:"value"`
						TypeURL string `json:"type_url"`
					} `json:"parameter"`
					Type string `json:"type"`
				} `json:"contract"`
				RefBlockBytes string `json:"ref_block_bytes"`
				RefBlockHash  string `json:"ref_block_hash"`
				Expiration    int64  `json:"expiration"`
				FeeLimit      int    `json:"fee_limit"`
				Timestamp     int64  `json:"timestamp"`
			} `json:"raw_data,omitempty"`
			RawDataHex string `json:"raw_data_hex"`
		} `json:"transactions"`
	} `json:"block"`
}

type Address struct {
	Bytecode                   string `json:"bytecode"`
	ConsumeUserResourcePercent int    `json:"consume_user_resource_percent"`
	Name                       string `json:"name"`
	OriginAddress              string `json:"origin_address"`
	Abi                        struct {
		Entrys []struct {
			Outputs []struct {
				Type string `json:"type"`
			} `json:"outputs,omitempty"`
			Constant        bool   `json:"constant,omitempty"`
			Name            string `json:"name,omitempty"`
			StateMutability string `json:"stateMutability,omitempty"`
			Type            string `json:"type"`
			Inputs          []struct {
				Name string `json:"name"`
				Type string `json:"type"`
			} `json:"inputs,omitempty"`
		} `json:"entrys"`
	} `json:"abi"`
	OriginEnergyLimit int    `json:"origin_energy_limit"`
	ContractAddress   string `json:"contract_address"`
	CodeHash          string `json:"code_hash"`
}

type LogResponse struct {
	Log []LogEvent `json:"log"`
}

type LogEvent struct {
	Address string   `json:"address"`
	Topics  []string `json:"topics"`
	Data    string   `json:"data"`
}

func init() {
	lib.Load_Cfg()
	lib.Mysql_Load()
}

func main() {
	address, e := lib.PDO().SelectAll("select DISTINCT hextrxaddr from trxaddr where del_flg=0 and status=0")
	if e != nil {
		log.Println("加载监控地听失败:", e.Error())
		os.Exit(-1)
	}
	for _, v := range address {
		lib.Addr = append(lib.Addr, v["hextrxaddr"].(string))
	}
	client, err := ethclient.Dial("wss://mainnet.infura.io/ws/v3/********************************")
	//client, err := ethclient.Dial("wss://mainnet.infura.io/ws/v3/********************************")
	if err != nil {
		log.Fatal(err)
	}
	bscclient, berr := ethclient.Dial("https://bsc-dataseed.binance.org/")
	if berr != nil {
		log.Fatal(berr)
	}
	go SweepScriptTrc(client, bscclient)
	go SweepScriptErc(client, bscclient)
	go SweepScriptBsc(client, bscclient)
	WsServer()
}

func SweepScriptTrc(client *ethclient.Client, bscclient *ethclient.Client) { //扫块
	fmt.Println("SweepScriptTrc")
	cfg, cerr := lib.PDO().SelectAll("select name,value from `config`")
	if cerr != nil {
		fmt.Println(cerr.Error())
		log.Println(cerr.Error())
		return
	}
	var c int64
	for _, cf := range cfg {
		if cf["name"] == "TrcBlockHeader" {
			c = cast.ToInt64(cf["value"].(string)) + 1
		}
	}

	x, xerr := GetNowBlock()
	if xerr != nil {
		fmt.Println(xerr.Error())
		log.Println(xerr.Error())
		return
	}
	r := true
	fmt.Println(x)
	fmt.Println(c)
	for {
		if x <= c {
			r = false
		}
		if r {
			t := time.Now()
			fmt.Println("Trc开始扫描区块:", cast.ToString(c)+" -- "+cast.ToString(x), "开始 时间:"+t.Format("2006-01-02 15:04:05"))
			Transfer(c, x)
			lib.PDO().Exec("update `config` set value = ? where name = ?", cast.ToString(x), "TrcBlockHeader")
			fmt.Println("Trc完成扫描区块:", cast.ToString(c)+" -- "+cast.ToString(x), "完成 时间:"+t.Format("2006-01-02 15:04:05"))
		}
		newx, nerr := GetNowBlock()
		fmt.Println(newx)
		if nerr != nil {
			log.Println(nerr.Error())
			r = false
			time.Sleep(time.Second * 1)
			continue
		}
		if x == newx {
			r = false
			time.Sleep(time.Second * 1)
			continue
		}
		c = x
		x = newx
		r = true
		time.Sleep(time.Second * 1)
	}
}

func SweepScriptErc(client *ethclient.Client, bscclient *ethclient.Client) { //扫块
	cfg, cerr := lib.PDO().SelectAll("select name,value from `config`")
	if cerr != nil {
		log.Println(cerr.Error())
		return
	}
	var exint int64
	for _, cf := range cfg {
		if cf["name"] == "ErcBlockHeader" {
			exint = cast.ToInt64(cf["value"].(string)) + 1
		}
	}
	ex := big.NewInt(exint)
	for {
		res := ErcTransfer(client, ex)
		if res {
			lib.PDO().Exec("update `config` set value = ? where name = ?", cast.ToString(ex), "ErcBlockHeader")
			t := time.Now()
			fmt.Println("Erc扫描区块:", cast.ToString(ex)+"完成 时间:"+t.Format("2006-01-02 15:04:05"))
			ex.Add(ex, big.NewInt(1))
		}
		time.Sleep(time.Second * 8)
	}
}

func SweepScriptBsc(client *ethclient.Client, bscclient *ethclient.Client) { //扫块
	cfg, cerr := lib.PDO().SelectAll("select name,value from `config`")
	if cerr != nil {
		log.Println(cerr.Error())
		return
	}
	var bnint int64
	for _, cf := range cfg {
		if cf["name"] == "BepBlockHeader" {
			bnint = cast.ToInt64(cf["value"].(string)) + 1
		}
	}
	bn := big.NewInt(bnint)
	for {
		ress := BepTransfer(bscclient, bn)
		if ress {
			lib.PDO().Exec("update `config` set value = ? where name = ?", cast.ToString(bn), "BepBlockHeader")
			t := time.Now()
			fmt.Println("Bsc扫描区块:", cast.ToString(bn)+"完成 时间:"+t.Format("2006-01-02 15:04:05"))
			bn.Add(bn, big.NewInt(1))
		}
		time.Sleep(time.Second * 2)
	}
}

func Transfer(start int64, end int64) bool {
	url := lib.GetTransferAPI
	payload := strings.NewReader("{\"startNum\":" + cast.ToString(start) + ",\"endNum\":" + cast.ToString(end) + "}")
	req, err := http.NewRequest("POST", url, payload)
	if err != nil {
		fmt.Println("httperr:" + err.Error())
		return false
	}
	req.Header.Add("accept", "application/json")
	req.Header.Add("content-type", "application/json")
	req.Header.Add("TRON-PRO-API-KEY", "3a96e5b3-b80e-46c1-b076-8031c90b414f")
	res, err := http.DefaultClient.Do(req)
	if err != nil {
		fmt.Println("httperr:" + err.Error())
		return false
	}
	defer res.Body.Close()
	var data Data
	jerr := json.NewDecoder(res.Body).Decode(&data)
	var blockheader string
	if jerr != nil {
		fmt.Println("jsonerr:" + jerr.Error())
		return false
	} else {
		for _, v := range data.Block {
			blockheader = cast.ToString(v.BlockHeader.RawData.Number)
			for _, vv := range v.Transactions {
				var raw_data_hex string
				var txid string
				var types string
				var amount string
				var addressName string
				var address string
				var toAddress string
				var ownerAddress string
				var transfertype string
				var selector string
				raw_data_hex = vv.RawDataHex
				if vv.Ret[0].ContractRet != "SUCCESS" {
					continue
				}
				for _, addr := range lib.Addr {
					if !strings.Contains(raw_data_hex, addr[2:]) {
						continue
					}
					txid = vv.TxID
					types = vv.RawData.Contract[0].Type
					//if types != "TriggerSmartContract" && types != "TransferContract" && types != "TransferAssetContract" {
					//	continue
					//}
					if types != "TriggerSmartContract" && types != "TransferContract" {
						continue
					}
					ownerAddress = vv.RawData.Contract[0].Parameter.Value.OwnerAddress
					if ownerAddress == addr {
						transfertype = "转出"
						//continue
					} else {
						transfertype = "收入"
					}
					if types == "TriggerSmartContract" {
						//如果data小于136 代表取不到金额 跳过
						if len(vv.RawData.Contract[0].Parameter.Value.Data) < 136 {
							continue
						}
						var abierr error
						selector, toAddress, amount, abierr = DecodeABI(vv.RawData.Contract[0].Parameter.Value.Data)
						if abierr != nil {
							continue
						}
						if selector != "a9059cbb" {
							var logerr error
							var isTransfer bool
							var conAddress string
							isTransfer, toAddress, amount, conAddress, logerr = GetLogById(txid)
							if logerr != nil {
								continue
							}
							if isTransfer == false {
								continue
							}
							vv.RawData.Contract[0].Parameter.Value.ContractAddress = conAddress
						}
					} else {
						amount = cast.ToString(vv.RawData.Contract[0].Parameter.Value.Amount)
						toAddress = vv.RawData.Contract[0].Parameter.Value.ToAddress
					}
					toAddress = strings.TrimLeft(toAddress, "0")
					if !strings.HasPrefix(toAddress, "41") {
						if len(toAddress)%2 != 0 {
							toAddress = "410" + toAddress
						} else {
							toAddress = "41" + toAddress
						}
					}
					if vv.RawData.Contract[0].Parameter.Value.ContractAddress != "" {
						address = vv.RawData.Contract[0].Parameter.Value.ContractAddress
						//判断是否为USDT
						if address != USDTAddress {
							continue
						}
						redisaddrname := GetAddressNameBySlice(vv.RawData.Contract[0].Parameter.Value.ContractAddress)
						if redisaddrname == "" {
							addressName = TriggerConstantContract(vv.RawData.Contract[0].Parameter.Value.ContractAddress)
							if addressName == "" {
								addressName = GetAddressName(vv.RawData.Contract[0].Parameter.Value.ContractAddress)
							}
							m1 := make(map[string]string)
							m1["name"] = vv.RawData.Contract[0].Parameter.Value.ContractAddress
							m1["value"] = addressName
							lib.AddressNameMap = append(lib.AddressNameMap, m1)
						} else {
							addressName = redisaddrname
						}
					} else {
						if types == "TransferContract" {
							addressName = "TRX"
						} else {
							addressName = "未知币种"
						}
					}
					famount := decimal.NewFromFloat(float64(lib.StrToint64(amount))).Div(decimal.NewFromFloat(float64(1000000)))
					if famount.Cmp(decimal.NewFromFloat(cast.ToFloat64(lib.MinAmount))) == -1 {
						continue
					}
					if transfertype == "收入" {
						check, _ := lib.PDO().Select("SELECT `id`,`transactionid` FROM `orders` WHERE `transactionid`=? and `to`=? and `addr`=?", txid, addr, addr)
						if check["id"] != nil {
							continue
						}
					} else {
						check, _ := lib.PDO().Select("SELECT `id`,`transactionid` FROM `orders` WHERE `transactionid`=? and `from`=? and `addr`=?", txid, addr, addr)
						if check["id"] != nil {
							continue
						}
					}

					a := lib.PDO().Exec("INSERT INTO `orders` ( `transactionid`, `from`, `to`, `value`, `block_timestamp`, `type`, `contract_address`, `contract_name`, `block_header`, `raw_data_hex`,`addr`) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?);", txid, ownerAddress, toAddress, amount, vv.RawData.Timestamp/1000, types, address, addressName, blockheader, raw_data_hex, addr)
					if a != nil {
						fmt.Println("sqlerr:" + a.Error())
					}

					amount = cast.ToString(famount)
					userids, _ := lib.PDO().SelectAll("select userid,remark,id,trxaddr,hextrxaddr from trxaddr where hextrxaddr=? and del_flg=0 and status=0", addr)
					baseaddr, _ := tron.EncodeHexAddress(addr)
					baseowner, _ := tron.EncodeHexAddress(ownerAddress)
					baseto, _ := tron.EncodeHexAddress(toAddress)
					for _, u := range userids {
						newdata := make(map[string]string)
						newdata["address"] = addr
						newdata["amount"] = amount
						newdata["addressName"] = addressName
						newdata["txid"] = txid
						newdata["time"] = lib.TimestampToTime(vv.RawData.Timestamp / 1000)
						newdata["chain"] = "TRC"
						if transfertype == "收入" {
							newdata["transferType"] = "收入"
							newdata["receivingAddress"] = baseaddr
							newdata["paymentAddress"] = baseowner
						} else {
							newdata["transferType"] = "支出"
							newdata["receivingAddress"] = baseto
							newdata["paymentAddress"] = baseaddr
						}
						jsonData, je := json.Marshal(newdata)
						if je != nil {
							log.Println(je)
						}
						go func() {
							sendMessageToClient(u["userid"].(string), string(jsonData))
						}()
					}
				}

			}
		}
	}
	return true
}

func ErcTransfer(client *ethclient.Client, blockNumber *big.Int) bool {
	fmt.Println(blockNumber)
	block, err := client.BlockByNumber(context.Background(), blockNumber)
	if err != nil {
		if err.Error() != "not found" {
			fmt.Println("blockerr:" + err.Error())
		}
		return false
	}

	for _, tx := range block.Transactions() {
		if tx.To() == nil {
			continue
		}
		data := hex.EncodeToString(tx.Data())
		typee := tx.Type()
		var toaddress string
		var fromaddress string
		var amount string
		var addressName string
		var transfertype string
		for _, addr := range lib.Addr {
			//如果data为空 type为2或0 为ETH交易 否则判断是否为USDT交易
			if data == "" && (typee == 2 || typee == 0) {
				to := tx.To().Hex()
				msg, _ := core.TransactionToMessage(tx, types.LatestSignerForChainID(tx.ChainId()), nil)
				fromaddress = strings.ToLower(msg.From.Hex())
				if strings.ToLower(to) != strings.ToLower(addr) && strings.ToLower(fromaddress) != strings.ToLower(addr) {
					continue
				}
				if tx.Value().String() == "0" {
					continue
				}
				receipt, _ := client.TransactionReceipt(context.Background(), common.HexToHash(tx.Hash().Hex()))
				if receipt == nil {
					continue
				}
				if receipt.Status != 1 {
					continue
				}
				if strings.ToLower(to) == strings.ToLower(addr) {
					transfertype = "收入"
				} else {
					transfertype = "支出"
				}
				addressName = "ETH"
				toaddress = strings.ToLower(to)
				//if tx.Hash().Hex() == "0xce6291bd77b5e5e423d471fb41c061add78c869de062c8a886afca0ac2d77f6f" {
				//	fmt.Println(tx.Value().String())
				//}
				divisor := big.NewInt(1000000000000000000)
				result := new(big.Float).Quo(
					new(big.Float).SetInt(tx.Value()),
					new(big.Float).SetInt(divisor),
				)
				amount = result.Text('f', 18)                  // 将结果转换为字符串，保留 18 位小数
				re := regexp.MustCompile(`(\.[0-9]*[1-9])0*$`) // 匹配小数点后面的零
				amount = re.ReplaceAllString(amount, "$1")
				//fmt.Println(amount)
				//amount = cast.ToString(decimal.NewFromFloat(float64(lib.StrToint64(tx.Value().String()))).Div(decimal.NewFromFloat(float64(1000000000000000000))))
			} else {
				to := tx.To().Hex()
				//判断是否为USDT交易
				if !strings.HasPrefix(data, "a9059cbb") {
					continue
				}
				if strings.ToLower(to) != strings.ToLower("******************************************") {
					continue
				}
				_, toaddr, value, abierr := DecodeABI(data)
				if abierr != nil {
					continue
				}
				end := len(toaddr)
				start := end - 40
				if start < 0 {
					start = 0
				}
				toaddress = strings.ToLower("0x" + toaddr[start:end])
				msg, _ := core.TransactionToMessage(tx, types.LatestSignerForChainID(tx.ChainId()), nil)
				fromaddress = strings.ToLower(msg.From.Hex())
				if strings.ToLower(toaddress) != strings.ToLower(addr) && strings.ToLower(fromaddress) != strings.ToLower(addr) {
					continue
				}
				receipt, _ := client.TransactionReceipt(context.Background(), common.HexToHash(tx.Hash().Hex()))
				if receipt == nil {
					continue
				}
				if receipt.Status != 1 {
					continue
				}
				if strings.ToLower(toaddress) == strings.ToLower(addr) {
					transfertype = "收入"
				} else {
					transfertype = "支出"
				}
				addressName = "USDT"
				amount = cast.ToString(decimal.NewFromFloat(float64(lib.StrToint64(value))).Div(decimal.NewFromFloat(float64(1000000))))
			}
			if transfertype == "收入" {
				check, _ := lib.PDO().Select("SELECT `id`,`transactionid` FROM `orders` WHERE `transactionid`=? and `to`=? and `addr`=?", tx.Hash().Hex(), strings.ToLower(addr), addr)
				if check["id"] != nil {
					continue
				}
			} else {
				check, _ := lib.PDO().Select("SELECT `id`,`transactionid` FROM `orders` WHERE `transactionid`=? and `from`=? and `addr`=?", tx.Hash().Hex(), strings.ToLower(addr), addr)
				if check["id"] != nil {
					continue
				}
			}
			a := lib.PDO().Exec("INSERT INTO `orders` ( `transactionid`, `from`, `to`, `value`, `block_timestamp`, `type`, `contract_name`, `block_header`, `raw_data_hex`, `addr`) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?);", tx.Hash().Hex(), fromaddress, toaddress, amount, block.Time(), typee, addressName, cast.ToString(blockNumber), data, addr)
			if a != nil {
				fmt.Println("sqlerr:" + a.Error())
			}
			userids, _ := lib.PDO().SelectAll("select userid,remark,id,trxaddr,hextrxaddr from trxaddr where hextrxaddr=? and del_flg=0 and status=0", addr)
			for _, u := range userids {
				newdata := make(map[string]string)
				newdata["address"] = addr
				newdata["amount"] = amount
				newdata["addressName"] = addressName
				newdata["txid"] = tx.Hash().Hex()
				newdata["time"] = lib.TimestampToTime(cast.ToInt64(block.Time()))
				newdata["transferType"] = transfertype
				newdata["receivingAddress"] = toaddress
				newdata["paymentAddress"] = fromaddress
				newdata["chain"] = "ERC"
				jsonData, je := json.Marshal(newdata)
				if je != nil {
					log.Println(je)
				}
				go func() {
					sendMessageToClient(u["userid"].(string), string(jsonData))
				}()
			}
		}
	}
	return true
}

func BepTransfer(client *ethclient.Client, blockNumber *big.Int) bool {
	block, err := client.BlockByNumber(context.Background(), blockNumber)
	if err != nil {
		if err.Error() != "not found" {
			fmt.Println("blockerr:" + err.Error())
		}
		return false
	}

	for _, tx := range block.Transactions() {
		if tx.To() == nil {
			continue
		}
		typee := tx.Type()
		var toaddress string
		var fromaddress string
		var amount string
		var addressName string
		var transfertype string
		data := hex.EncodeToString(tx.Data())
		for _, addr := range lib.Addr {
			//如果data为空 为BNB交易 否则判断是否为USDT交易
			if data == "" {
				to := tx.To().Hex()
				msg, _ := core.TransactionToMessage(tx, types.LatestSignerForChainID(tx.ChainId()), nil)
				fromaddress = strings.ToLower(msg.From.Hex())
				if strings.ToLower(to) != strings.ToLower(addr) && strings.ToLower(fromaddress) != strings.ToLower(addr) {
					continue
				}
				if tx.Value().String() == "0" {
					continue
				}
				receipt, _ := client.TransactionReceipt(context.Background(), common.HexToHash(tx.Hash().Hex()))
				if receipt == nil {
					continue
				}
				if receipt.Status != 1 {
					continue
				}
				if strings.ToLower(to) == strings.ToLower(addr) {
					transfertype = "收入"
				} else {
					transfertype = "支出"
				}
				addressName = "BNB"
				toaddress = strings.ToLower(to)
				//if tx.Hash().Hex() == "0xce6291bd77b5e5e423d471fb41c061add78c869de062c8a886afca0ac2d77f6f" {
				//	fmt.Println(tx.Value().String())
				//}
				divisor := big.NewInt(1000000000000000000)
				result := new(big.Float).Quo(
					new(big.Float).SetInt(tx.Value()),
					new(big.Float).SetInt(divisor),
				)
				amount = result.Text('f', 18)                  // 将结果转换为字符串，保留 18 位小数
				re := regexp.MustCompile(`(\.[0-9]*[1-9])0*$`) // 匹配小数点后面的零
				amount = re.ReplaceAllString(amount, "$1")
				//fmt.Println(amount)
				//amount = cast.ToString(decimal.NewFromFloat(float64(lib.StrToint64(tx.Value().String()))).Div(decimal.NewFromFloat(float64(1000000000000000000))))
			} else {
				to := tx.To().Hex()
				//判断是否为USDT交易
				if !strings.HasPrefix(data, "a9059cbb") {
					continue
				}
				if strings.ToLower(to) != strings.ToLower("0x55d398326f99059fF775485246999027B3197955") {
					continue
				}
				_, toaddr, value, abierr := DecodeABI(data)
				if abierr != nil {
					continue
				}
				end := len(toaddr)
				start := end - 40
				if start < 0 {
					start = 0
				}
				toaddress = strings.ToLower("0x" + toaddr[start:end])
				msg, _ := core.TransactionToMessage(tx, types.LatestSignerForChainID(tx.ChainId()), nil)
				fromaddress = strings.ToLower(msg.From.Hex())
				if strings.ToLower(toaddress) != strings.ToLower(addr) && strings.ToLower(fromaddress) != strings.ToLower(addr) {
					continue
				}

				receipt, _ := client.TransactionReceipt(context.Background(), common.HexToHash(tx.Hash().Hex()))
				if receipt == nil {
					continue
				}

				if receipt.Status != 1 {
					continue
				}
				if strings.ToLower(toaddress) == strings.ToLower(addr) {
					transfertype = "收入"
				} else {
					transfertype = "支出"
				}
				addressName = "USDT"
				amount = decimal.NewFromFloat(cast.ToFloat64(value)).Div(decimal.NewFromFloat(1000000000000000000)).String()
			}
			if transfertype == "收入" {
				check, _ := lib.PDO().Select("SELECT `id`,`transactionid` FROM `orders` WHERE `transactionid`=? and `to`=? and `addr`=?", tx.Hash().Hex(), strings.ToLower(addr), addr)
				if check["id"] != nil {
					continue
				}
			} else {
				check, _ := lib.PDO().Select("SELECT `id`,`transactionid` FROM `orders` WHERE `transactionid`=? and `from`=? and `addr`=?", tx.Hash().Hex(), strings.ToLower(addr), addr)
				if check["id"] != nil {
					continue
				}
			}
			a := lib.PDO().Exec("INSERT INTO `orders` ( `transactionid`, `from`, `to`, `value`, `block_timestamp`, `type`, `contract_name`, `block_header`, `raw_data_hex`, `addr`) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?);", tx.Hash().Hex(), fromaddress, toaddress, amount, block.Time(), typee, addressName, cast.ToString(blockNumber), data, addr)
			if a != nil {
				fmt.Println("sqlerr:" + a.Error())
			}
			userids, uerr := lib.PDO().SelectAll("select userid,remark,id,trxaddr,hextrxaddr from trxaddr where hextrxaddr=? and del_flg=0 and status=0", addr)
			fmt.Println(userids)
			fmt.Println(uerr)
			for _, u := range userids {
				newdata := make(map[string]string)
				newdata["address"] = addr
				newdata["amount"] = amount
				newdata["addressName"] = addressName
				newdata["txid"] = tx.Hash().Hex()
				newdata["time"] = lib.TimestampToTime(cast.ToInt64(block.Time()))
				newdata["transferType"] = transfertype
				newdata["receivingAddress"] = toaddress
				newdata["paymentAddress"] = fromaddress
				newdata["chain"] = "BSC"
				jsonData, je := json.Marshal(newdata)
				if je != nil {
					log.Println(je)
				}
				fmt.Println(string(jsonData))
				go func() {
					sendMessageToClient(u["userid"].(string), string(jsonData))
				}()
			}
		}
	}
	return true
}

func GetAddressName(addr string) string {
	url := lib.GetContractAPI

	payload := strings.NewReader("{\"value\":\"" + addr + "\"}")

	req, _ := http.NewRequest("POST", url, payload)

	req.Header.Add("accept", "application/json")
	req.Header.Add("content-type", "application/json")
	req.Header.Add("TRON-PRO-API-KEY", "3a96e5b3-b80e-46c1-b076-8031c90b414f")

	res, _ := http.DefaultClient.Do(req)

	defer res.Body.Close()
	var address Address
	jerr := json.NewDecoder(res.Body).Decode(&address)
	if jerr != nil {
		fmt.Printf("Failed to decode JSON response: %s\n", jerr)
	} else {
		return address.Name
	}
	return ""
}

func GetNowBlock() (int64, error) {
	response, err := http.Get(lib.GetNowBlockAPI)
	if err != nil {
		fmt.Printf("The HTTP request failed with error %s\n", err)
		return 0, err
	} else {
		defer response.Body.Close()

		var block Block
		err := json.NewDecoder(response.Body).Decode(&block)
		if err != nil {
			fmt.Printf("Failed to decode JSON response: %s\n", err)
			return 0, err
		} else {
			return block.BlockHeader.RawData.Number, nil
		}
	}
}

//func GetNowBlock() (int64, error) {
//	// 假设lib.GetNowBlockAPI是请求的URL
//	reqBody, err := json.Marshal(map[string]string{
//		// 如果API需要请求体参数，可以在这里添加
//	})
//	if err != nil {
//		fmt.Printf("Failed to marshal request body: %s\n", err)
//		return 0, err
//	}
//
//	req, err := http.NewRequest("POST", lib.GetNowBlockAPI, bytes.NewBuffer(reqBody))
//	if err != nil {
//		fmt.Printf("Failed to create request: %s\n", err)
//		return 0, err
//	}
//
//	req.Header.Set("Content-Type", "application/json")
//	req.Header.Set("TRON-PRO-API-KEY", "3a96e5b3-b80e-46c1-b076-8031c90b414f")
//
//	client := &http.Client{}
//	response, err := client.Do(req)
//	if err != nil {
//		fmt.Printf("The HTTP request failed with error %s\n", err)
//		return 0, err
//	}
//	defer response.Body.Close()
//
//	var block Block
//	err = json.NewDecoder(response.Body).Decode(&block)
//	if err != nil {
//		fmt.Printf("Failed to decode JSON response: %s\n", err)
//		return 0, err
//	}
//
//	return block.BlockHeader.RawData.Number, nil
//}

func DecodeABI(abistr string) (string, string, string, error) {
	selector := ""
	toAddress := ""
	amountHex := ""

	if len(abistr) >= 136 {
		//提取函数选择器
		selector = abistr[:8]

		// 提取收款人地址
		toAddress = abistr[8:72]

		// 提取发送金额
		amountHex = abistr[72:136]
	} else {
		return "", "", "", errors.New("abistr too short")
	}

	bytes, err := hex.DecodeString(amountHex)
	if err != nil {
		fmt.Println("ABIerr:" + err.Error())
		return "", "", "", err
	}
	var val big.Int
	val.SetBytes(bytes)
	return selector, toAddress, val.String(), nil
}

func TriggerConstantContract(address string) string {
	url := lib.TriggerContractAPI
	method := "POST"

	payload := map[string]interface{}{
		"owner_address":     "410000000000000000000000000000000000000000",
		"contract_address":  address,
		"function_selector": "symbol()",
	}

	payloadBytes, err := json.Marshal(payload)
	if err != nil {
		fmt.Println("Error while encoding payload:", err)
		return ""
	}

	client := &http.Client{}
	req, err := http.NewRequest(method, url, bytes.NewBuffer(payloadBytes))
	if err != nil {
		fmt.Println("Error while creating request:", err)
		return ""
	}

	req.Header.Add("accept", "application/json")
	req.Header.Add("content-type", "application/json")
	req.Header.Add("TRON-PRO-API-KEY", "3a96e5b3-b80e-46c1-b076-8031c90b414f")

	res, err := client.Do(req)
	if err != nil {
		fmt.Println("Error while making request:", err)
		return ""
	}

	defer res.Body.Close()

	body, err := ioutil.ReadAll(res.Body)
	if err != nil {
		fmt.Println("Error while reading response body:", err)
		return ""
	}

	var response map[string]interface{}
	err = json.Unmarshal(body, &response)
	if err != nil {
		fmt.Println("Error while decoding response:", err)
		return ""
	}

	constantResultArray, ok := response["constant_result"].([]interface{})
	if !ok {
		fmt.Println("Error while parsing constant result")
		return ""
	}
	var constantResult string
	if len(constantResultArray) > 0 {
		constantResult = constantResultArray[0].(string)
	}

	if constantResult == "" {
		return ""
	}

	hexStr := constantResult[128:136]
	usdt, err := hex.DecodeString(hexStr)
	return cast.ToString(usdt)
}

func GetLogById(txid string) (bool, string, string, string, error) {
	client := &http.Client{}
	transactionID := txid

	req, err := http.NewRequest("GET", fmt.Sprintf("https://api.trongrid.io/wallet/gettransactioninfobyid?value=%s", transactionID), nil)
	if err != nil {
		fmt.Println(err)
		return false, "", "", "", err
	}

	resp, err := client.Do(req)
	if err != nil {
		fmt.Println(err)
		return false, "", "", "", err
	}
	defer resp.Body.Close()

	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		fmt.Println(err)
		return false, "", "", "", err
	}

	var response LogResponse
	err = json.Unmarshal(body, &response)
	if err != nil {
		fmt.Println(err)
		return false, "", "", "", err
	}

	transferEventFound := false
	var toAddress string
	var contractAddress string
	var value *big.Int

	for _, event := range response.Log {
		if len(event.Topics) > 0 && event.Topics[0] == TRANSFERMETHOD {
			transferEventFound = true
			toAddress = strings.TrimPrefix(event.Topics[2], "0x")
			contractAddress = "41" + event.Address
			valueBytes, err := hex.DecodeString(strings.TrimPrefix(event.Data, "0x"))
			if err != nil {
				fmt.Println(err)
				return false, "", "", "", err
			}
			value = new(big.Int).SetBytes(valueBytes)
			break
		}
	}

	if transferEventFound {
		return true, toAddress, value.String(), contractAddress, nil
	} else {
		return false, "", "", "", nil
	}
}

func GetAddressNameBySlice(name string) string {
	str := ""
	for _, m := range lib.AddressNameMap {
		if m["name"] == name {
			str = m["value"]
		}
	}
	return str
}

func removeAddr(slice []string, elem string) []string {
	index := -1
	for i, v := range slice {
		if v == elem {
			index = i
			break
		}
	}
	if index == -1 {
		return slice
	}
	copy(slice[index:], slice[index+1:])
	return slice[:len(slice)-1]
}

func escapeMarkdown(input string) string {
	charsToEscape := []string{"_", "*", "`"}

	escaped := input
	for _, char := range charsToEscape {
		escaped = strings.ReplaceAll(escaped, char, "\\"+char)
	}

	return escaped
}
