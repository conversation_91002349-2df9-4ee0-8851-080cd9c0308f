package lib

import (
	"database/sql"
	"log"
	"os"

	"github.com/go-ini/ini"
)

// 321jhbjkbadsd!@sPDKLSA
var (
	DB                 *sql.DB
	DBUSER             = "root"
	DBPASS             = "123456"
	DBHOST             = "127.0.0.1"
	DBPORT             = "3306"
	DBCHARSET          = "utf8"
	DBNAME             = "USDT"
	REDISHOST          = "127.0.0.1:6379"
	REDISPASS          = "123456"
	TempDir            = "./tmp/"
	APIHOST            = ""
	TgToken            = ""
	GetContractAPI     = ""
	GetNowBlockAPI     = ""
	TriggerContractAPI = ""
	GetTransferAPI     = ""
	MinAmount          = ""
	MaxAddrCount       = ""
	Addr               []string
	AddressNameMap     []map[string]string
)

func Load_Cfg() {
	cfg, err := ini.Load("./config.ini")
	if err != nil {
		log.Println("加载配置文件失败:", err.Error())
		os.Exit(-1)
	}
	cfgs := cfg.Section("TrxTransferAlert")
	DBUSER = cfgs.Key("DBUSER").Value()
	DBPASS = cfgs.Key("DBPASS").Value()
	DBHOST = cfgs.Key("DBHOST").Value()
	DBPORT = cfgs.Key("DBPORT").Value()
	DBCHARSET = cfgs.Key("DBCHARSET").Value()
	DBNAME = cfgs.Key("DBNAME").Value()
	TgToken = cfgs.Key("TgToken").Value()
	GetContractAPI = cfgs.Key("GetContractAPI").Value()
	GetNowBlockAPI = cfgs.Key("GetNowBlockAPI").Value()
	TriggerContractAPI = cfgs.Key("TriggerContractAPI").Value()
	GetTransferAPI = cfgs.Key("GetTransferAPI").Value()
	MinAmount = cfgs.Key("MinAmount").Value()
	MaxAddrCount = cfgs.Key("MaxAddrCount").Value()

}
