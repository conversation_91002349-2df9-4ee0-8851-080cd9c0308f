package lib

import (
	"context"
	"crypto/md5"
	"crypto/sha256"
	"crypto/tls"
	"encoding/json"
	"errors"
	"fmt"
	"github.com/ethereum/go-ethereum"
	"github.com/ethereum/go-ethereum/common"
	"github.com/ethereum/go-ethereum/ethclient"
	"github.com/shopspring/decimal"
	"io"
	"io/ioutil"
	"math/big"
	"math/rand"
	"net/http"
	"os"
	"path/filepath"
	"reflect"
	"regexp"
	"strconv"
	"strings"
	"time"
	"trontool/tron"

	"github.com/spf13/cast"
)

var (
	Iplist                       = []string{}
	base58Alphabet               = []byte("**********************************************************")
	Trc20Api       *tron.TronApi = tron.NewTronApi(
		"https://api.trongrid.io", //全节点URL
		"https://api.trongrid.io", //合约节点URL
		"https://api.trongrid.io", //事件节点URL
	) //单元 SUN
	USDTToken = "TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t"                               //USDTToken网关
	QueryKey  = "1000000000000000000000000000000000000000000000000000000000000000" //查询密钥
)

type Ankr struct {
	Jsonrpc string `json:"jsonrpc"`
	ID      int    `json:"id"`
	Result  struct {
		TotalBalanceUsd string `json:"totalBalanceUsd"`
		TotalCount      int    `json:"totalCount"`
		Assets          []struct {
			Blockchain        string `json:"blockchain"`
			TokenName         string `json:"tokenName"`
			TokenSymbol       string `json:"tokenSymbol"`
			TokenDecimals     int    `json:"tokenDecimals"`
			TokenType         string `json:"tokenType"`
			ContractAddress   string `json:"contractAddress,omitempty"`
			HolderAddress     string `json:"holderAddress"`
			Balance           string `json:"balance"`
			BalanceRawInteger string `json:"balanceRawInteger"`
			BalanceUsd        string `json:"balanceUsd"`
			TokenPrice        string `json:"tokenPrice"`
			Thumbnail         string `json:"thumbnail"`
		} `json:"assets"`
	} `json:"result"`
}

type UsdtData struct {
	Trc20TokenBalances []struct {
		TokenID          string  `json:"tokenId"`
		Balance          string  `json:"balance"`
		TokenName        string  `json:"tokenName"`
		TokenAbbr        string  `json:"tokenAbbr"`
		TokenDecimal     int     `json:"tokenDecimal"`
		TokenCanShow     int     `json:"tokenCanShow"`
		TokenType        string  `json:"tokenType"`
		TokenLogo        string  `json:"tokenLogo"`
		Vip              bool    `json:"vip"`
		TokenPriceInTrx  float64 `json:"tokenPriceInTrx,omitempty"`
		Amount           float64 `json:"amount,omitempty"`
		NrOfTokenHolders int     `json:"nrOfTokenHolders"`
		TransferCount    int     `json:"transferCount"`
	} `json:"trc20token_balances"`
}

func UsdtInt64ToFloat64(num int64) float64 {
	var SUN int64 = 1000000
	return float64(num) / float64(SUN)
}
func UpdateIplist() {
	go func() {
		for {
			ip, _ := ReadFile("iplist.txt")
			ip = strings.ReplaceAll(ip, "\r\n", "\n")
			Iplist = strings.Split(ip, "\n")
			time.Sleep(time.Second * time.Duration(60))
		}
	}()
}
func StrToint64(str string) int64 {
	i, err := strconv.ParseInt(str, 10, 64)
	if err != nil {
		return 0
	}
	return i
}
func GetCurrentAbPathByExecutable() string {
	exePath, err := os.Executable()
	if err != nil {
		return ""
	}
	res, _ := filepath.EvalSymlinks(filepath.Dir(exePath))
	return res
}
func ReadFile(filePath string) (string, error) {
	content, err := ioutil.ReadFile(filePath)
	if err != nil {
		return "", err
	}
	return string(content), err
}
func StringToFloat64(str string) float64 {
	num, err := strconv.ParseFloat(str, 64)
	if err != nil {
		return 0.00
	}
	return num
}
func Shuffle(slice []string) {
	r := rand.New(rand.NewSource(time.Now().UnixNano()))
	time.Sleep(time.Microsecond * 1)
	for len(slice) > 0 {
		n := len(slice)
		randIndex := r.Intn(n)
		slice[n-1], slice[randIndex] = slice[randIndex], slice[n-1]
		slice = slice[:n-1]
	}
}

func CallbackGet(url string) string {
	transport := &http.Transport{
		TLSClientConfig: &tls.Config{InsecureSkipVerify: true},
	}
	client := &http.Client{
		Timeout:   10 * time.Second,
		Transport: transport,
	}
	reqest, err := http.NewRequest("GET", url, nil)
	if err != nil {
		return ""
	}
	//增加header选项
	reqest.Header.Add("User-Agent", "Telegram USDT Server 1.0")
	//处理返回结果
	response, err := client.Do(reqest)
	if err != nil {
		return ""
	}
	defer response.Body.Close()
	r, e := io.ReadAll(response.Body)
	if e != nil {
		return ""
	}
	return string(r)
}
func Get(url string) string {
	transport := &http.Transport{
		TLSClientConfig: &tls.Config{InsecureSkipVerify: true},
	}
	client := &http.Client{
		Timeout:   10 * time.Second,
		Transport: transport,
	}
	reqest, err := http.NewRequest("GET", url, nil)
	if err != nil {
		return ""
	}
	//增加header选项
	reqest.Header.Add("User-Agent", "Mozilla/5.0 (Linux; U; Android 4.0.3; zh-cn; U8860 Build/HuaweiU8860) UC AppleWebKit/530+ (KHTML, like Gecko) Mobile Safari/530 ")
	//处理返回结果
	response, err := client.Do(reqest)
	if err != nil {
		return ""
	}
	defer response.Body.Close()
	r, e := io.ReadAll(response.Body)
	if e != nil {
		return ""
	}
	return string(r)
}

// 类型转换
func TypeConversion(value string, ntype string) (reflect.Value, error) {
	if ntype == "string" {
		return reflect.ValueOf(value), nil
	} else if ntype == "time.Time" {
		t, err := time.ParseInLocation("2006-01-02 15:04:05", value, time.Local)
		return reflect.ValueOf(t), err
	} else if ntype == "Time" {
		t, err := time.ParseInLocation("2006-01-02 15:04:05", value, time.Local)
		return reflect.ValueOf(t), err
	} else if ntype == "int" {
		i, err := strconv.Atoi(value)
		return reflect.ValueOf(i), err
	} else if ntype == "int8" {
		i, err := strconv.ParseInt(value, 10, 64)
		return reflect.ValueOf(int8(i)), err
	} else if ntype == "int32" {
		i, err := strconv.ParseInt(value, 10, 64)
		return reflect.ValueOf(int64(i)), err
	} else if ntype == "int64" {
		i, err := strconv.ParseInt(value, 10, 64)
		return reflect.ValueOf(i), err
	} else if ntype == "float32" {
		i, err := strconv.ParseFloat(value, 64)
		return reflect.ValueOf(float32(i)), err
	} else if ntype == "float64" {
		i, err := strconv.ParseFloat(value, 64)
		return reflect.ValueOf(i), err
	}

	//else if .......增加其他一些类型的转换
	return reflect.ValueOf(value), errors.New("未知的类型：" + ntype)
}
func Md5(s string) string {
	srcCode := md5.Sum([]byte(s))
	code := fmt.Sprintf("%x", srcCode)
	return string(code)
}
func FilteredSQLInject(to_match_str string) bool {
	//过滤 ‘
	//ORACLE 注解 --  /**/
	//关键字过滤 update ,delete
	// 正则的字符串, 不能用 " " 因为" "里面的内容会转义
	str := `(?:')|(?:--)|(/\\*(?:.|[\\n\\r])*?\\*/)|(\b(select|update|and|or|delete|insert|trancate|char|chr|into|substr|ascii|declare|exec|count|master|into|drop|execute)\b)`
	re, err := regexp.Compile(str)
	if err != nil {
		//panic(err.Error())
		//log.Println("注入")
		return false
	}
	//log.Println("没注入")
	return re.MatchString(to_match_str)
}
func RandAllString(lenNum int) string {
	var CHARS = []string{"a", "b", "c", "d", "e", "f", "g", "h", "i", "j", "k", "l", "m", "n", "o", "p", "q", "r", "s", "t", "u", "v", "w", "x", "y", "z",
		"A", "B", "C", "D", "E", "F", "G", "H", "I", "J", "K", "L", "M", "N", "O", "P", "Q", "R", "S", "T", "U", "V", "W", "X", "Y", "Z",
		"1", "2", "3", "4", "5", "6", "7", "8", "9", "0"}
	str := strings.Builder{}
	length := len(CHARS)
	rand.Seed(time.Now().UnixNano())
	time.Sleep(time.Microsecond / 1000)
	for i := 0; i < lenNum; i++ {
		l := CHARS[rand.Intn(length)]
		str.WriteString(l)
	}
	return str.String()
}

func SliceContains(s []string, v string) bool {
	for _, item := range s {
		if item == v {
			return true
		}
	}
	return false
}

func StringContains(s string, substr string) bool {
	return strings.Contains(s, substr)
}

func TimestampToTime(timestamp int64) string {
	t := time.Unix(timestamp, 0)
	return t.Format("2006-01-02 15:04:05")
}

func base58Decode(input string) ([]byte, error) {
	result := big.NewInt(0)
	multiplier := big.NewInt(1)
	for i := len(input) - 1; i >= 0; i-- {
		index := -1
		for j, b := range base58Alphabet {
			if b == input[i] {
				index = j
				break
			}
		}
		if index == -1 {
			return nil, fmt.Errorf("解码出错，发现无效字符: %c", input[i])
		}
		temp := big.NewInt(int64(index))
		temp.Mul(temp, multiplier)
		result.Add(result, temp)
		multiplier.Mul(multiplier, big.NewInt(58))
	}

	decoded := result.Bytes()
	return decoded, nil
}

func base58Encodec(input []byte) string {
	bigInt := big.NewInt(0).SetBytes(input)
	encoded := []byte{}

	base := big.NewInt(58)
	remainder := new(big.Int)

	for bigInt.Cmp(big.NewInt(0)) > 0 {
		bigInt.DivMod(bigInt, base, remainder)
		encoded = append(encoded, base58Alphabet[remainder.Int64()])
	}

	for i, j := 0, len(encoded)-1; i < j; i, j = i+1, j-1 {
		encoded[i], encoded[j] = encoded[j], encoded[i]
	}

	return string(encoded)
}

func ValidateAddress(address string) (bool, error) {
	decoded, err := base58Decode(address)
	if err != nil {
		return false, err
	}

	if len(decoded) != 25 || decoded[0] != 0x41 {
		return false, nil
	}

	hash := sha256.Sum256(decoded[:21])
	hash = sha256.Sum256(hash[:])

	for i := 0; i < 4; i++ {
		if decoded[21+i] != hash[i] {
			return false, nil
		}
	}

	return true, nil
}

func TronGetUsdtBalance(address string) string {
	// 设置查询地址和代币合约地址
	contract := "TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t"

	// 构造查询URL
	url := fmt.Sprintf("https://apilist.tronscan.org/api/account?address=%s", address)

	// 发送查询请求
	response, err := http.Get(url)
	if err != nil {
		fmt.Println("发送查询请求失败：", err)
		return "err"
	}

	// 解析查询结果
	var result UsdtData
	data, _ := ioutil.ReadAll(response.Body)
	if err := json.Unmarshal(data, &result); err != nil {
		fmt.Println("解析查询结果失败：", err)
		return "err"
	}

	// 查找代币余额
	var balance string
	for _, token := range result.Trc20TokenBalances {
		if token.TokenID == contract {
			balance = token.Balance
		}
	}
	return balance
}

func Trc20UsdtGetMenoyS(addr string) (int64, bool) {
	credential, err := tron.HexKeyToCredential(QueryKey)
	if err != nil {
		return 0, false
	}
	kit := tron.NewTronKit(
		Trc20Api,
		credential,
	)
	usdt, err := kit.Trc20(USDTToken) //创建USDT-TRC20代币实例
	if err != nil {
		fmt.Println(1)
		fmt.Println(err)
		return -1, false
	}
	fmt.Println(usdt)
	balance, err := usdt.BalanceOf(addr) //查询Trc20代币余额
	if err != nil {
		fmt.Println(2)
		fmt.Println(err)
		return -2, false
	}
	fmt.Println(balance)
	x := balance.Uint64()
	return int64(x), true
}
func TrxGetMenoyS(addr string) (int64, bool) { //trx余额查询
	credential, err := tron.HexKeyToCredential(QueryKey)
	if err != nil {
		return -1, false
	}
	kit := tron.NewTronKit(
		Trc20Api,
		credential,
	)
	//var SUN int64 = 1000000
	balance, err := kit.GetTrxBalance(addr) //查询Trx余额，单位：SUN
	if err != nil {
		return -2, false
	}
	return balance, true
}

func AnkrGetErcBalance(addr string) (string, string) {
	client, err := ethclient.Dial("https://rpc.ankr.com/eth")
	usdt := common.HexToAddress("******************************************")
	address := addr
	dataString := fmt.Sprintf("0x70a08231000000000000000000000000%s", address[2:])
	qq, err := client.CallContract(context.Background(), ethereum.CallMsg{
		From: common.HexToAddress("******************************************"),
		To:   &usdt,
		Data: common.FromHex(dataString),
	}, nil)
	if err != nil {
		fmt.Printf("err: %v\n", err)
	}
	num, err := strconv.ParseInt(common.Bytes2Hex(qq), 16, 64)
	balance, _ := client.BalanceAt(context.Background(), common.HexToAddress(addr), nil)
	dividend := balance
	divisor := big.NewInt(1000000000000000000)
	result := new(big.Float).Quo(
		new(big.Float).SetInt(dividend),
		new(big.Float).SetInt(divisor),
	)
	ethnum := result.Text('f', 18)                 // 将结果转换为字符串，保留 18 位小数
	re := regexp.MustCompile(`(\.[0-9]*[1-9])0*$`) // 匹配小数点后面的零
	ethnum = re.ReplaceAllString(ethnum, "$1")
	usdtnum := cast.ToString(decimal.NewFromFloat(cast.ToFloat64(num)).Div(decimal.NewFromFloat(float64(1000000))))
	return cast.ToString(usdtnum), cast.ToString(ethnum)
}
