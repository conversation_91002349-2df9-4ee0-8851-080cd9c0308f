package lib

import (
	"database/sql"
	"fmt"
	"log"
	"os"
	"time"

	_ "github.com/go-sql-driver/mysql"
)

type Mysql struct {
	Pdo *sql.DB
}

func PDO() *Mysql {
	var r Mysql
	r.Pdo = DB
	return &r
}

type dbRow map[string]interface{}

func scanRow(rows *sql.Rows) (dbRow, error) {
	columns, _ := rows.Columns()

	vals := make([]interface{}, len(columns))
	valsPtr := make([]interface{}, len(columns))

	for i := range vals {
		valsPtr[i] = &vals[i]
	}

	err := rows.Scan(valsPtr...)

	if err != nil {
		return nil, err
	}

	r := make(dbRow)

	for i, v := range columns {
		if va, ok := vals[i].([]byte); ok {
			r[v] = string(va)
		} else {
			r[v] = vals[i]
		}
	}

	return r, nil

}
func (k *Mysql) Exec(sql string, args ...interface{}) error {
	tx, err := k.Pdo.Begin()
	if err != nil {
		return err
	}
	_, err = tx.Exec(sql, args...)
	if err != nil {
		tx.Rollback()
		return err
	}
	err = tx.Commit()
	if err != nil {
		return err
	}
	return nil
}

// 获取一行记录
func (k *Mysql) Select(sql string, args ...interface{}) (dbRow, error) {
	rows, err := k.Pdo.Query(sql, args...)
	if err != nil {
		return nil, err
	}

	defer rows.Close()
	rows.Next()
	result, err := scanRow(rows)
	return result, err
}

// 获取多行记录
func (k *Mysql) SelectAll(sql string, args ...interface{}) ([]dbRow, error) {
	rows, err := k.Pdo.Query(sql, args...)
	if err != nil {
		return nil, err
	}

	defer rows.Close()

	result := make([]dbRow, 0)

	for rows.Next() {
		r, err := scanRow(rows)
		if err != nil {
			continue
		}

		result = append(result, r)
	}

	return result, nil

}
func Mysql_Load() {
	dsn := fmt.Sprintf("%s:%s@tcp(%s:%s)/%s?charset=%s&parseTime=True&loc=Local", DBUSER, DBPASS, DBHOST, DBPORT, DBNAME, DBCHARSET)
	DBS, err := sql.Open("mysql", dsn) // 使用本地时间，即东八区，北京时间
	if err != nil {
		log.Println("[数据库错误日志]Mysql 链接失败:", err.Error())
		os.Exit(-1)
	}
	DB = DBS
	DB.SetMaxOpenConns(2000)
	DB.SetMaxIdleConns(1000)
	DB.SetConnMaxLifetime(time.Minute * 60) // mysql default conn timeout=8h, should < mysql_timeout
	err = DB.Ping()
	if err != nil {
		log.Println("[数据库错误日志]Mysql 链接失败:", err.Error())
		os.Exit(-1)
	}
}
