package lib

import (
	"context"
	"time"

	"github.com/go-redis/redis/v8"
)

var RedisCtx = context.Background()
var Rdb *redis.Client

func Redis_Load() {
	Rdb = redis.NewClient(&redis.Options{
		Addr:     REDISHOST,
		Password: REDISPASS, // no password set
		DB:       0,         // use default DB
	})
}
func RSet(k, v string, i int64) (bool, error) {
	return Rdb.SetNX(RedisCtx, k, v, time.Duration(i)*time.Second).Result()
}
func RGet(k string) (string, error) {
	return Rdb.Get(RedisCtx, k).Result()
}
func RDel(k string) (int64, error) {
	return Rdb.Del(RedisCtx, k).Result()
}
