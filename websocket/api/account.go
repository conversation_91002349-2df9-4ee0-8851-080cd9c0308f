package api

type AccountRequest struct {
	Address string `url:"address"`
	Visible bool   `url:"visible"`
}

type Account struct {
	AccountName           string          `json:"account_name"`
	Address               string          `json:"address"`
	Balance               int64           `json:"balance"`
	Asset                 []KeyValue      `json:"asset"`
	CreateTime            int64           `json:"create_time"`
	LatestOperationTime   int64           `json:"latest_operation_time"`
	LatestConsumeTime     int64           `json:"latest_consume_time"`
	LatestConsumeFreeTime int64           `json:"latest_consume_free_time"`
	AccountResource       AccountResource `json:"account_resource"`
	AssetV2               []KeyValue      `json:"assetV2"`
	FreeAssetNetUsageV2   []KeyValue      `json:"free_asset_net_usageV2"`
	FreeNetUsage          int64           `json:"free_net_usage"`
	OwnerPermission       Permission      `json:"owner_permission"`
	ActivePermission      []Permission    `json:"active_permission"`
}

type KeyValue struct {
	Key   string `json:"key"`
	Value int64  `json:"value"`
}

type AccountResource struct {
	LastConsumeTimeForEnergy int64 `json:"last_consume_time_for_energy"`
}

type Permission struct {
	PermissionName string `json:"permission_name"`
	Type           string `json:"type"`
	Id             int32  `json:"id"`
	Threshold      int32  `json:"threshold"`
	Operations     string `json:"operations"`
	Keys           []struct {
		Address string `json:"address"`
		Weight  int32  `json:"weight"`
	} `json:"keys"`
}
