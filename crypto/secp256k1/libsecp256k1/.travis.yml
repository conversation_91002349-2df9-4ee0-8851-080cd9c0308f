language: c
sudo: false
addons:
  apt:
    packages: libgmp-dev
compiler:
  - clang
  - gcc
cache:
  directories:
  - src/java/guava/
env:
  global:
    - FIELD=auto  BIGNUM=auto  SCALAR=auto  ENDOMORPHISM=no  STATICPRECOMPUTATION=yes  ASM=no  BUILD=check  EXTRAFLAGS=  HOST=  ECDH=no  RECOVERY=no  EXPERIMENTAL=no
    - GUAVA_URL=https://search.maven.org/remotecontent?filepath=com/google/guava/guava/18.0/guava-18.0.jar GUAVA_JAR=src/java/guava/guava-18.0.jar
  matrix:
    - SCALAR=32bit    RECOVERY=yes
    - SCALAR=32bit    FIELD=32bit       ECDH=yes  EXPERIMENTAL=yes
    - SCALAR=64bit
    - FIELD=64bit     RECOVERY=yes
    - FIELD=64bit     ENDOMORPHISM=yes
    - FIELD=64bit     ENDOMORPHISM=yes  ECDH=yes EXPERIMENTAL=yes
    - FIELD=64bit                       ASM=x86_64
    - FIELD=64bit     ENDOMORPHISM=yes  ASM=x86_64
    - FIELD=32bit     ENDOMORPHISM=yes
    - BIGNUM=no
    - BIGNUM=no       ENDOMORPHISM=yes RECOVERY=yes EXPERIMENTAL=yes
    - BIGNUM=no       STATICPRECOMPUTATION=no
    - BUILD=distcheck
    - EXTRAFLAGS=CPPFLAGS=-DDETERMINISTIC
    - EXTRAFLAGS=CFLAGS=-O0
    - BUILD=check-java ECDH=yes EXPERIMENTAL=yes
matrix:
  fast_finish: true
  include:
    - compiler: clang
      env: HOST=i686-linux-gnu ENDOMORPHISM=yes
      addons:
        apt:
          packages:
            - gcc-multilib
            - libgmp-dev:i386
    - compiler: clang
      env: HOST=i686-linux-gnu
      addons:
        apt:
          packages:
            - gcc-multilib
    - compiler: gcc
      env: HOST=i686-linux-gnu ENDOMORPHISM=yes
      addons:
        apt:
          packages:
            - gcc-multilib
    - compiler: gcc
      env: HOST=i686-linux-gnu
      addons:
        apt:
          packages:
            - gcc-multilib
            - libgmp-dev:i386
before_install: mkdir -p `dirname $GUAVA_JAR`
install: if [ ! -f $GUAVA_JAR ]; then wget $GUAVA_URL -O $GUAVA_JAR; fi
before_script: ./autogen.sh
script:
 - if [ -n "$HOST" ]; then export USE_HOST="--host=$HOST"; fi
 - if [ "x$HOST" = "xi686-linux-gnu" ]; then export CC="$CC -m32"; fi
 - ./configure --enable-experimental=$EXPERIMENTAL --enable-endomorphism=$ENDOMORPHISM --with-field=$FIELD --with-bignum=$BIGNUM --with-scalar=$SCALAR --enable-ecmult-static-precomputation=$STATICPRECOMPUTATION --enable-module-ecdh=$ECDH --enable-module-recovery=$RECOVERY $EXTRAFLAGS $USE_HOST && make -j2 $BUILD
os: linux
