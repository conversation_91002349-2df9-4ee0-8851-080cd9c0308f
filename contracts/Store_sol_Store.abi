[{"constant": true, "inputs": [{"name": "", "type": "bytes32"}], "name": "items", "outputs": [{"name": "", "type": "bytes32"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": true, "inputs": [], "name": "version", "outputs": [{"name": "", "type": "string"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": false, "inputs": [{"name": "key", "type": "bytes32"}, {"name": "value", "type": "bytes32"}], "name": "setItem", "outputs": [], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"name": "_version", "type": "string"}], "payable": false, "stateMutability": "nonpayable", "type": "constructor"}, {"anonymous": false, "inputs": [{"indexed": false, "name": "key", "type": "bytes32"}, {"indexed": false, "name": "value", "type": "bytes32"}], "name": "ItemSet", "type": "event"}]