package lib

import (
	"Mapi/config"
	"Mapi/dal"
	"Mapi/model/entity"
	"context"
	"encoding/json"
	"fmt"
	"github.com/spf13/cast"
	"log"
	"math/rand"
	"net/url"
	"strconv"
	"time"

	"github.com/gorilla/websocket"
	"github.com/shopspring/decimal"
	"go.uber.org/fx"
)

var wsaddr = config.NewConfig().Ws.Wsurl

func NewWsServerUrl() url.URL {
	queryParams := url.Values{}
	queryParams.Set("id", config.NewConfig().Ws.Wsid)
	u := url.URL{Scheme: "ws", Host: wsaddr, Path: "/ws", RawQuery: queryParams.Encode()}
	return u
}

func WsServer(wsUrl url.URL, lc fx.Lifecycle) {
	log.Printf("正在连接 %s", wsUrl)

	lc.Append(fx.Hook{
		OnStart: func(ctx context.Context) error {
			go func() {
				connect(wsUrl) // 在新的goroutine中启动连接
			}()
			return nil
		},
	})

}

var (
	conn        *websocket.Conn
	SendCh      = make(chan []byte)
	reconnect   = make(chan struct{})
	IsConnected bool
)

// 断线重连
func connect(u url.URL) {
	for {
		c, _, err := websocket.DefaultDialer.Dial(u.String(), nil)
		if err != nil {
			log.Printf("连接失败: %v", err)
			time.Sleep(2 * time.Second)
			continue
		}
		conn = c
		IsConnected = true
		log.Printf("已连接到 %s", u.String())

		go receiveMessage()
		go sendMessageLoop()

		<-reconnect
	}
}

func sendMessageLoop() {
	for {
		select {
		case msg := <-SendCh:
			if err := conn.WriteMessage(websocket.TextMessage, msg); err != nil {
				log.Printf("write error: %v", err)
				IsConnected = false
				reconnect <- struct{}{}
			}
		}
	}
}

func receiveMessage() {
	for {
		_, message, err := conn.ReadMessage()
		if err != nil {
			log.Printf("read error: %v", err)
			IsConnected = false
			reconnect <- struct{}{}
			break
		}
		//收到消息逻辑处理
		//fmt.Printf("received: %s", message)
		var data map[string]string
		json.Unmarshal(message, &data)
		fmt.Println(data)
		//币种
		tokenName := data["addressName"]
		//金额
		amount := data["amount"]
		//付款地址
		paymentAddress := data["paymentAddress"]
		//收款地址
		receivingAddress := data["receivingAddress"]
		//交易时间
		txtime := data["time"]
		//交易类型
		transferType := data["transferType"]
		//Hash
		txid := data["txid"]
		//链
		chain := data["chain"]
		//判断是否是TRX
		if tokenName == "TRX" {
			addr, ae := dal.Address.Where(dal.Address.Trxaddr.Eq(receivingAddress)).First()
			if addr == nil || ae != nil {
				continue
			}
			dal.Address.Where(dal.Address.ID.Eq(addr.ID)).Update(dal.Address.UpdateMoney, 1)
			checkmch, _ := dal.Merchant.Where(dal.Merchant.AddressID.Eq(cast.ToInt32(addr.ID))).First()

			if checkmch != nil {
				m64 := cast.ToFloat64(amount)
				newmoney, _ := decimal.NewFromFloat(*checkmch.FeeMoney).Add(decimal.NewFromFloat(m64)).Float64()
				dal.Merchant.Where(dal.Merchant.ID.Eq(cast.ToInt64(checkmch.ID))).Update(dal.Merchant.FeeMoney, newmoney)
				str := "后台充值手续费(TRC)"
				dal.Tran.Create(&entity.Tran{
					UserID:        Int64ToInt32Ptr(checkmch.ID),
					UserType:      Int64ToInt32Ptr(int64(1)),
					Type:          Int64ToInt32Ptr(int64(10)),
					Money:         &m64,
					Orderid:       &str,
					Rorderid:      &str,
					Transactionid: &str,
					Username:      checkmch.Username,
					Ctime:         Int64ToInt32Ptr(time.Now().Unix()),
				})
				continue
			}

			//mch, me := dal.Merchant.Where(dal.Merchant.ID.Eq(int64(*addr.MerchantID))).First()
			//if mch == nil || me != nil {
			//	continue
			//}
			//if mch.ID != 1010 && mch.ID != 1011 {
			//	continue
			//}
			if addr.Callbackurl != nil {
				urlStr := *addr.Callbackurl + "?amount=" + data["amount"] + "&paymentAddress=" + data["paymentAddress"] + "&receivingAddress=" + data["receivingAddress"] + "&txid=" + data["txid"] + "&txtime=" + data["time"] + "&addressName=" + data["addressName"]
				fmt.Println(urlStr)
				aa := Get(urlStr)
				fmt.Println(aa)
			}
			continue
		}
		if tokenName == "ETH" {
			addr, ae := dal.Address.Where(dal.Address.Ethaddr.Eq(receivingAddress)).First()
			if addr == nil || ae != nil {
				continue
			}
			dal.Address.Where(dal.Address.ID.Eq(addr.ID)).Update(dal.Address.UpdateMoney, 1)
			checkmch, _ := dal.Merchant.Where(dal.Merchant.AddressID.Eq(cast.ToInt32(addr.ID))).First()
			if checkmch != nil {
				m64 := cast.ToFloat64(amount)
				newmoney, _ := decimal.NewFromFloat(*checkmch.EthFeemoney).Add(decimal.NewFromFloat(m64)).Float64()
				dal.Merchant.Where(dal.Merchant.ID.Eq(cast.ToInt64(checkmch.ID))).Update(dal.Merchant.EthFeemoney, newmoney)
				str := "后台充值手续费(ERC)"
				dal.Tran.Create(&entity.Tran{
					UserID:        Int64ToInt32Ptr(checkmch.ID),
					UserType:      Int64ToInt32Ptr(int64(1)),
					Type:          Int64ToInt32Ptr(int64(12)),
					Money:         &m64,
					Orderid:       &str,
					Rorderid:      &str,
					Transactionid: &str,
					Username:      checkmch.Username,
					Ctime:         Int64ToInt32Ptr(time.Now().Unix()),
				})
			}
		}
		if tokenName == "BNB" {
			addr, ae := dal.Address.Where(dal.Address.Ethaddr.Eq(receivingAddress)).First()
			if addr == nil || ae != nil {
				continue
			}
			dal.Address.Where(dal.Address.ID.Eq(addr.ID)).Update(dal.Address.UpdateMoney, 1)
			checkmch, _ := dal.Merchant.Where(dal.Merchant.AddressID.Eq(cast.ToInt32(addr.ID))).First()
			if checkmch != nil {
				m64 := cast.ToFloat64(amount)
				newmoney, _ := decimal.NewFromFloat(*checkmch.BscFeemoney).Add(decimal.NewFromFloat(m64)).Float64()
				dal.Merchant.Where(dal.Merchant.ID.Eq(cast.ToInt64(checkmch.ID))).Update(dal.Merchant.BscFeemoney, newmoney)
				str := "后台充值手续费(BSC)"
				dal.Tran.Create(&entity.Tran{
					UserID:        Int64ToInt32Ptr(checkmch.ID),
					UserType:      Int64ToInt32Ptr(int64(1)),
					Type:          Int64ToInt32Ptr(int64(21)),
					Money:         &m64,
					Orderid:       &str,
					Rorderid:      &str,
					Transactionid: &str,
					Username:      checkmch.Username,
					Ctime:         Int64ToInt32Ptr(time.Now().Unix()),
				})
			}
		}
		//判断是否是USDT交易
		if tokenName != "USDT" || transferType != "收入" {
			continue
		}
		var addr *entity.Address
		var ae error
		//判断是波场还是以太坊
		if chain == "TRC" {
			//波场记录订单
			addr, ae = dal.Address.Where(dal.Address.Trxaddr.Eq(receivingAddress)).First()
			if addr == nil || ae != nil {
				continue
			}
			dal.Address.Where(dal.Address.ID.Eq(addr.ID)).Update(dal.Address.UpdateMoney, 1)
			dal.Trcorder.Create(&entity.Trcorder{
				Transactionid:  txid,
				From:           paymentAddress,
				To:             receivingAddress,
				Value:          amount,
				BlockTimestamp: int32(TimeToTimeStamp(txtime)),
			})
			//监听是不是订单的，不是订单的走着 直接给子钱包充钱
			order, oe := dal.Order.Where(dal.Order.Trxadd.Eq(receivingAddress)).Where(dal.Order.Status.Eq(0)).Limit(1).Find()
			if len(order) == 0 || oe != nil {
				//这个是用户子地址的，因为没有订单，所以在这写
				tm := time.Now()
				var ordertype string
				var orderid string
				var rorderid string
				randomNumber := rand.Intn(9000) + 1000
				//生成系统订单
				ordertype = "Trc20"
				orderid = "CT" + tm.Format("060102150405") + cast.ToString(randomNumber)
				//随机生成的商户的订单 id
				rorderid = "RO" + tm.Format("060102150405") + cast.ToString(randomNumber)
				mch, _ := dal.Merchant.Where(dal.Merchant.ID.Eq(cast.ToInt64(addr.MerchantID))).First()
				nmoney, _ := strconv.ParseFloat(amount, 64)
				dqrate := GetExchange()
				rmb := "0"
				rate := 0.00

				if *mch.Ratetype == "1" { //固定汇率
					rate, _ = decimal.NewFromFloat(float64(*mch.Rate)).Truncate(2).Float64()
				} else { //浮动汇率
					if *mch.Floatratetype == "1" { //百分比
						mchrv, _ := strconv.ParseFloat(*mch.Floatratedvalue, 64)
						der := decimal.NewFromFloat(dqrate)
						dej, _ := decimal.NewFromFloat(dqrate).Mul(decimal.NewFromFloat(mchrv)).Div(decimal.NewFromFloat(100)).RoundCeil(2).Float64()
						rate, _ = der.Sub(decimal.NewFromFloat(dej)).RoundCeil(2).Float64()
					} else { //固定值
						mchrv, _ := strconv.ParseFloat(*mch.Floatratedvalue, 64)
						der := decimal.NewFromFloat(dqrate)
						dej := decimal.NewFromFloat(mchrv)
						rate, _ = der.Sub(dej).RoundCeil(2).Float64()
					}
				}

				a := decimal.NewFromFloat(nmoney)
				nmoney, _ = a.RoundCeil(2).Float64()
				if nmoney < 5 && mch.ID != 1 {
					log.Printf("商户 %d 充值金额小于10U:金额为：%v,充值地址是：%v", mch.Username, nmoney, paymentAddress)
					continue
				}
				log.Printf("商户 %d 充值金额为：%v,充值地址是：%v", mch.Username, nmoney, paymentAddress)

				score, serr := getAddressScore(paymentAddress)
				strScore := cast.ToString(score)
				//还要添加 扣费流程
				idStr := fmt.Sprintf("%d", mch.ID)
				ReturnurlStr := fmt.Sprintf("%s", "")
				newOrder := &entity.Order{
					MerchantID:    &idStr,
					Appkey:        mch.Appkey,
					Orderid:       &orderid,
					Rorderid:      &rorderid,
					Trxadd:        &receivingAddress,
					Addrid:        Int64ToInt32Ptr(addr.ID),
					Money:         &amount,
					Realmoney:     &amount,
					Status:        Int64ToInt32Ptr(2),
					Callbackurl:   addr.Callbackurl,
					Callbacknum:   Int64ToInt32Ptr(0),
					MerchantName:  mch.Username,
					Ctime:         Int64ToInt32Ptr(time.Now().Unix()),
					Returnurl:     &ReturnurlStr,
					Rmb:           &rmb,
					Rate:          Float64ToStringPtr(rate),
					Chain:         &ordertype,
					Transactionid: &txid,
					Etime:         Int64ToInt32Ptr(time.Now().Unix()),
					Fromaddr:      &paymentAddress,
				}
				if serr == nil {
					newOrder.Score = &strScore
				}
				err := dal.Order.Create(newOrder)
				if err != nil {
					log.Printf("创建订单失败：" + err.Error())
					continue
				}

				//var address *entity.Address
				//address, _ = dal.Address.Where(dal.Address.Trxaddr.Eq(receivingAddress)).Where(dal.Address.MerchantID.Eq(int32(mch.ID))).First()

				//解锁地址
				//dal.Lockaddress.Where(dal.Lockaddress.Addrid.Eq(int32(address.ID))).Where(dal.Lockaddress.Money.Eq(amount)).Update(dal.Lockaddress.Islock, 1)
				//dal.Address.Where(dal.Address.ID.Eq(address.ID)).Update(dal.Address.Lock, 0)
				//商户余额上分
				//商户手续费处理 改为固定扣费
				emoney64, _ := strconv.ParseFloat(amount, 64)
				demoney64 := decimal.NewFromFloat(emoney64)
				dmchfee64 := decimal.NewFromFloat(*mch.Fee)
				d1000 := decimal.NewFromInt(1000)
				//订单金额*商户费率/1000=商户手续费
				mchfeed := demoney64.Mul(dmchfee64).Div(d1000)
				mchfee, _ := mchfeed.RoundCeil(6).Float64()
				ying64, _ := decimal.NewFromFloat(emoney64).Sub(decimal.NewFromFloat(mchfee)).Float64()
				madd, _ := decimal.NewFromFloat(ying64).Add(decimal.NewFromFloat(*mch.Money)).Float64()
				if *mch.CommissionPrice > 0 {
					ying64, _ = decimal.NewFromFloat(emoney64).Sub(decimal.NewFromFloat(*mch.CommissionPrice)).Float64()
					madd, _ = decimal.NewFromFloat(ying64).Add(decimal.NewFromFloat(*mch.Money)).Float64()
				}

				dal.Merchant.Where(dal.Merchant.ID.Eq(mch.ID)).Update(dal.Merchant.Money, madd)
				dal.Balance.Create(&entity.Balance{
					MerchantID:   Int64ToInt32Ptr(mch.ID),
					MerchantName: mch.Username,
					Trc:          &madd,
					Erc:          mch.ErcMoney,
					Bsc:          mch.BscMoney,
					Ctime:        Int64ToInt32Ptr(time.Now().Unix()),
				})
				str := "收入 " + cast.ToString(nmoney) + " 手续费 " + cast.ToString(*mch.CommissionPrice)
				log.Printf("商户 %d 收到充值通知，充值金额为：%v，手续费为：%v", mch.Username, nmoney, *mch.CommissionPrice)
				//商户余额变动记录
				dal.Tran.Create(&entity.Tran{
					UserID:        Int64ToInt32Ptr(mch.ID),
					UserType:      Int64ToInt32Ptr(int64(1)),
					Type:          Int64ToInt32Ptr(int64(1)),
					Money:         &ying64,
					Orderid:       &orderid,
					Rorderid:      &rorderid,
					Transactionid: &txid,
					Username:      mch.Username,
					Remark:        &str,
					Ctime:         Int64ToInt32Ptr(time.Now().Unix()),
				})

				if *mch.CommissionPrice > 0 {
					//商户手续费变动记录
					dal.Tran.Create(&entity.Tran{
						UserID:        Int64ToInt32Ptr(mch.ID),
						UserType:      Int64ToInt32Ptr(int64(1)),
						Type:          Int64ToInt32Ptr(int64(2)),
						Money:         &*mch.CommissionPrice,
						Orderid:       &orderid,
						Rorderid:      &rorderid,
						Transactionid: &txid,
						Username:      mch.Username,
						Ctime:         Int64ToInt32Ptr(time.Now().Unix()),
					})
				} else {
					if mchfee != 0 {
						//商户手续费变动记录
						dal.Tran.Create(&entity.Tran{
							UserID:        Int64ToInt32Ptr(mch.ID),
							UserType:      Int64ToInt32Ptr(int64(1)),
							Type:          Int64ToInt32Ptr(int64(2)),
							Money:         &mchfee,
							Orderid:       &orderid,
							Rorderid:      &rorderid,
							Transactionid: &txid,
							Username:      mch.Username,
							Ctime:         Int64ToInt32Ptr(time.Now().Unix()),
						})
					}
				}
			}

		} else if chain == "ERC" {
			//以太坊记录订单
			addr, ae = dal.Address.Where(dal.Address.Ethaddr.Eq(receivingAddress)).First()
			if addr == nil || ae != nil {
				continue
			}
			dal.Address.Where(dal.Address.ID.Eq(addr.ID)).Update(dal.Address.UpdateMoney, 1)
			dal.Ercorder.Create(&entity.Ercorder{
				Transactionid:  txid,
				From:           paymentAddress,
				To:             receivingAddress,
				Value:          amount,
				BlockTimestamp: int32(TimeToTimeStamp(txtime)),
			})
		} else if chain == "BSC" {
			//币安链记录订单
			addr, ae = dal.Address.Where(dal.Address.Ethaddr.Eq(receivingAddress)).First()
			if addr == nil || ae != nil {
				continue
			}
			dal.Address.Where(dal.Address.ID.Eq(addr.ID)).Update(dal.Address.UpdateMoney, 1)
			dal.Bscorder.Create(&entity.Bscorder{
				Transactionid:  txid,
				From:           paymentAddress,
				To:             receivingAddress,
				Value:          amount,
				BlockTimestamp: int32(TimeToTimeStamp(txtime)),
			})
		}
		//逻辑处理
		if addr.Callbackurl != nil {
			//websocket 监听转账回调
			url := *addr.Callbackurl + "?amount=" + data["amount"] + "&paymentAddress=" + data["paymentAddress"] + "&receivingAddress=" + data["receivingAddress"] + "&txid=" + data["txid"] + "&txtime=" + data["time"] + "&addressName=" + data["addressName"]
			fmt.Println(url)
			aa := Get(url)
			fmt.Println(aa)
		}
		order, oe := dal.Order.Where(dal.Order.Trxadd.Eq(receivingAddress)).Where(dal.Order.Status.Eq(0)).Limit(1).Find()
		if len(order) == 0 || oe != nil {
			continue
		}
		mch, me := dal.Merchant.Where(dal.Merchant.ID.Eq(int64(*addr.MerchantID))).First()
		if mch == nil || me != nil {
			continue
		}
		for _, r := range order {
			m := *r.Money
			//v := decimal.NewFromFloat(float64(StrToint64(amount))).Div(decimal.NewFromFloat(float64(1000000)))
			v := decimal.NewFromFloat(StringToFloat64(amount))
			fmt.Println(v)
			fmt.Println(m)
			//判断是否下浮
			floatdown := 0
			if (v).GreaterThan(decimal.NewFromFloat(3)) {
				//float64的订单金额
				m64, _ := strconv.ParseFloat(m, 64)
				//decimal的订单金额-1
				mde := decimal.NewFromFloat(m64).Sub(decimal.NewFromFloat(float64(2)))
				//判断实收金额 >= 订单金额-2 && 实收金额 <= 订单金额 成功
				if (v).GreaterThanOrEqual(mde) && (v).LessThan(decimal.NewFromFloat(m64)) {
					if *mch.IsFloatdown == "1" {
						floatdown = 1
					} else {
						floatdown = 2
					}
					m = v.String()
				} else if v.String() == m {
					floatdown = 1
				}
			} else {
				if v.String() == m {
					floatdown = 1
				}
			}
			//校验金额完成 计费处理
			if floatdown == 1 {
				emoney64, _ := strconv.ParseFloat(m, 64)
				//更新订单状态
				score, serr := getAddressScore(paymentAddress)
				strScore := cast.ToString(score)
				if serr != nil {
					dal.Order.Where(dal.Order.ID.Eq(r.ID)).Updates(&entity.Order{
						Transactionid: &txid,
						Status:        Int64ToInt32Ptr(2),
						Etime:         Int64ToInt32Ptr(time.Now().Unix()),
						Realmoney:     &m,
						Fromaddr:      &paymentAddress,
					})
				} else {
					dal.Order.Where(dal.Order.ID.Eq(r.ID)).Updates(&entity.Order{
						Transactionid: &txid,
						Status:        Int64ToInt32Ptr(2),
						Etime:         Int64ToInt32Ptr(time.Now().Unix()),
						Realmoney:     &m,
						Fromaddr:      &paymentAddress,
						Score:         &strScore,
					})
				}
				//dal.Order.Where(dal.Order.ID.Eq(r.ID)).Updates(&entity.Order{
				//	Transactionid: &txid,
				//	Status:        Int64ToInt32Ptr(2),
				//	Etime:         Int64ToInt32Ptr(time.Now().Unix()),
				//	Realmoney:     &m,
				//})
				var address *entity.Address
				if chain == "TRC" {
					address, _ = dal.Address.Where(dal.Address.Trxaddr.Eq(receivingAddress)).Where(dal.Address.MerchantID.Eq(int32(mch.ID))).First()
				} else {
					address, _ = dal.Address.Where(dal.Address.Ethaddr.Eq(receivingAddress)).Where(dal.Address.MerchantID.Eq(int32(mch.ID))).First()
				}
				//解锁地址
				dal.Lockaddress.Where(dal.Lockaddress.Addrid.Eq(int32(address.ID))).Where(dal.Lockaddress.Money.Eq(*r.Money)).Update(dal.Lockaddress.Islock, 1)
				dal.Address.Where(dal.Address.ID.Eq(address.ID)).Update(dal.Address.Lock, 0)
				//商户余额上分
				if chain == "TRC" {
					m64, _ := strconv.ParseFloat(m, 64)
					//商户手续费处理
					demoney64 := decimal.NewFromFloat(emoney64)
					dmchfee64 := decimal.NewFromFloat(*mch.Fee)
					d1000 := decimal.NewFromInt(1000)
					//订单金额*商户费率/1000=商户手续费
					mchfeed := demoney64.Mul(dmchfee64).Div(d1000)
					mchfee, _ := mchfeed.RoundCeil(6).Float64()
					ying64, _ := decimal.NewFromFloat(m64).Sub(decimal.NewFromFloat(mchfee)).Float64()
					madd, _ := decimal.NewFromFloat(ying64).Add(decimal.NewFromFloat(*mch.Money)).Float64()
					if *mch.CommissionPrice > 0 {
						ying64, _ = decimal.NewFromFloat(m64).Sub(decimal.NewFromFloat(*mch.CommissionPrice)).Float64()
						madd, _ = decimal.NewFromFloat(ying64).Add(decimal.NewFromFloat(*mch.Money)).Float64()
					}

					dal.Merchant.Where(dal.Merchant.ID.Eq(int64(mch.ID))).Update(dal.Merchant.Money, madd)
					dal.Balance.Create(&entity.Balance{
						MerchantID:   Int64ToInt32Ptr(mch.ID),
						MerchantName: mch.Username,
						Trc:          &madd,
						Erc:          mch.ErcMoney,
						Bsc:          mch.BscMoney,
						Ctime:        Int64ToInt32Ptr(time.Now().Unix()),
					})
					str := "收入 " + cast.ToString(m64) + " 手续费 " + cast.ToString(mchfee)
					//商户余额变动记录
					dal.Tran.Create(&entity.Tran{
						UserID:        Int64ToInt32Ptr(mch.ID),
						UserType:      Int64ToInt32Ptr(int64(1)),
						Type:          Int64ToInt32Ptr(int64(1)),
						Money:         &ying64,
						Orderid:       r.Orderid,
						Rorderid:      r.Rorderid,
						Transactionid: &txid,
						Username:      mch.Username,
						Remark:        &str,
						Ctime:         Int64ToInt32Ptr(time.Now().Unix()),
					})
					//商户手续费扣除
					//newfeemoney, _ := decimal.NewFromFloat(*mch.FeeMoney).Sub(decimal.NewFromFloat(mchfee)).Float64()
					//dal.Merchant.Where(dal.Merchant.ID.Eq(mch.ID)).Update(dal.Merchant.FeeMoney, newfeemoney)

					if *mch.CommissionPrice > 0 {
						//商户手续费变动记录
						dal.Tran.Create(&entity.Tran{
							UserID:        Int64ToInt32Ptr(mch.ID),
							UserType:      Int64ToInt32Ptr(int64(1)),
							Type:          Int64ToInt32Ptr(int64(2)),
							Money:         &*mch.CommissionPrice,
							Orderid:       r.Orderid,
							Rorderid:      r.Rorderid,
							Transactionid: &txid,
							Username:      mch.Username,
							Ctime:         Int64ToInt32Ptr(time.Now().Unix()),
						})
					} else {
						if mchfee != 0 {
							//商户手续费变动记录
							dal.Tran.Create(&entity.Tran{
								UserID:        Int64ToInt32Ptr(mch.ID),
								UserType:      Int64ToInt32Ptr(int64(1)),
								Type:          Int64ToInt32Ptr(int64(2)),
								Money:         &mchfee,
								Orderid:       r.Orderid,
								Rorderid:      r.Rorderid,
								Transactionid: &txid,
								Username:      mch.Username,
								Ctime:         Int64ToInt32Ptr(time.Now().Unix()),
							})
						}
					}

					//如果有代理 执行代理提成
					if *mch.AgentID != 0 {
						agent, _ := dal.Agent.Where(dal.Agent.ID.Eq(cast.ToInt64(mch.AgentID))).First()
						//demoney64 := decimal.NewFromFloat(emoney64)
						dagfee64 := decimal.NewFromFloat(*agent.Fee)
						//d1000 := decimal.NewFromInt(1000)
						//订单金额*代理费率/1000=代理提成
						agfeed := demoney64.Mul(dagfee64).Div(d1000)
						yingagadd, _ := mchfeed.Sub(agfeed).Float64()
						aadd, _ := decimal.NewFromFloat(yingagadd).Add(decimal.NewFromFloat(*agent.Money)).Float64()
						dal.Agent.Where(dal.Agent.ID.Eq(int64(agent.ID))).Update(dal.Agent.Money, aadd)
						//商户余额变动记录
						dal.AgentTran.Create(&entity.AgentTran{
							UserID:        Int64ToInt32Ptr(agent.ID),
							Type:          Int64ToInt32Ptr(int64(1)),
							Money:         &yingagadd,
							Orderid:       r.Orderid,
							Rorderid:      r.Rorderid,
							Transactionid: &txid,
							Username:      agent.Username,
							Ctime:         Int64ToInt32Ptr(time.Now().Unix()),
						})
					}
				} else if chain == "ERC" {
					m64, _ := strconv.ParseFloat(m, 64)
					//商户手续费处理
					demoney64 := decimal.NewFromFloat(emoney64)
					dmchfee64 := decimal.NewFromFloat(*mch.Ethfee)
					d1000 := decimal.NewFromInt(1000)
					//订单金额*商户费率/1000=商户手续费
					mchfeed := demoney64.Mul(dmchfee64).Div(d1000)
					mchfee, _ := mchfeed.RoundCeil(6).Float64()
					ying64, _ := decimal.NewFromFloat(m64).Sub(decimal.NewFromFloat(mchfee)).Float64()
					madd, _ := decimal.NewFromFloat(ying64).Add(decimal.NewFromFloat(*mch.ErcMoney)).Float64()
					dal.Merchant.Where(dal.Merchant.ID.Eq(int64(mch.ID))).Update(dal.Merchant.ErcMoney, madd)
					dal.Balance.Create(&entity.Balance{
						MerchantID:   Int64ToInt32Ptr(mch.ID),
						MerchantName: mch.Username,
						Trc:          mch.Money,
						Erc:          &madd,
						Bsc:          mch.BscMoney,
						Ctime:        Int64ToInt32Ptr(time.Now().Unix()),
					})
					str := "收入 " + cast.ToString(m64) + " 手续费 " + cast.ToString(mchfee)
					//商户余额变动记录
					dal.Tran.Create(&entity.Tran{
						UserID:        Int64ToInt32Ptr(mch.ID),
						UserType:      Int64ToInt32Ptr(int64(1)),
						Type:          Int64ToInt32Ptr(int64(5)),
						Money:         &ying64,
						Orderid:       r.Orderid,
						Rorderid:      r.Rorderid,
						Transactionid: &txid,
						Username:      mch.Username,
						Remark:        &str,
						Ctime:         Int64ToInt32Ptr(time.Now().Unix()),
					})
					//商户手续费扣除
					//newfeemoney, _ := decimal.NewFromFloat(*mch.EthFeemoney).Sub(decimal.NewFromFloat(mchfee)).Float64()
					//dal.Merchant.Where(dal.Merchant.ID.Eq(mch.ID)).Update(dal.Merchant.EthFeemoney, newfeemoney)
					if mchfee != 0 {
						//商户手续费变动记录
						dal.Tran.Create(&entity.Tran{
							UserID:        Int64ToInt32Ptr(mch.ID),
							UserType:      Int64ToInt32Ptr(int64(1)),
							Type:          Int64ToInt32Ptr(int64(6)),
							Money:         &mchfee,
							Orderid:       r.Orderid,
							Rorderid:      r.Rorderid,
							Transactionid: &txid,
							Username:      mch.Username,

							Ctime: Int64ToInt32Ptr(time.Now().Unix()),
						})
					}
					//如果有代理 执行代理提成
					if *mch.AgentID != 0 {
						agent, _ := dal.Agent.Where(dal.Agent.ID.Eq(cast.ToInt64(mch.AgentID))).First()
						//demoney64 := decimal.NewFromFloat(emoney64)
						dagfee64 := decimal.NewFromFloat(*agent.Ethfee)
						//d1000 := decimal.NewFromInt(1000)
						//订单金额*代理费率/1000=代理提成
						agfeed := demoney64.Mul(dagfee64).Div(d1000)
						yingagadd, _ := mchfeed.Sub(agfeed).Float64()
						aadd, _ := decimal.NewFromFloat(yingagadd).Add(decimal.NewFromFloat(*agent.Ercmoney)).Float64()
						dal.Agent.Where(dal.Agent.ID.Eq(int64(agent.ID))).Update(dal.Agent.Ercmoney, aadd)
						//商户余额变动记录
						dal.AgentTran.Create(&entity.AgentTran{
							UserID:        Int64ToInt32Ptr(agent.ID),
							Type:          Int64ToInt32Ptr(int64(2)),
							Money:         &yingagadd,
							Orderid:       r.Orderid,
							Rorderid:      r.Rorderid,
							Transactionid: &txid,
							Username:      agent.Username,
							Ctime:         Int64ToInt32Ptr(time.Now().Unix()),
						})
					}
				} else if chain == "BSC" {
					m64, _ := strconv.ParseFloat(m, 64)
					//商户手续费处理
					demoney64 := decimal.NewFromFloat(emoney64)
					dmchfee64 := decimal.NewFromFloat(*mch.Bscfee)
					d1000 := decimal.NewFromInt(1000)
					//订单金额*商户费率/1000=商户手续费
					mchfeed := demoney64.Mul(dmchfee64).Div(d1000)
					mchfee, _ := mchfeed.RoundCeil(6).Float64()
					ying64, _ := decimal.NewFromFloat(m64).Sub(decimal.NewFromFloat(mchfee)).Float64()
					madd, _ := decimal.NewFromFloat(ying64).Add(decimal.NewFromFloat(*mch.BscMoney)).Float64()
					dal.Merchant.Where(dal.Merchant.ID.Eq(int64(mch.ID))).Update(dal.Merchant.BscMoney, madd)
					dal.Balance.Create(&entity.Balance{
						MerchantID:   Int64ToInt32Ptr(mch.ID),
						MerchantName: mch.Username,
						Trc:          mch.Money,
						Erc:          mch.ErcMoney,
						Bsc:          &madd,
						Ctime:        Int64ToInt32Ptr(time.Now().Unix()),
					})
					str := "收入 " + cast.ToString(m64) + " 手续费 " + cast.ToString(mchfee)
					//商户余额变动记录
					dal.Tran.Create(&entity.Tran{
						UserID:        Int64ToInt32Ptr(mch.ID),
						UserType:      Int64ToInt32Ptr(int64(1)),
						Type:          Int64ToInt32Ptr(int64(15)),
						Money:         &ying64,
						Orderid:       r.Orderid,
						Rorderid:      r.Rorderid,
						Transactionid: &txid,
						Username:      mch.Username,
						Remark:        &str,
						Ctime:         Int64ToInt32Ptr(time.Now().Unix()),
					})
					//商户手续费扣除
					//newfeemoney, _ := decimal.NewFromFloat(*mch.EthFeemoney).Sub(decimal.NewFromFloat(mchfee)).Float64()
					//dal.Merchant.Where(dal.Merchant.ID.Eq(mch.ID)).Update(dal.Merchant.EthFeemoney, newfeemoney)
					if mchfee != 0 {
						//商户手续费变动记录
						dal.Tran.Create(&entity.Tran{
							UserID:        Int64ToInt32Ptr(mch.ID),
							UserType:      Int64ToInt32Ptr(int64(1)),
							Type:          Int64ToInt32Ptr(int64(16)),
							Money:         &mchfee,
							Orderid:       r.Orderid,
							Rorderid:      r.Rorderid,
							Transactionid: &txid,
							Username:      mch.Username,
							Ctime:         Int64ToInt32Ptr(time.Now().Unix()),
						})
					}
					//如果有代理 执行代理提成
					if *mch.AgentID != 0 {
						agent, _ := dal.Agent.Where(dal.Agent.ID.Eq(cast.ToInt64(mch.AgentID))).First()
						//demoney64 := decimal.NewFromFloat(emoney64)
						dagfee64 := decimal.NewFromFloat(*agent.Bscfee)
						//d1000 := decimal.NewFromInt(1000)
						//订单金额*代理费率/1000=代理提成
						agfeed := demoney64.Mul(dagfee64).Div(d1000)
						yingagadd, _ := mchfeed.Sub(agfeed).Float64()
						aadd, _ := decimal.NewFromFloat(yingagadd).Add(decimal.NewFromFloat(*agent.Bscmoney)).Float64()
						dal.Agent.Where(dal.Agent.ID.Eq(int64(agent.ID))).Update(dal.Agent.Bscmoney, aadd)
						//商户余额变动记录
						dal.AgentTran.Create(&entity.AgentTran{
							UserID:        Int64ToInt32Ptr(agent.ID),
							Type:          Int64ToInt32Ptr(int64(3)),
							Money:         &yingagadd,
							Orderid:       r.Orderid,
							Rorderid:      r.Rorderid,
							Transactionid: &txid,
							Username:      agent.Username,
							Ctime:         Int64ToInt32Ptr(time.Now().Unix()),
						})
					}
				}

				//添加订单到回调队列
			} else if floatdown == 2 {
				//存实收金额和hash
				score, serr := getAddressScore(paymentAddress)
				strScore := cast.ToString(score)
				if serr != nil {
					dal.Order.Where(dal.Order.ID.Eq(r.ID)).Updates(&entity.Order{
						Transactionid: &txid,
						Etime:         Int64ToInt32Ptr(time.Now().Unix()),
						Realmoney:     &m,
						Fromaddr:      &paymentAddress,
					})
				} else {
					dal.Order.Where(dal.Order.ID.Eq(r.ID)).Updates(&entity.Order{
						Transactionid: &txid,
						Etime:         Int64ToInt32Ptr(time.Now().Unix()),
						Realmoney:     &m,
						Fromaddr:      &paymentAddress,
						Score:         &strScore,
					})
				}

				//地址锁30天
				now := time.Now()
				future := now.AddDate(0, 0, 30)
				timestamp := future.Unix()
				var address *entity.Address
				if chain == "TRC" {
					address, _ = dal.Address.Where(dal.Address.Trxaddr.Eq(receivingAddress)).Where(dal.Address.MerchantID.Eq(int32(mch.ID))).First()
				} else {
					address, _ = dal.Address.Where(dal.Address.Ethaddr.Eq(receivingAddress)).Where(dal.Address.MerchantID.Eq(int32(mch.ID))).First()
				}
				dal.Lockaddress.Where(dal.Lockaddress.Addrid.Eq(int32(address.ID))).Update(dal.Lockaddress.Etime, Int64ToInt32Ptr(timestamp))
				//dal.Address.Where(dal.Address.ID.Eq(address.ID)).Update(dal.Address.Lock, 0)
			}
		}
	}
}

func SendMessage(msg string) {
	if IsConnected {
		SendCh <- []byte(msg)
	} else {
		log.Printf("ws未连接,无法发送消息")
	}
}
