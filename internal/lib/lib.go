package lib

import (
	"Mapi/dal"
	"Mapi/model/entity"
	"bytes"
	"crypto/md5"
	"crypto/sha256"
	"crypto/tls"
	"encoding/base64"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"io/ioutil"
	"math"
	"math/big"
	"math/rand"
	"net/http"
	"os"
	"path/filepath"
	"reflect"
	"regexp"
	"strconv"
	"strings"
	"time"
)

var (
	Iplist         = []string{}
	Rxf            = 0.00
	usdtRate       = 7.0
	OkExApi        = "https://www.okx.com/v3/c2c/otc-ticker/quotedPrice?t={time}&side=buy&quoteCurrency=CNY&baseCurrency=USDT"
	base58Alphabet = []byte("**********************************************************")
)

func TgSend(id, data, TgToken string) bool {
	Api := "https://api.telegram.org/bot" + TgToken + "/sendMessage"
	transport := &http.Transport{
		TLSClientConfig: &tls.Config{InsecureSkipVerify: true},
	}
	client := &http.Client{
		Timeout:   10 * time.Second,
		Transport: transport,
	}
	type TgPost struct {
		Chat_id string `json:"chat_id"`
		Text    string `json:"text"`
	}
	var post TgPost
	post.Chat_id = id
	post.Text = data
	b, err := json.Marshal(post)
	if err != nil {
		return false
	}
	//post := `{"chat_id":` + id + `,"text":"` + data + `"}`
	reqest, err := http.NewRequest("POST", Api, bytes.NewBuffer(b))
	if err != nil {
		return false
	}
	reqest.Header.Set("Content-Type", "application/json")
	response, err := client.Do(reqest)
	if err != nil {
		return false
	}
	defer response.Body.Close()
	return true
}

func UsdtInt64ToFloat64(num int64) float64 {
	var SUN int64 = 1000000
	return float64(num) / float64(SUN)
}
func UpdateIplist() {
	go func() {
		for {
			ip, _ := ReadFile("iplist.txt")
			ip = strings.ReplaceAll(ip, "\r\n", "\n")
			Iplist = strings.Split(ip, "\n")
			time.Sleep(time.Second * time.Duration(60))
		}
	}()
}
func StrToint64(str string) int64 {
	i, err := strconv.ParseInt(str, 10, 64)
	if err != nil {
		return 0
	}
	return i
}
func GetCurrentAbPathByExecutable() string {
	exePath, err := os.Executable()
	if err != nil {
		return ""
	}
	res, _ := filepath.EvalSymlinks(filepath.Dir(exePath))
	return res
}
func ReadFile(filePath string) (string, error) {
	content, err := ioutil.ReadFile(filePath)
	if err != nil {
		return "", err
	}
	return string(content), err
}
func StringToFloat64(str string) float64 {
	num, err := strconv.ParseFloat(str, 64)
	if err != nil {
		return 0.00
	}
	return num
}
func Shuffle(slice []string) {
	r := rand.New(rand.NewSource(time.Now().UnixNano()))
	time.Sleep(time.Microsecond * 1)
	for len(slice) > 0 {
		n := len(slice)
		randIndex := r.Intn(n)
		slice[n-1], slice[randIndex] = slice[randIndex], slice[n-1]
		slice = slice[:n-1]
	}
}
func CallbackGet(url string) string {
	transport := &http.Transport{
		TLSClientConfig: &tls.Config{InsecureSkipVerify: true},
	}
	client := &http.Client{
		Timeout:   10 * time.Second,
		Transport: transport,
	}
	reqest, err := http.NewRequest("GET", url, nil)
	if err != nil {
		return ""
	}
	//增加header选项
	reqest.Header.Add("User-Agent", "Telegram USDT Server 1.0")
	//处理返回结果
	response, err := client.Do(reqest)
	if err != nil {
		return ""
	}
	defer response.Body.Close()
	r, e := io.ReadAll(response.Body)
	if e != nil {
		return ""
	}
	return string(r)
}
func Get(url string) string {
	transport := &http.Transport{
		TLSClientConfig: &tls.Config{InsecureSkipVerify: true},
	}
	client := &http.Client{
		Timeout:   10 * time.Second,
		Transport: transport,
	}
	reqest, err := http.NewRequest("GET", url, nil)
	if err != nil {
		return ""
	}
	//增加header选项
	reqest.Header.Add("User-Agent", "Mozilla/5.0 (Linux; U; Android 4.0.3; zh-cn; U8860 Build/HuaweiU8860) UC AppleWebKit/530+ (KHTML, like Gecko) Mobile Safari/530 ")
	//处理返回结果
	response, err := client.Do(reqest)
	if err != nil {
		return ""
	}
	defer response.Body.Close()
	r, e := io.ReadAll(response.Body)
	if e != nil {
		return ""
	}
	return string(r)
}

func Int64ToInt32Ptr(value int64) *int32 {
	if value < math.MinInt32 || value > math.MaxInt32 {
		return nil
	}
	result := int32(value)
	return &result
}

// 类型转换
func TypeConversion(value string, ntype string) (reflect.Value, error) {
	if ntype == "string" {
		return reflect.ValueOf(value), nil
	} else if ntype == "time.Time" {
		t, err := time.ParseInLocation("2006-01-02 15:04:05", value, time.Local)
		return reflect.ValueOf(t), err
	} else if ntype == "Time" {
		t, err := time.ParseInLocation("2006-01-02 15:04:05", value, time.Local)
		return reflect.ValueOf(t), err
	} else if ntype == "int" {
		i, err := strconv.Atoi(value)
		return reflect.ValueOf(i), err
	} else if ntype == "int8" {
		i, err := strconv.ParseInt(value, 10, 64)
		return reflect.ValueOf(int8(i)), err
	} else if ntype == "int32" {
		i, err := strconv.ParseInt(value, 10, 64)
		return reflect.ValueOf(int64(i)), err
	} else if ntype == "int64" {
		i, err := strconv.ParseInt(value, 10, 64)
		return reflect.ValueOf(i), err
	} else if ntype == "float32" {
		i, err := strconv.ParseFloat(value, 64)
		return reflect.ValueOf(float32(i)), err
	} else if ntype == "float64" {
		i, err := strconv.ParseFloat(value, 64)
		return reflect.ValueOf(i), err
	}

	//else if .......增加其他一些类型的转换
	return reflect.ValueOf(value), errors.New("未知的类型：" + ntype)
}
func Md5(s string) string {
	srcCode := md5.Sum([]byte(s))
	code := fmt.Sprintf("%x", srcCode)
	return string(code)
}
func FilteredSQLInject(to_match_str string) bool {
	//过滤 ‘
	//ORACLE 注解 --  /**/
	//关键字过滤 update ,delete
	// 正则的字符串, 不能用 " " 因为" "里面的内容会转义
	str := `(?:')|(?:--)|(/\\*(?:.|[\\n\\r])*?\\*/)|(\b(select|update|and|or|delete|insert|trancate|char|chr|into|substr|ascii|declare|exec|count|master|into|drop|execute)\b)`
	re, err := regexp.Compile(str)
	if err != nil {
		//panic(err.Error())
		//log.Println("注入")
		return false
	}
	//log.Println("没注入")
	return re.MatchString(to_match_str)
}
func RandAllString(lenNum int) string {
	var CHARS = []string{"a", "b", "c", "d", "e", "f", "g", "h", "i", "j", "k", "l", "m", "n", "o", "p", "q", "r", "s", "t", "u", "v", "w", "x", "y", "z",
		"A", "B", "C", "D", "E", "F", "G", "H", "I", "J", "K", "L", "M", "N", "O", "P", "Q", "R", "S", "T", "U", "V", "W", "X", "Y", "Z",
		"1", "2", "3", "4", "5", "6", "7", "8", "9", "0"}
	str := strings.Builder{}
	length := len(CHARS)
	rand.Seed(time.Now().UnixNano())
	time.Sleep(time.Microsecond / 1000)
	for i := 0; i < lenNum; i++ {
		l := CHARS[rand.Intn(length)]
		str.WriteString(l)
	}
	return str.String()
}

func Decimal(num float64) float64 {
	num, _ = strconv.ParseFloat(fmt.Sprintf("%.2f", num), 64)
	return num
}

type OkexJson struct {
	Code int `json:"code"`
	Data []struct {
		BestOption bool   `json:"bestOption"`
		Payment    string `json:"payment"`
		Price      string `json:"price"`
	} `json:"data"`
	DetailMsg    string `json:"detailMsg"`
	ErrorCode    string `json:"error_code"`
	ErrorMessage string `json:"error_message"`
	Msg          string `json:"msg"`
}

func GetExchange() float64 {
	api := strings.ReplaceAll(OkExApi, "{time}", fmt.Sprint(time.Now().UnixNano()/1e6))
	jsonstr := Get(api)
	if jsonstr == "" {
		return usdtRate
	}
	var jsondata OkexJson
	err := json.Unmarshal([]byte(jsonstr), &jsondata)
	if err != nil {
		return usdtRate
	}
	if jsondata.Code != 0 {
		return usdtRate
	}
	r := StringToFloat64(jsondata.Data[0].Price)
	r = Decimal(r - r*Rxf)
	return r
	//return lib.StringToFloat64(jsondata.Data[0].Price)
}

func GetTrueAddr(id int64, money string, chain string) (string, string, int64, error) {
	//cfg, _ := dal.Cfg.Where(dal.Cfg.Func.Eq("ColAmount")).First()
	lockaddr, _ := dal.Lockaddress.Where(dal.Lockaddress.Islock.Eq(0)).Find()
	int64Slice := make([]int64, len(lockaddr))
	for i, l := range lockaddr {
		int64Slice[i] = int64(*l.Addrid)
	}
	mch, _ := dal.Merchant.Where(dal.Merchant.ID.Eq(id)).First()
	var r *entity.Address
	var e error
	//if chain == "Trc20" {
	//	r, e = dal.Address.Where(dal.Address.MerchantID.Eq(int32(id))).Where(dal.Address.ID.NotIn(int64Slice...)).Where(dal.Address.UsdtTrc.Lt(cast.ToFloat64(cfg.Value))).Where(dal.Address.DelFlg.Eq(0)).Order(dal.Address.ID).First()
	//} else {
	//	r, e = dal.Address.Where(dal.Address.MerchantID.Eq(int32(id))).Where(dal.Address.ID.NotIn(int64Slice...)).Where(dal.Address.UsdtErc.Lt(cast.ToFloat64(cfg.Value))).Where(dal.Address.DelFlg.Eq(0)).Order(dal.Address.ID).First()
	//}
	if chain == "Trc20" {
		r, e = dal.Address.Where(dal.Address.MerchantID.Eq(int32(id))).Where(dal.Address.ID.NotIn(int64Slice...)).Where(dal.Address.DelFlg.Eq(0)).Where(dal.Address.Num.Eq(0)).Where(dal.Address.Ercnum.Eq(0)).Order(dal.Address.ID).First()
	} else {
		r, e = dal.Address.Where(dal.Address.MerchantID.Eq(int32(id))).Where(dal.Address.ID.NotIn(int64Slice...)).Where(dal.Address.DelFlg.Eq(0)).Where(dal.Address.Num.Eq(0)).Where(dal.Address.Ercnum.Eq(0)).Order(dal.Address.ID).First()
	}
	if r == nil && e == nil {
		r, e = dal.Address.Where(dal.Address.MerchantID.Eq(int32(id))).Where(dal.Address.ID.NotIn(int64Slice...)).Where(dal.Address.DelFlg.Eq(0)).Where(dal.Address.Num.Eq(0)).Where(dal.Address.Ercnum.Eq(0)).Order(dal.Address.ID).First()
	}

	ling := StringToFloat64("0")
	if e != nil || r == nil {
		trc, erc, key, index := CreateAddr()
		yi := "1"
		dal.Address.Create(&entity.Address{
			ID:           index,
			Trxaddr:      &trc,
			Ethaddr:      &erc,
			Key:          &key,
			Trx:          &ling,
			Eth:          &ling,
			Bnb:          &ling,
			UsdtErc:      &ling,
			UsdtTrc:      &ling,
			UsdtBsc:      &ling,
			MerchantID:   Int64ToInt32Ptr(mch.ID),
			MerchantName: mch.Username,
			Num:          Int64ToInt32Ptr(0),
			Ercnum:       Int64ToInt32Ptr(0),
			Bscnum:       Int64ToInt32Ptr(0),
			Lock:         Int64ToInt32Ptr(0),
			DelFlg:       Int64ToInt32Ptr(0),
			TrxStatus:    Int64ToInt32Ptr(0),
			EthStatus:    1,
			BscStatus:    Int64ToInt32Ptr(1),
			UpdateMoney:  Int64ToInt32Ptr(0),
			Ethlock:      Int64ToInt32Ptr(0),
			Trxlock:      Int64ToInt32Ptr(0),
			Bnblock:      Int64ToInt32Ptr(0),
			Leaseid:      &yi,
			Trcsid:       0,
			Ercsid:       0,
			Bscsid:       0,
		})
		wsdatatrc := make(map[string]string)
		wsdatatrc["type"] = "addtrc"
		wsdatatrc["address"] = trc
		jsonDataTrc, _ := json.Marshal(wsdatatrc)
		SendMessage(string(jsonDataTrc))
		wsdataerc := make(map[string]string)
		wsdataerc["type"] = "adderc"
		wsdataerc["address"] = erc
		jsonDataErc, _ := json.Marshal(wsdataerc)
		SendMessage(string(jsonDataErc))
		return trc, erc, index, nil
	}
	c, _ := dal.Lockaddress.Where(dal.Lockaddress.Islock.Eq(0)).Where(dal.Lockaddress.Addrid.Eq(int32(r.ID))).Find()
	if len(c) == 0 {
		return *r.Trxaddr, *r.Ethaddr, r.ID, nil
	}
	return GetTrueAddr(id, money, chain)
}

func DoLockAddr(addrid int64, money string, locktime int64) bool {
	dal.Lockaddress.Create(&entity.Lockaddress{
		Addrid: Int64ToInt32Ptr(addrid),
		Money:  &money,
		Ctime:  Int64ToInt32Ptr(time.Now().Unix()),
		Etime:  Int64ToInt32Ptr(time.Now().Unix() + locktime),
		Islock: Int64ToInt32Ptr(0),
	})
	dal.Address.Where(dal.Address.ID.Eq(addrid)).Update(dal.Address.Lock, 1)
	return true
}

func CreateAddr() (string, string, string, int64) {
	lastAddr, _ := dal.Address.Last()
	var id int64
	if lastAddr == nil {
		id = 0
	} else {
		id = lastAddr.ID
	}
	index := id + 1
	trc, erc, key := GetAddr(uint64(index))
	return trc, erc, key, index
}

func Float64ToStringPtr(f float64) *string {
	s := strconv.FormatFloat(f, 'f', -1, 64)
	return &s
}

func TimeToTimeStamp(dateString string) int64 {
	layout := "2006-01-02 15:04:05"

	// 将日期字符串解析为 time.Time 类型
	parsedTime, err := time.Parse(layout, dateString)
	if err != nil {
		return 0
	}

	// 将 time.Time 转换为 Unix 时间戳（秒）
	unixTimestamp := parsedTime.Unix()
	return unixTimestamp
}

func base58Decode(input string) ([]byte, error) {
	result := big.NewInt(0)
	multiplier := big.NewInt(1)
	for i := len(input) - 1; i >= 0; i-- {
		index := -1
		for j, b := range base58Alphabet {
			if b == input[i] {
				index = j
				break
			}
		}
		if index == -1 {
			return nil, fmt.Errorf("解码出错，发现无效字符: %c", input[i])
		}
		temp := big.NewInt(int64(index))
		temp.Mul(temp, multiplier)
		result.Add(result, temp)
		multiplier.Mul(multiplier, big.NewInt(58))
	}

	decoded := result.Bytes()
	return decoded, nil
}
func ValidateAddress(address string) (bool, error) {
	decoded, err := base58Decode(address)
	if err != nil {
		return false, err
	}

	if len(decoded) != 25 || decoded[0] != 0x41 {
		return false, nil
	}

	hash := sha256.Sum256(decoded[:21])
	hash = sha256.Sum256(hash[:])

	for i := 0; i < 4; i++ {
		if decoded[21+i] != hash[i] {
			return false, nil
		}
	}

	return true, nil
}

func IsTRC20Address(str string) bool {
	address := str
	isValid, err := ValidateAddress(address)
	if err != nil {
		fmt.Println("错误:", err)
		return false
	}
	return isValid
}
func IsERC20Address(addr string) bool {
	// ERC-20 addresses are 42 characters long, with "0x" prefix
	if len(addr) != 42 || addr[:2] != "0x" {
		return false
	}

	// Check that the remaining 40 characters are valid hex digits
	match, _ := regexp.MatchString("^0x[0-9a-fA-F]{40}$", addr)
	return match
}

func Gopool(run func(), i int64) {
	go func(n int64) {
		for {
			time.Sleep(time.Second * time.Duration(n))
			run()
		}
	}(i)
}

type ResponseData struct {
	Data struct {
		Score float64 `json:"score"`
	} `json:"data"`
}

func getAddressScore(address string) (float64, error) {
	t := time.Now().UnixNano() / int64(time.Millisecond)
	key := getApiKey(t)
	client := &http.Client{}
	req, err := http.NewRequest("GET", "https://www.oklink.com/api/tracker/c/v1/r1/healthy/scoreV3?chain=TRX&address="+address, nil)
	if err != nil {
		fmt.Println("Score Error")
		fmt.Println(err)
		return 0, err
	}
	req.Header.Add("x-apikey", key)
	//req.Header.Add("x-locale", "zh_CN")
	req.Header.Add("accept-language", "zh-CN,zh;q=0.9")
	resp, err := client.Do(req)
	if err != nil {
		fmt.Println("Score Error")
		fmt.Println(err)
		return 0, err
	}

	defer resp.Body.Close()
	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		fmt.Println("Score Error")
		fmt.Println(err)
		return 0, err
	}

	var responseData ResponseData
	err = json.Unmarshal(body, &responseData)
	if err != nil {
		fmt.Println("Score Error")
		fmt.Println(err)
		return 0, err
	}
	return responseData.Data.Score, nil
}

func getApiKey(timestamp int64) string {
	e := encryptApiKey()
	return comb(e, encryptTime(timestamp))
}

func encryptApiKey() string {
	t := "a2c903cc-b31e-4547-9299-b6d07b7631ab"
	e := []rune(t)
	r := e[:8]
	return string(e[8:]) + string(r)
}

func encryptTime(t int64) string {
	e := []rune(strconv.FormatInt(1*t+1111111111111, 10))
	r := strconv.Itoa(rand.Intn(10))
	n := strconv.Itoa(rand.Intn(10))
	o := strconv.Itoa(rand.Intn(10))
	return string(e) + r + n + o
}

func comb(t string, e string) string {
	r := t + "|" + e
	return base64.StdEncoding.EncodeToString([]byte(r))
}
