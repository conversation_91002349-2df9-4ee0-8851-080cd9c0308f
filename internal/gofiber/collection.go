package gofiber

import (
	"Mapi/config"
	"Mapi/dal"
	"Mapi/internal/lib"
	"Mapi/model/entity"
	"github.com/gofiber/fiber/v2"
	"github.com/shopspring/decimal"
	"github.com/spf13/cast"
	"log"
	"math/rand"
	"net/url"
	"strconv"
	"time"
)

func collectionOrder(c *fiber.Ctx) error {
	memberid := c.Query("memberid")
	money := c.Query("money")
	rorderid := c.Query("rorderid")
	callbackurl, _ := url.QueryUnescape(c.Query("callbackurl"))
	returnurl, _ := url.QueryUnescape(c.Query("returnurl"))
	currency := c.Query("currency")
	chain := c.Query("chain")
	sign := c.Query("sign")
	if sign == "" {
		sign = c.Query("sgin")
	}
	userid := c.Query("userid")
	//if memberid == "" || money == "" || rorderid == "" || sign == "" {
	//q	return c.Status(fiber.StatusOK).JSON(fiber.Map{8b
	//		"code": false,
	//		"msg":  "参数错误",
	//	})
	//}
	ip := c.IP()
	iplist, _ := dal.Whitelist.Where(dal.Whitelist.Content.Eq(ip)).Where(dal.Whitelist.Type.Eq(3)).First()
	if iplist == nil {
		return nil
	}
	if memberid == "" {
		return c.Status(fiber.StatusOK).JSON(fiber.Map{
			"code": false,
			"msg":  "参数错误,memberid",
		})
	}
	if money == "" {
		return c.Status(fiber.StatusOK).JSON(fiber.Map{
			"code": false,
			"msg":  "参数错误,money",
		})
	}
	if rorderid == "" {
		return c.Status(fiber.StatusOK).JSON(fiber.Map{
			"code": false,
			"msg":  "参数错误,rorderid",
		})
	}
	if sign == "" {
		return c.Status(fiber.StatusOK).JSON(fiber.Map{
			"code": false,
			"msg":  "参数错误,sign",
		})
	}
	mch, _ := dal.Merchant.Where(dal.Merchant.ID.Eq(cast.ToInt64(memberid))).First()
	if mch == nil {
		return c.Status(fiber.StatusOK).JSON(fiber.Map{
			"code": false,
			"msg":  "商户不存在",
		})
	}
	if *mch.Status == 1 {
		return c.Status(fiber.StatusOK).JSON(fiber.Map{
			"code": false,
			"msg":  "商户账号已冻结",
		})
	}
	if *mch.FeeMoney < -*mch.Credits || *mch.FeeMoney == -*mch.Credits {
		return c.Status(fiber.StatusOK).JSON(fiber.Map{
			"code": false,
			"msg":  "商户手续费余额不足",
		})
	}
	appkey := mch.Appkey
	mchname := mch.Username
	intm, _ := strconv.ParseFloat(money, 64)
	if intm < 10 && mch.ID != 1 {
		return c.Status(fiber.StatusOK).JSON(fiber.Map{
			"code": false,
			"msg":  "最小金额为10U",
		})
	}
	signs := lib.Md5(memberid + money + rorderid + *appkey)
	if signs != sign {
		log.Printf("signs:%s,sign:%s", signs, sign)
		return c.Status(fiber.StatusOK).JSON(fiber.Map{
			"code": false,
			"msg":  "验签失败",
		})
	}
	if mch.Ratetype == nil {
		return c.Status(fiber.StatusOK).JSON(fiber.Map{
			"code": false,
			"msg":  "请联系客服设置汇率",
		})
	}
	check, _ := dal.Order.Where(dal.Order.Rorderid.Eq(rorderid)).First()
	if check != nil {
		return c.Status(fiber.StatusOK).JSON(fiber.Map{
			"code": false,
			"msg":  "订单已存在",
		})
	}
	var addr *entity.Address
	if userid != "" {
		addr, _ = dal.Address.Where(dal.Address.Where(dal.Address.MerchantID.Eq(cast.ToInt32(memberid)))).Where(dal.Address.Userid.Eq(userid)).First()
		if addr == nil {
			return c.Status(fiber.StatusOK).JSON(fiber.Map{
				"code": false,
				"msg":  "此用户未绑定地址",
			})
		}
	}
	tm := time.Now()
	rand.Seed(time.Now().UnixNano())
	randomNumber := rand.Intn(9000) + 1000
	var address string
	var ordertype string
	var orderid string
	if chain == "Erc" || chain == "Eth" {
		ordertype = "Erc20"
		orderid = "CE" + tm.Format("060102150405") + cast.ToString(randomNumber)
	} else if chain == "Bsc" || chain == "Bep" || chain == "Bnb" {
		return c.Status(fiber.StatusOK).JSON(fiber.Map{
			"code": false,
			"msg":  "BSC通道已关闭,请联系客服开启",
		})
		ordertype = "Bep20"
		orderid = "CB" + tm.Format("060102150405") + cast.ToString(randomNumber)
	} else {
		ordertype = "Trc20"
		orderid = "CT" + tm.Format("060102150405") + cast.ToString(randomNumber)
	}

	nmoney, _ := strconv.ParseFloat(money, 64)
	dqrate := lib.GetExchange()
	rmb := "0"
	rate := 0.00

	if *mch.Ratetype == "1" { //固定汇率
		rate, _ = decimal.NewFromFloat(float64(*mch.Rate)).Truncate(2).Float64()
	} else { //浮动汇率
		if *mch.Floatratetype == "1" { //百分比
			mchrv, _ := strconv.ParseFloat(*mch.Floatratedvalue, 64)
			der := decimal.NewFromFloat(dqrate)
			dej, _ := decimal.NewFromFloat(dqrate).Mul(decimal.NewFromFloat(mchrv)).Div(decimal.NewFromFloat(100)).RoundCeil(2).Float64()
			rate, _ = der.Sub(decimal.NewFromFloat(dej)).RoundCeil(2).Float64()
		} else { //固定值
			mchrv, _ := strconv.ParseFloat(*mch.Floatratedvalue, 64)
			der := decimal.NewFromFloat(dqrate)
			dej := decimal.NewFromFloat(mchrv)
			rate, _ = der.Sub(dej).RoundCeil(2).Float64()
		}
	}
	if currency == "CNY" {
		a := decimal.NewFromFloat(nmoney)
		b := decimal.NewFromFloat(rate)
		c := a.Div(b)
		nmoney, _ = c.RoundCeil(2).Float64()
		rmb = money
	} else {
		a := decimal.NewFromFloat(nmoney)
		nmoney, _ = a.RoundCeil(2).Float64()
	}
	if nmoney < 10 && mch.ID != 1 {
		return c.Status(fiber.StatusOK).JSON(fiber.Map{
			"code": false,
			"msg":  "最小金额为10U",
		})
	}
	var trc string
	var erc string
	var addrid int64
	if userid != "" {
		trc = *addr.Trxaddr
		erc = *addr.Ethaddr
		addrid = addr.ID
	} else {
		trc, erc, addrid, _ = lib.GetTrueAddr(mch.ID, cast.ToString(nmoney), ordertype)
		if trc == "" || erc == "" {
			return c.Status(fiber.StatusOK).JSON(fiber.Map{
				"code": false,
				"msg":  "创建订单失败",
			})
		}
	}

	if chain == "Erc" || chain == "Eth" || chain == "Bsc" || chain == "Bep" || chain == "Bnb" {
		address = erc
	} else {
		address = trc
	}
	dal.Order.Create(&entity.Order{
		MerchantID:   &memberid,
		Appkey:       mch.Appkey,
		Orderid:      &orderid,
		Rorderid:     &rorderid,
		Trxadd:       &address,
		Addrid:       lib.Int64ToInt32Ptr(addrid),
		Money:        lib.Float64ToStringPtr(nmoney),
		Status:       lib.Int64ToInt32Ptr(0),
		Callbackurl:  &callbackurl,
		Callbacknum:  lib.Int64ToInt32Ptr(0),
		MerchantName: mchname,
		Ctime:        lib.Int64ToInt32Ptr(time.Now().Unix()),
		Returnurl:    &returnurl,
		Rmb:          &rmb,
		Rate:         lib.Float64ToStringPtr(rate),
		Chain:        &ordertype,
	})

	if nmoney > lib.StringToFloat64(*mch.Largeorderamount) {
		lib.DoLockAddr(addrid, cast.ToString(nmoney), lib.StrToint64(*mch.Largeorderlocktime)*60)
	} else {
		lib.DoLockAddr(addrid, cast.ToString(nmoney), lib.StrToint64(*mch.Smallorderlocktime)*60)
	}
	cashierurl := config.NewConfig().Cashier.Cashierurl
	data := make(map[string]string)
	data["trxaddr"] = address
	data["orderid"] = orderid
	data["rorderid"] = rorderid
	data["money"] = cast.ToString(nmoney)
	data["cashierurl"] = cashierurl + "?orderid=" + orderid + "&chain=" + ordertype

	return c.Status(fiber.StatusOK).JSON(fiber.Map{
		"code": true,
		"msg":  "创建订单成功",
		"data": data,
	})
}

func collectionQuery(c *fiber.Ctx) error {
	memberid := c.Query("memberid")
	orderid := c.Query("orderid")
	sign := c.Query("sign")
	if sign == "" {
		sign = c.Query("sgin")
	}
	if (memberid == "" && orderid == "") || sign == "" {
		return c.Status(fiber.StatusOK).JSON(fiber.Map{
			"code": false,
			"msg":  "参数错误",
		})
	}
	ip := c.IP()
	iplist, _ := dal.Whitelist.Where(dal.Whitelist.Content.Eq(ip)).Where(dal.Whitelist.Type.Eq(3)).First()
	if iplist == nil {
		return nil
	}
	mch, _ := dal.Merchant.Where(dal.Merchant.ID.Eq(cast.ToInt64(memberid))).First()
	if mch == nil {
		return c.Status(fiber.StatusOK).JSON(fiber.Map{
			"code": false,
			"msg":  "商户不存在",
		})
	}
	if *mch.Status == 1 {
		return c.Status(fiber.StatusOK).JSON(fiber.Map{
			"code": false,
			"msg":  "商户账号已冻结",
		})
	}
	appkey := mch.Appkey
	signs := lib.Md5(memberid + orderid + *appkey)
	if signs != sign {
		return c.Status(fiber.StatusOK).JSON(fiber.Map{
			"code": false,
			"msg":  "验签失败",
		})
	}
	order, _ := dal.Order.Where(dal.Order.Orderid.Eq(orderid)).Where(dal.Order.MerchantID.Eq(memberid)).First()
	if order == nil {
		return c.Status(fiber.StatusOK).JSON(fiber.Map{
			"code": false,
			"msg":  "订单不存在",
		})
	}

	s := map[int64]string{
		0: "创建订单成功",
		1: "订单超时",
		2: "支付成功",
		3: "回调成功",
		4: "主动成功",
	}
	status := order.Status
	st := cast.ToInt64(status)
	data := make(map[string]interface{})
	data["trxaddr"] = order.Trxadd
	data["orderid"] = orderid
	data["rorderid"] = order.Rorderid
	data["ctime"] = order.Ctime
	data["etime"] = order.Etime
	data["status"] = s[st]

	return c.Status(fiber.StatusOK).JSON(fiber.Map{
		"code": true,
		"msg":  "查询成功",
		"data": data,
	})
}

func collectionCancel(c *fiber.Ctx) error {
	memberid := c.Query("memberid")
	rorderid := c.Query("rorderid")
	sign := c.Query("sign")
	if sign == "" {
		sign = c.Query("sgin")
	}
	ip := c.IP()
	iplist, _ := dal.Whitelist.Where(dal.Whitelist.Content.Eq(ip)).Where(dal.Whitelist.Type.Eq(3)).First()
	if iplist == nil {
		return nil
	}
	if (memberid == "" && rorderid == "") || sign == "" {
		return c.Status(fiber.StatusOK).JSON(fiber.Map{
			"code": false,
			"msg":  "参数错误",
		})
	}
	mch, _ := dal.Merchant.Where(dal.Merchant.ID.Eq(cast.ToInt64(memberid))).First()
	if mch == nil {
		return c.Status(fiber.StatusOK).JSON(fiber.Map{
			"code": false,
			"msg":  "商户不存在",
		})
	}
	if *mch.Status == 1 {
		return c.Status(fiber.StatusOK).JSON(fiber.Map{
			"code": false,
			"msg":  "商户账号已冻结",
		})
	}
	appkey := mch.Appkey
	signs := lib.Md5(memberid + rorderid + *appkey)
	if signs != sign {
		return c.Status(fiber.StatusOK).JSON(fiber.Map{
			"code": false,
			"msg":  "验签失败",
		})
	}
	order, _ := dal.Order.Where(dal.Order.Rorderid.Eq(rorderid)).Where(dal.Order.MerchantID.Eq(memberid)).First()
	if order == nil {
		return c.Status(fiber.StatusOK).JSON(fiber.Map{
			"code": false,
			"msg":  "订单不存在",
		})
	}
	dal.Order.Where(dal.Order.ID.Eq(order.ID)).Updates(&entity.Order{
		Status: lib.Int64ToInt32Ptr(5),
		Etime:  lib.Int64ToInt32Ptr(time.Now().Unix()),
	})
	dal.Lockaddress.Where(dal.Lockaddress.Addrid.Eq(*order.Addrid)).Where(dal.Lockaddress.Islock.Eq(0)).Where(dal.Lockaddress.Money.Eq(*order.Money)).Update(dal.Lockaddress.Islock, 0)
	return c.Status(fiber.StatusOK).JSON(fiber.Map{
		"code": true,
		"msg":  "取消成功",
		"data": nil,
	})
}
