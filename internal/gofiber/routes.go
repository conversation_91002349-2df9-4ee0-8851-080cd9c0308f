package gofiber

import (
	"github.com/gofiber/fiber/v2"
)

func SetupRoutes(app *fiber.App) {
	//查询汇率
	app.Get("/Api/ExchangeRate", getExchangeRate)
	//绑定用户地址
	app.Get("/Api/BindAddress", bindAddress)
	//更新回调地址
	app.Get("/Api/UpdateCallbackurl", updateCallbackurl)
	//更新用户地址
	app.Get("/Api/UpdateAddress", updateAddress)
	//收款订单下单
	app.Get("/Api/Order", collectionOrder)
	//收款订单查询
	app.Get("/Api/Query", collectionQuery)
	//收款订单取消
	app.Get("/Api/Cancel", collectionCancel)
	//下发订单下单
	app.Get("/Api/PayOrder", payOrder)
	//下发订单查询
	app.Get("/Api/PayQuery", payQuery)
	//下发订单取消
	app.Get("/Api/PayCancel", payCancel)
}
