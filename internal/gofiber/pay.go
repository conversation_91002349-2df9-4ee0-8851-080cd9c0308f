package gofiber

import (
	"Mapi/dal"
	"Mapi/internal/lib"
	"Mapi/internal/queue"
	"Mapi/model/entity"
	"fmt"
	"log"
	"math/rand"
	"net/url"
	"strconv"
	"sync"
	"time"

	"github.com/gofiber/fiber/v2"
	"github.com/shopspring/decimal"
	"github.com/spf13/cast"
)

var mutex sync.Mutex

func payOrder(c *fiber.Ctx) error {
	mutex.Lock()
	defer mutex.Unlock()
	memberid := c.Query("memberid")
	money := c.Query("money")
	rorderid := c.Query("rorderid")
	callbackurl, _ := url.QueryUnescape(c.Query("callbackurl"))
	address := c.Query("address")
	currency := c.Query("currency")
	chain := c.Query("chain")
	sign := c.Query("sign")
	coin := c.Query("coin")
	ip := c.IP()
	iplist, _ := dal.Whitelist.Where(dal.Whitelist.Content.Eq(ip)).Where(dal.Whitelist.Type.Eq(3)).First()
	log.Printf("白名单数据为：%+v", iplist)
	if iplist == nil {
		return nil
	}
	if chain == "Bsc" {
		return c.Status(fiber.StatusOK).JSON(fiber.Map{
			"code": false,
			"msg":  "BSC通道已关闭,请联系客服开启",
		})
	}
	if memberid == "" || money == "" || rorderid == "" || sign == "" || address == "" {
		return c.Status(fiber.StatusOK).JSON(fiber.Map{
			"code": false,
			"msg":  "参数错误",
		})
	}
	mch, _ := dal.Merchant.Where(dal.Merchant.ID.Eq(cast.ToInt64(memberid))).First()
	if mch == nil {
		return c.Status(fiber.StatusOK).JSON(fiber.Map{
			"code": false,
			"msg":  "商户不存在",
		})
	}
	if mch.Status == nil || *mch.Status == 1 {
		return c.Status(fiber.StatusOK).JSON(fiber.Map{
			"code": false,
			"msg":  "商户账号已冻结",
		})
	}
	if mch.Appkey == nil {
		return c.Status(fiber.StatusOK).JSON(fiber.Map{
			"code": false,
			"msg":  "商户配置错误",
		})
	}
	if mch.Username == nil {
		return c.Status(fiber.StatusOK).JSON(fiber.Map{
			"code": false,
			"msg":  "商户配置错误",
		})
	}
	if mch.IsLimitAmount == nil {
		return c.Status(fiber.StatusOK).JSON(fiber.Map{
			"code": false,
			"msg":  "商户配置错误",
		})
	}
	if lib.IsTRC20Address(address) == false && lib.IsERC20Address(address) == false {
		return c.Status(fiber.StatusOK).JSON(fiber.Map{
			"code": false,
			"msg":  "请提交正确的地址信息",
		})
	}
	if lib.IsERC20Address(address) == false {
		chain = "Trc"
	} else {
		chain = "Erc"
	}
	if chain != "Trc" && chain != "Erc" && chain != "Bsc" {
		return c.Status(fiber.StatusOK).JSON(fiber.Map{
			"code": false,
			"msg":  "请提交正确的链信息,如Trc,Erc,Bsc",
		})
	}
	fmt.Println(rorderid)
	fmt.Println(chain)
	//if *mch.FeeMoney < -*mch.Credits || *mch.FeeMoney == -*mch.Credits {
	//	return c.Status(fiber.StatusOK).JSON(fiber.Map{
	//		"code": false,
	//		"msg":  "商户手续费余额不足",
	//	})
	//}
	//if chain == "Trc" {
	//	if *mch.FeeMoney < 28 {
	//		return c.Status(fiber.StatusOK).JSON(fiber.Map{
	//			"code": false,
	//			"msg":  "商户手续费余额不足",
	//		})
	//	}
	//} else if chain == "Erc" {
	//	if *mch.EthFeemoney < 0.002 {
	//		return c.Status(fiber.StatusOK).JSON(fiber.Map{
	//			"code": false,
	//			"msg":  "商户手续费余额不足",
	//		})
	//	}
	//} else if chain == "Bsc" {
	//	if *mch.BscFeemoney < 0.01 {
	//		return c.Status(fiber.StatusOK).JSON(fiber.Map{
	//			"code": false,
	//			"msg":  "商户手续费余额不足",
	//		})
	//	}
	//}
	appkey := mch.Appkey
	mchname := mch.Username
	intm, _ := strconv.ParseFloat(money, 64)
	if *mch.IsLimitAmount == 1 {
		if intm < 10 {
			return c.Status(fiber.StatusOK).JSON(fiber.Map{
				"code": false,
				"msg":  "最小金额为10U",
			})
		}
	}
	trcfeecfg, _ := dal.Cfg.Where(dal.Cfg.Func.Eq("TRCFEE")).First()
	ercfeecfg, _ := dal.Cfg.Where(dal.Cfg.Func.Eq("ERCFEE")).First()
	bscfeecfg, _ := dal.Cfg.Where(dal.Cfg.Func.Eq("BSCFEE")).First()

	if trcfeecfg == nil || ercfeecfg == nil || bscfeecfg == nil {
		return c.Status(fiber.StatusOK).JSON(fiber.Map{
			"code": false,
			"msg":  "系统配置错误",
		})
	}

	trcfee := cast.ToFloat64(trcfeecfg.Value)
	ercfee := cast.ToFloat64(ercfeecfg.Value)
	bscfee := cast.ToFloat64(bscfeecfg.Value)
	if chain == "Erc" {
		checkmoney, _ := decimal.NewFromFloat(intm).Add(decimal.NewFromFloat(ercfee)).Float64()
		if *mch.ErcMoney < checkmoney {
			return c.Status(fiber.StatusOK).JSON(fiber.Map{
				"code": false,
				"msg":  "商户余额不足",
			})
		}
	} else if chain == "Trc" {
		checkmoney, _ := decimal.NewFromFloat(intm).Add(decimal.NewFromFloat(trcfee)).Float64()
		if *mch.Money < checkmoney {
			return c.Status(fiber.StatusOK).JSON(fiber.Map{
				"code": false,
				"msg":  "商户余额不足",
			})
		}
	} else if chain == "Bsc" {
		checkmoney, _ := decimal.NewFromFloat(intm).Add(decimal.NewFromFloat(bscfee)).Float64()
		if *mch.BscMoney < checkmoney {
			return c.Status(fiber.StatusOK).JSON(fiber.Map{
				"code": false,
				"msg":  "商户余额不足",
			})
		}
	}
	nmoney, _ := strconv.ParseFloat(money, 64)
	dqrate := lib.GetExchange()
	rmb := "0"
	rate := 0.00
	if *mch.Ratetype == "1" { //固定汇率
		rate, _ = decimal.NewFromFloat(float64(*mch.Rate)).Truncate(2).Float64()
	} else { //浮动汇率
		if *mch.Floatratetype == "1" { //百分比
			mchrv, _ := strconv.ParseFloat(*mch.Floatratedvalue, 64)
			der := decimal.NewFromFloat(dqrate)
			dej, _ := decimal.NewFromFloat(dqrate).Mul(decimal.NewFromFloat(mchrv)).Div(decimal.NewFromFloat(100)).RoundCeil(2).Float64()
			rate, _ = der.Sub(decimal.NewFromFloat(dej)).RoundCeil(2).Float64()
		} else { //固定值
			mchrv, _ := strconv.ParseFloat(*mch.Floatratedvalue, 64)
			der := decimal.NewFromFloat(dqrate)
			dej := decimal.NewFromFloat(mchrv)
			rate, _ = der.Sub(dej).RoundCeil(2).Float64()
		}
	}
	if currency == "CNY" {
		a := decimal.NewFromFloat(nmoney)
		b := decimal.NewFromFloat(rate)
		c := a.Div(b)
		nmoney, _ = c.RoundCeil(2).Float64()
		rmb = money
	} else {
		a := decimal.NewFromFloat(nmoney)
		nmoney, _ = a.RoundCeil(2).Float64()
	}
	if chain == "Erc" {
		checkmoney, _ := decimal.NewFromFloat(intm).Add(decimal.NewFromFloat(ercfee)).Float64()
		if *mch.ErcMoney < checkmoney {
			return c.Status(fiber.StatusOK).JSON(fiber.Map{
				"code": false,
				"msg":  "商户余额不足",
			})
		}
	} else if chain == "Trc" {
		checkmoney, _ := decimal.NewFromFloat(intm).Add(decimal.NewFromFloat(trcfee)).Float64()
		if *mch.Money < checkmoney {
			return c.Status(fiber.StatusOK).JSON(fiber.Map{
				"code": false,
				"msg":  "商户余额不足",
			})
		}
	} else if chain == "Bsc" {
		checkmoney, _ := decimal.NewFromFloat(intm).Add(decimal.NewFromFloat(bscfee)).Float64()
		if *mch.BscMoney < checkmoney {
			return c.Status(fiber.StatusOK).JSON(fiber.Map{
				"code": false,
				"msg":  "商户余额不足",
			})
		}
	}
	if *mch.IsLimitAmount == 1 {
		if nmoney < 10 {
			return c.Status(fiber.StatusOK).JSON(fiber.Map{
				"code": false,
				"msg":  "最小金额为10U",
			})
		}
	}
	signs := lib.Md5(address + memberid + money + rorderid + *appkey)
	if signs != sign {
		log.Printf("signs:%s,sign:%s", signs, sign)
		return c.Status(fiber.StatusOK).JSON(fiber.Map{
			"code": false,
			"msg":  "验签失败",
		})
	}
	if mch.Ratetype == nil {
		return c.Status(fiber.StatusOK).JSON(fiber.Map{
			"code": false,
			"msg":  "请联系客服设置汇率",
		})
	}
	check, _ := dal.Payorder.Where(dal.Payorder.Rorderid.Eq(rorderid)).First()
	if check != nil {
		return c.Status(fiber.StatusOK).JSON(fiber.Map{
			"code": false,
			"msg":  "订单已存在",
		})
	}
	tm := time.Now()
	rand.Seed(time.Now().UnixNano())
	randomNumber := rand.Intn(9000) + 1000
	var ordertype string
	var orderid string
	if chain == "Erc" {
		ordertype = "Erc20"
		orderid = "PE" + tm.Format("060102150405") + cast.ToString(randomNumber)
	} else if chain == "Trc" {
		ordertype = "Trc20"
		orderid = "PT" + tm.Format("060102150405") + cast.ToString(randomNumber)
	} else if chain == "Bsc" {
		ordertype = "Bep20"
		orderid = "PB" + tm.Format("060102150405") + cast.ToString(randomNumber)
	}
	var co string
	if coin == "USDT" || coin == "" {
		co = "USDT"
	} else {
		co = "TRX"
	}
	dal.Payorder.Create(&entity.Payorder{
		MerchantID:   &memberid,
		Appkey:       mch.Appkey,
		Orderid:      &orderid,
		Rorderid:     &rorderid,
		Addr:         &address,
		Money:        lib.Float64ToStringPtr(nmoney),
		Coin:         &co,
		Status:       lib.Int64ToInt32Ptr(0),
		Callbackurl:  &callbackurl,
		Callbacknum:  lib.Int64ToInt32Ptr(0),
		MerchantName: mchname,
		Ctime:        lib.Int64ToInt32Ptr(time.Now().Unix()),
		Rmb:          &rmb,
		Rate:         lib.Float64ToStringPtr(rate),
		Chain:        &ordertype,
		Lease:        lib.Int64ToInt32Ptr(0),
	})
	if chain == "Erc" {
		//更新商户余额
		nmoney, _ = decimal.NewFromFloat(nmoney).Add(decimal.NewFromFloat(ercfee)).Float64()
		newmoney, _ := decimal.NewFromFloat(*mch.ErcMoney).Sub(decimal.NewFromFloat(nmoney)).Float64()
		newlockmoney, _ := decimal.NewFromFloat(*mch.ErcLockmoney).Add(decimal.NewFromFloat(nmoney)).Float64()
		dal.Merchant.Where(dal.Merchant.ID.Eq(mch.ID)).Updates(&entity.Merchant{
			ErcMoney:     &newmoney,
			ErcLockmoney: &newlockmoney,
		})
		dal.Balance.Create(&entity.Balance{
			MerchantID:   lib.Int64ToInt32Ptr(mch.ID),
			MerchantName: mch.Username,
			Trc:          mch.Money,
			Erc:          &newmoney,
			Bsc:          mch.BscMoney,
			Ctime:        lib.Int64ToInt32Ptr(time.Now().Unix()),
		})
	} else if chain == "Trc" {
		//更新商户余额
		nmoney, _ = decimal.NewFromFloat(nmoney).Add(decimal.NewFromFloat(trcfee)).Float64()
		newmoney, _ := decimal.NewFromFloat(*mch.Money).Sub(decimal.NewFromFloat(nmoney)).Float64()
		newlockmoney, _ := decimal.NewFromFloat(*mch.LockMoney).Add(decimal.NewFromFloat(nmoney)).Float64()
		dal.Merchant.Where(dal.Merchant.ID.Eq(mch.ID)).Updates(&entity.Merchant{
			Money:     &newmoney,
			LockMoney: &newlockmoney,
		})
		dal.Balance.Create(&entity.Balance{
			MerchantID:   lib.Int64ToInt32Ptr(mch.ID),
			MerchantName: mch.Username,
			Trc:          &newmoney,
			Erc:          mch.ErcMoney,
			Bsc:          mch.BscMoney,
			Ctime:        lib.Int64ToInt32Ptr(time.Now().Unix()),
		})
	} else if chain == "Bsc" {
		//更新商户余额
		nmoney, _ = decimal.NewFromFloat(nmoney).Add(decimal.NewFromFloat(bscfee)).Float64()
		newmoney, _ := decimal.NewFromFloat(*mch.BscMoney).Sub(decimal.NewFromFloat(nmoney)).Float64()
		newlockmoney, _ := decimal.NewFromFloat(*mch.BscLockmoney).Add(decimal.NewFromFloat(nmoney)).Float64()
		dal.Merchant.Where(dal.Merchant.ID.Eq(mch.ID)).Updates(&entity.Merchant{
			BscMoney:     &newmoney,
			BscLockmoney: &newlockmoney,
		})
		dal.Balance.Create(&entity.Balance{
			MerchantID:   lib.Int64ToInt32Ptr(mch.ID),
			MerchantName: mch.Username,
			Trc:          mch.Money,
			Erc:          mch.ErcMoney,
			Bsc:          &newmoney,
			Ctime:        lib.Int64ToInt32Ptr(time.Now().Unix()),
		})
	}
	//订单插入转账队列
	queue.AddTaskToQueue(orderid)

	data := make(map[string]string)
	data["address"] = address
	data["orderid"] = orderid
	data["rorderid"] = rorderid
	data["money"] = cast.ToString(nmoney)

	return c.Status(fiber.StatusOK).JSON(fiber.Map{
		"code": true,
		"msg":  "创建订单成功",
		"data": data,
	})
}

func payQuery(c *fiber.Ctx) error {
	memberid := c.Query("memberid")
	orderid := c.Query("orderid")
	sign := c.Query("sign")
	ip := c.IP()
	iplist, _ := dal.Whitelist.Where(dal.Whitelist.Content.Eq(ip)).Where(dal.Whitelist.Type.Eq(3)).First()
	if iplist == nil {
		return nil
	}
	if (memberid == "" && orderid == "") || sign == "" {
		return c.Status(fiber.StatusOK).JSON(fiber.Map{
			"code": false,
			"msg":  "参数错误",
		})
	}
	mch, _ := dal.Merchant.Where(dal.Merchant.ID.Eq(cast.ToInt64(memberid))).First()
	if mch == nil {
		return c.Status(fiber.StatusOK).JSON(fiber.Map{
			"code": false,
			"msg":  "商户不存在",
		})
	}
	if *mch.Status == 1 {
		return c.Status(fiber.StatusOK).JSON(fiber.Map{
			"code": false,
			"msg":  "商户账号已冻结",
		})
	}
	appkey := mch.Appkey
	signs := lib.Md5(memberid + orderid + *appkey)
	if signs != sign {
		return c.Status(fiber.StatusOK).JSON(fiber.Map{
			"code": false,
			"msg":  "验签失败",
		})
	}
	order, _ := dal.Payorder.Where(dal.Payorder.Orderid.Eq(orderid)).Where(dal.Payorder.MerchantID.Eq(memberid)).First()
	if order == nil {
		return c.Status(fiber.StatusOK).JSON(fiber.Map{
			"code": false,
			"msg":  "订单不存在",
		})
	}

	s := map[int64]string{
		0: "创建订单成功",
		1: "订单超时",
		2: "转账成功",
		3: "回调成功",
		4: "主动成功",
		5: "转账失败",
	}
	status := order.Status
	st := cast.ToInt64(status)
	data := make(map[string]interface{})
	data["address"] = order.Addr
	data["orderid"] = orderid
	data["rorderid"] = order.Rorderid
	data["ctime"] = order.Ctime
	data["etime"] = order.Etime
	data["status"] = s[st]
	if order.Transactionid != nil {
		data["txid"] = order.Transactionid
	} else {
		data["txid"] = nil
	}

	return c.Status(fiber.StatusOK).JSON(fiber.Map{
		"code": true,
		"msg":  "查询成功",
		"data": data,
	})
}

func payCancel(c *fiber.Ctx) error {
	memberid := c.Query("memberid")
	rorderid := c.Query("rorderid")
	sign := c.Query("sign")
	ip := c.IP()
	iplist, _ := dal.Whitelist.Where(dal.Whitelist.Content.Eq(ip)).Where(dal.Whitelist.Type.Eq(3)).First()
	if iplist == nil {
		return nil
	}
	if (memberid == "" && rorderid == "") || sign == "" {
		return c.Status(fiber.StatusOK).JSON(fiber.Map{
			"code": false,
			"msg":  "参数错误",
		})
	}
	mch, _ := dal.Merchant.Where(dal.Merchant.ID.Eq(cast.ToInt64(memberid))).First()
	if mch == nil {
		return c.Status(fiber.StatusOK).JSON(fiber.Map{
			"code": false,
			"msg":  "商户不存在",
		})
	}
	if *mch.Status == 1 {
		return c.Status(fiber.StatusOK).JSON(fiber.Map{
			"code": false,
			"msg":  "商户账号已冻结",
		})
	}
	appkey := mch.Appkey
	signs := lib.Md5(memberid + rorderid + *appkey)
	if signs != sign {
		return c.Status(fiber.StatusOK).JSON(fiber.Map{
			"code": false,
			"msg":  "验签失败",
		})
	}
	order, _ := dal.Payorder.Where(dal.Payorder.Rorderid.Eq(rorderid)).Where(dal.Payorder.MerchantID.Eq(memberid)).First()
	if order == nil {
		return c.Status(fiber.StatusOK).JSON(fiber.Map{
			"code": false,
			"msg":  "订单不存在",
		})
	}
	dal.Payorder.Where(dal.Payorder.ID.Eq(order.ID)).Updates(&entity.Payorder{
		Status: lib.Int64ToInt32Ptr(5),
		Etime:  lib.Int64ToInt32Ptr(time.Now().Unix()),
	})
	return c.Status(fiber.StatusOK).JSON(fiber.Map{
		"code": true,
		"msg":  "取消成功",
		"data": nil,
	})
}
