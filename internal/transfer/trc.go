package transfer

import (
	"Mapi/config"
	"Mapi/dal"
	"Mapi/internal/lib"
	"Mapi/model/entity"
	"Mapi/tron"
	"encoding/json"
	"fmt"
	"io/ioutil"
	"log"
	"math/big"
	"net/http"
	"os"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/ethereum/go-ethereum/ethclient"
	"github.com/shopspring/decimal"
	"github.com/spf13/cast"
)

var (
	SUN         float64       = 1000000
	TronMainNet               = "https://api.trongrid.io"
	Trc20Api    *tron.TronApi = tron.NewTronApi(
		"https://api.trongrid.io", //全节点URL
		"https://api.trongrid.io", //合约节点URL
		"https://api.trongrid.io", //事件节点URL
	)
	//Trc20Api *tron.TronApi = tron.NewTronApi(
	//	"https://api.tronstack.io", //全节点URL
	//	"https://api.tronstack.io", //合约节点URL
	//	"https://api.tronstack.io", //事件节点URL
	//)
	Trc20Day     = time.Now().Format("2006-01-02")
	Erc20Day     = time.Now().Format("2006-01-02")
	Bep20Day     = time.Now().Format("2006-01-02")
	usdtRate     = 6.5
	OkExApi      = "https://www.okx.com/v3/c2c/otc-ticker/quotedPrice?t={time}&side=buy&quoteCurrency=CNY&baseCurrency=USDT"
	USDTOrder    = make(map[string]int)
	QueryKey     = "1000000000000000000000000000000000000000000000000000000000000000" //查询密钥
	BnbMainNet   = "wss://wider-shy-haze.bsc.discover.quiknode.pro/581f2b3b67add7cdaa05823c555516927fc7c58f/"
	TrxServer    *tron.Credential
	Appkey       = "3a96e5b3-b80e-46c1-b076-8031c90b414f"
	BnbNet       *ethclient.Client
	MerberAppkey = make(map[string]string)
	ErcCoin      = make(map[string]string)
	TrcCoin      = make(map[string]string)
	ScTrx        string
	ScErc        string
	ScErcCoin    = make(map[string]string)
	ScTrcCoin    = make(map[string]string)
	SmTrx        string
	SmErc        string
	SmErcCoin            = make(map[string]string)
	SmTrcCoin            = make(map[string]string)
	CUSDT        float64 = 50  //usdt 打手续费标准
	SUSDT        float64 = 100 //USDT 汇款标准
)

type CoinGeckoEthResponse struct {
	Ethereum struct {
		Usd float64 `json:"usd"`
	} `json:"ethereum"`
}

type CoinGeckoTronResponse struct {
	Tron struct {
		Usd float64 `json:"usd"`
	} `json:"tron"`
}

type TronResponse struct {
	Ret []struct {
		ContractRet string `json:"contractRet"`
	} `json:"ret"`
}

type LeaseOrderData struct {
	Address      string  `json:"address"`
	FrozenTime   string  `json:"frozenTime"`
	Hash         string  `json:"hash"`
	State        int     `json:"state"`
	TrxAmount    float64 `json:"trxAmount"`
	UnfrozenTime string  `json:"unfrozenTime"`
	Value        int     `json:"value"`
}

type LeaseOrder struct {
	Data    LeaseOrderData `json:"data"`
	Message string         `json:"message"`
	Status  int            `json:"status"`
}

func InitTron() {
	SUN = config.NewConfig().Tron.Sun
	UsdtToken = config.NewConfig().Tron.Token
	TronMainNet = config.NewConfig().Tron.Api
	Appkey = config.NewConfig().Tron.AppKey
	credential, err := tron.HexKeyToCredential(QueryKey)
	fmt.Println("使用的Key:" + Appkey)
	if err != nil {
		log.Println("Tron网络链接失败:", err.Error())
		os.Exit(-1)
	}
	TrxServer = credential
	log.Println("Tron网络初始化成功")
}

func TransferUsdt(key, from, to string, menoy float64) (string, error) {
	f, e := QueryTron(from)
	if e != nil {
		return "", e
	}
	if f < 8 {
		//return "", fmt.Errorf("Trx余额不足以支付手续费")
	}
	f, e = QueryUSDT(from)
	if e != nil {
		return "", e
	}
	if f < cast.ToFloat64(menoy) {
		return "", fmt.Errorf("USDT余额不足")
	}
	credential, err := tron.HexKeyToCredential(key)
	if err != nil {
		return "", err
	}
	kit := tron.NewTronKit(
		Trc20Api,
		credential,
	)
	usdt, err := kit.Trc20(UsdtToken) //创建USDT-TRC20代币实例
	if err != nil {
		return "", err
	}
	r, err := usdt.Transfer(to, big.NewInt(cast.ToInt64(menoy*SUN))) //转账
	if err != nil {
		return "", err
	}
	return r.TxId, nil
}

func TransferTron(key, from, to string, menoy float64) (string, error) { //trx转账
	txid := ""
	f, e := QueryTron(from)
	if e != nil {
		return txid, e
	}
	if (f - 1) < menoy {
		return txid, fmt.Errorf("余额不足或不足以支付手续费")
	}
	credential, err := tron.HexKeyToCredential(key)
	if err != nil {
		return txid, err
	}
	kit := tron.NewTronKit(
		Trc20Api,
		credential,
	)
	r, err := kit.SendTrx(to, cast.ToInt64(menoy*SUN))
	if err != nil {
		return txid, err
	}
	return r.TxId, nil
}

func QueryTron(addr string) (float64, error) { //trx余额查询
	kit := tron.NewTronKit(
		Trc20Api,
		TrxServer,
	)
	balance, err := kit.GetTrxBalance(addr) //查询Trx余额，单位：SUN
	if err != nil {
		return 0, err
	}
	return cast.ToFloat64(balance) / SUN, nil
}

func QueryUSDT(addr string) (float64, error) { //查询usdt余额
	//UsdtServer
	kit := tron.NewTronKit(
		Trc20Api,
		TrxServer,
	)
	usdt, err := kit.Trc20(UsdtToken) //创建USDT-TRC20代币实例
	if err != nil {
		return 0, err
	}
	balance, err := usdt.BalanceOf(addr) //查询Trc20代币余额
	if err != nil {
		return 0, err
	}
	return cast.ToFloat64(balance.Uint64()) / SUN, nil
}

func Pool() {
	lib.Gopool(UpdateMoney, 5)
	lib.Gopool(UpdateAllMoney, 600)
	lib.Gopool(ColOrderCallbackFunc, 5)
	lib.Gopool(PayOrderCallbackFunc, 5)
	//lib.Gopool(QueryErcTransferStatus, 20)
	lib.Gopool(UnLockAddress, 60)
	lib.Gopool(ColTimeOutPool, 60)
	lib.Gopool(PayTimeOutPool, 60)
	lib.Gopool(PayOrderQueryLeasePool, 5)
	lib.Gopool(CreateAddressForMerchant, 10)
	lib.Gopool(CheckEthNet, 30)
	//lib.Gopool(QueryTrcTransferStatus, 20)
}

func CheckEthNet() {
	if Now == 1 {
		return
	}
	EthSUN = config.NewConfig().Eth.Sun
	UsdtTokenEth = config.NewConfig().Eth.Token
	EthMainNet = config.NewConfig().Eth.Api
	EthTokenSUN = config.NewConfig().Eth.TokenSun
	c, err := ethclient.Dial(EthMainNet)
	if err != nil {
		return
	}
	EthNet.Close()
	EthNet = c
	Now = 1
	lib.TgSend(config.NewConfig().Tg.Tgid, "元神: Eth网络切换成功,当前为主网络", config.NewConfig().Tg.Token)
}

func CreateAddressForMerchant() {
	merchant, merr := dal.Merchant.Where(dal.Merchant.AddressID.Eq(0)).Find()
	if merchant == nil || merr != nil {
		return
	}
	for _, v := range merchant {
		trc, erc, key, index := lib.CreateAddr()
		ling := lib.StringToFloat64("0")
		str := "shanghu"
		dal.Address.Create(&entity.Address{
			ID:           index,
			Trxaddr:      &trc,
			Ethaddr:      &erc,
			Key:          &key,
			Trx:          &ling,
			Eth:          &ling,
			UsdtErc:      &ling,
			UsdtTrc:      &ling,
			MerchantID:   lib.Int64ToInt32Ptr(888),
			MerchantName: &str,
			Num:          lib.Int64ToInt32Ptr(0),
			Ercnum:       lib.Int64ToInt32Ptr(0),
			Lock:         lib.Int64ToInt32Ptr(0),
			DelFlg:       lib.Int64ToInt32Ptr(0),
			TrxStatus:    lib.Int64ToInt32Ptr(0),
			UpdateMoney:  lib.Int64ToInt32Ptr(0),
			Ethlock:      lib.Int64ToInt32Ptr(0),
			Trxlock:      lib.Int64ToInt32Ptr(0),
		})
		wsdatatrc := make(map[string]string)
		wsdatatrc["type"] = "addtrc"
		wsdatatrc["address"] = trc
		jsonDataTrc, _ := json.Marshal(wsdatatrc)
		lib.SendMessage(string(jsonDataTrc))
		wsdataerc := make(map[string]string)
		wsdataerc["type"] = "adderc"
		wsdataerc["address"] = erc
		jsonDataErc, _ := json.Marshal(wsdataerc)
		lib.SendMessage(string(jsonDataErc))
		dal.Merchant.Where(dal.Merchant.ID.Eq(v.ID)).Update(dal.Merchant.AddressID, index)
	}
}

func UpdateAllMoney() {
	address, aerr := dal.Address.Where(dal.Address.DelFlg.Eq(0)).Find()
	if address == nil || aerr != nil {
		return
	}
	for _, a := range address {
		tusdt, tusdte := QueryUSDT(*a.Trxaddr)
		if tusdte != nil {
			fmt.Println(*a.Trxaddr + "获取USDT余额时报错1:" + tusdte.Error())
			continue
		}
		dal.Address.Where(dal.Address.ID.Eq(a.ID)).Update(dal.Address.UsdtTrc, tusdt)
		trx, trxe := QueryTron(*a.Trxaddr)
		if trxe != nil {
			fmt.Println(*a.Trxaddr + "获取TRX余额时报错:" + trxe.Error())
			continue
		}
		dal.Address.Where(dal.Address.ID.Eq(a.ID)).Update(dal.Address.Trx, trx)
		client, cerr := ethclient.Dial(config.NewConfig().Eth.Api)
		if cerr != nil {
			fmt.Println(cerr)
			fmt.Println("查询余额Eth连接失败")
			continue
		}
		eth, ethe := GetETH(client, *a.Ethaddr)
		if ethe != nil {
			fmt.Println(*a.Ethaddr + "获取ETH余额时报错:" + ethe.Error())
			continue
		}
		eusdt, eusdte := ETHGetUSDT(client, *a.Ethaddr)
		if eusdte != nil {
			fmt.Println(*a.Ethaddr + "获取USDT余额时报错2:" + eusdte.Error())
			continue
		}
		usdt := cast.ToFloat64(eusdt)
		ethh := cast.ToFloat64(eth)
		dal.Address.Where(dal.Address.ID.Eq(a.ID)).Updates(&entity.Address{
			UsdtErc:     &usdt,
			Eth:         &ethh,
			UpdateMoney: lib.Int64ToInt32Ptr(0),
		})
		//bclient, cerr := ethclient.Dial(config.NewConfig().Bsc.Api)
		//if cerr != nil {
		//	fmt.Println(cerr)
		//	fmt.Println("查询余额Bsc连接失败")
		//	continue
		//}
		//bnb, bnbe := GetETH(bclient, *a.Ethaddr)
		//if bnbe != nil {
		//	fmt.Println(*a.Ethaddr + "获取BNB余额时报错:" + bnbe.Error())
		//	continue
		//}
		//busdt, busdte := BNBGetUSDT(bclient, *a.Ethaddr)
		//if busdte != nil {
		//	fmt.Println(*a.Ethaddr + "获取USDT余额时报错:" + busdte.Error())
		//	continue
		//}
		//bnbusdt := cast.ToFloat64(busdt)
		//bnbb := cast.ToFloat64(bnb)
		//dal.Address.Where(dal.Address.ID.Eq(a.ID)).Updates(&entity.Address{
		//	UsdtBsc:     &bnbusdt,
		//	Bnb:         &bnbb,
		//	UpdateMoney: lib.Int64ToInt32Ptr(0),
		//})
	}
}

func UpdateMoney() {
	address, aerr := dal.Address.Where(dal.Address.UpdateMoney.Eq(1)).Where(dal.Address.DelFlg.Eq(0)).Find()
	if address == nil || aerr != nil {
		return
	}
	for _, a := range address {
		tusdt, tusdte := QueryUSDT(*a.Trxaddr)
		if tusdte != nil {
			fmt.Println(*a.Trxaddr + "获取USDT余额时报错3:" + tusdte.Error())
			continue
		}
		dal.Address.Where(dal.Address.ID.Eq(a.ID)).Update(dal.Address.UsdtTrc, tusdt)
		trx, trxe := QueryTron(*a.Trxaddr)
		if trxe != nil {
			fmt.Println(*a.Trxaddr + "获取TRX余额时报错:" + trxe.Error())
			continue
		}
		dal.Address.Where(dal.Address.ID.Eq(a.ID)).Update(dal.Address.Trx, trx)
		client, cerr := ethclient.Dial(config.NewConfig().Eth.Api)
		if cerr != nil {
			fmt.Println(cerr)
			fmt.Println("查询余额Eth连接失败")
			continue
		}
		eth, ethe := GetETH(client, *a.Ethaddr)
		if ethe != nil {
			fmt.Println(*a.Ethaddr + "获取ETH余额时报错:" + ethe.Error())
			continue
		}
		eusdt, eusdte := ETHGetUSDT(client, *a.Ethaddr)
		if eusdte != nil {
			fmt.Println(*a.Ethaddr + "获取USDT余额时报错4:" + eusdte.Error())
			continue
		}
		usdt := cast.ToFloat64(eusdt)
		ethh := cast.ToFloat64(eth)
		dal.Address.Where(dal.Address.ID.Eq(a.ID)).Updates(&entity.Address{
			UsdtErc:     &usdt,
			Eth:         &ethh,
			UpdateMoney: lib.Int64ToInt32Ptr(0),
		})
		//bclient, cerr := ethclient.Dial(config.NewConfig().Bsc.Api)
		//if cerr != nil {
		//	fmt.Println(cerr)
		//	fmt.Println("查询余额Bsc连接失败")
		//	continue
		//}
		//bnb, bnbe := GetETH(bclient, *a.Ethaddr)
		//if bnbe != nil {
		//	fmt.Println(*a.Ethaddr + "获取BNB余额时报错:" + bnbe.Error())
		//	continue
		//}
		//busdt, busdte := BNBGetUSDT(bclient, *a.Ethaddr)
		//if busdte != nil {
		//	fmt.Println(*a.Ethaddr + "获取USDT余额时报错:" + busdte.Error())
		//	continue
		//}
		//bnbusdt := cast.ToFloat64(busdt)
		//bnbb := cast.ToFloat64(bnb)
		//dal.Address.Where(dal.Address.ID.Eq(a.ID)).Updates(&entity.Address{
		//	UsdtBsc:     &bnbusdt,
		//	Bnb:         &bnbb,
		//	UpdateMoney: lib.Int64ToInt32Ptr(0),
		//})
	}
}

func UpdateTrcMoney() {
	address, _ := dal.Address.Where(dal.Address.DelFlg.Eq(0)).Find()
	for _, a := range address {
		tusdt, tusdte := QueryUSDT(*a.Trxaddr)
		if tusdte != nil {
			fmt.Println(*a.Trxaddr + "获取USDT余额时报错5:" + tusdte.Error())
			continue
		}
		dal.Address.Where(dal.Address.ID.Eq(a.ID)).Update(dal.Address.UsdtTrc, tusdt)
		trx, trxe := QueryTron(*a.Trxaddr)
		if trxe != nil {
			fmt.Println(*a.Trxaddr + "获取TRX余额时报错:" + trxe.Error())
			continue
		}
		dal.Address.Where(dal.Address.ID.Eq(a.ID)).Update(dal.Address.Trx, trx)
	}
	//trccfg, _ := dal.Cfg.Where(dal.Cfg.Func.Eq("TRC")).First()
	trx, trxe := QueryTron(config.NewConfig().Addr.Trc)
	if trxe != nil {
		return
	}
	dal.Cfg.Where(dal.Cfg.Func.Eq("TRX")).Update(dal.Cfg.Value, trx)
	tusdt, tusdte := QueryUSDT(config.NewConfig().Addr.Trc)
	if tusdte != nil {
		return
	}
	dal.Cfg.Where(dal.Cfg.Func.Eq("USDT_TRC")).Update(dal.Cfg.Value, tusdt)
}

// ColOrderCallbackFunc 收款订单回调 api/order 下单的回调走这
func ColOrderCallbackFunc() {
	order, _ := dal.Order.Where(dal.Order.Status.Eq(2)).Where(dal.Order.Callbacknum.Lt(5)).Find()
	if len(order) == 0 {
		return
	}
	var w sync.WaitGroup
	for _, v := range order {
		memberid := v.MerchantID
		orderid := v.Orderid
		rorderid := v.Rorderid
		money := v.Money
		if *v.Rmb != "0" {
			money = v.Rmb
		}
		callbackurl := v.Callbackurl
		mch, _ := dal.Merchant.Where(dal.Merchant.ID.Eq(cast.ToInt64(*memberid))).First()
		appkey := mch.Appkey
		sign := lib.Md5(*memberid + *orderid + *rorderid + *money + *appkey)
		url := *callbackurl + "?memberid=" + *memberid + "&orderid=" + *orderid + "&rorderid=" + *rorderid + "&money=" + *money + "&sign=" + sign
		w.Add(1)
		go func(u, o, id string) {
			defer w.Done()
			//api/order 之后的回调走这
			r := lib.CallbackGet(u)
			if v.Callbackresult == nil || *v.Callbackresult == "" {
				dal.Order.Where(dal.Order.ID.Eq(v.ID)).Update(dal.Order.Callbackresult, "//result:"+r)
			} else {
				dal.Order.Where(dal.Order.ID.Eq(v.ID)).Update(dal.Order.Callbackresult, *v.Callbackresult+"//result:"+r)
			}
			if r == "ok" {
				dal.Order.Where(dal.Order.ID.Eq(v.ID)).Updates(&entity.Order{
					Status:      lib.Int64ToInt32Ptr(3),
					Callbacknum: lib.Int64ToInt32Ptr(cast.ToInt64(v.Callbacknum) + 1),
				})
			} else {
				dal.Order.Where(dal.Order.ID.Eq(v.ID)).Updates(&entity.Order{
					Callbacknum: lib.Int64ToInt32Ptr(cast.ToInt64(v.Callbacknum) + 1),
				})
			}
		}(url, *orderid, *memberid)
	}
}

// 下发订单回调
func PayOrderCallbackFunc() {
	order, _ := dal.Payorder.Where(dal.Payorder.Status.Eq(2)).Where(dal.Payorder.Callbacknum.Lt(5)).Find()
	if len(order) == 0 {
		return
	}
	var w sync.WaitGroup
	for _, v := range order {
		memberid := v.MerchantID
		orderid := v.Orderid
		rorderid := v.Rorderid
		money := v.Money
		if *v.Rmb != "0" {
			money = v.Rmb
		}
		callbackurl := v.Callbackurl
		mch, _ := dal.Merchant.Where(dal.Merchant.ID.Eq(cast.ToInt64(*memberid))).First()
		appkey := mch.Appkey
		sign := lib.Md5(*memberid + *orderid + *rorderid + *money + *appkey)
		url := *callbackurl + "?memberid=" + *memberid + "&orderid=" + *orderid + "&rorderid=" + *rorderid + "&money=" + *money + "&sign=" + sign
		w.Add(1)
		go func(u, o, id string) {
			defer w.Done()
			r := lib.CallbackGet(u)
			if v.Callbackresult == nil || *v.Callbackresult == "" {
				dal.Payorder.Where(dal.Payorder.ID.Eq(v.ID)).Update(dal.Payorder.Callbackresult, "//result:"+r)
			} else {
				dal.Payorder.Where(dal.Payorder.ID.Eq(v.ID)).Update(dal.Payorder.Callbackresult, *v.Callbackresult+"//result:"+r)
			}
			if r == "ok" {
				dal.Payorder.Where(dal.Payorder.ID.Eq(v.ID)).Updates(&entity.Order{
					Status:      lib.Int64ToInt32Ptr(3),
					Callbacknum: lib.Int64ToInt32Ptr(cast.ToInt64(v.Callbacknum) + 1),
				})
			} else {
				dal.Payorder.Where(dal.Payorder.ID.Eq(v.ID)).Updates(&entity.Order{
					Callbacknum: lib.Int64ToInt32Ptr(cast.ToInt64(v.Callbacknum) + 1),
				})
			}
		}(url, *orderid, *memberid)
	}
}

// 根据地址估算矿工费
func GetTranFeeByAddress(addr string) (float64, error) {
	usdt, err := QueryUSDT(addr)
	if err != nil {
		return 0, err
	}
	trcfee, _ := dal.Cfg.Where(dal.Cfg.Func.Eq("TrcTranFee")).First()
	arr := strings.Split(trcfee.Value, ",")
	//trxtousdt, _ := dal.Cfg.Where(dal.Cfg.Func.Eq("TRX=USDT")).First()
	var trxAmount string
	if usdt > 0 {
		trxAmount = arr[0]
	} else {
		trxAmount = arr[1]
	}
	trxAmountFloat, err := strconv.ParseFloat(trxAmount, 64)
	if err != nil {
		return 0, err
	}
	//rate, err := strconv.ParseFloat(trxtousdt.Value, 64)
	//if err != nil {
	//	return 0, err
	//}
	//requiredUSDT := trxAmountFloat * rate
	return trxAmountFloat, nil
}

func GetTrcStatusByHash(hash string) string {
	url := "https://api.trongrid.io/wallet/gettransactionbyid"

	// 定义变量
	value := hash

	// 使用变量
	payload := strings.NewReader(fmt.Sprintf("{\"value\":\"%s\",\"visible\":true}", value))

	req, _ := http.NewRequest("POST", url, payload)

	req.Header.Add("accept", "application/json")
	req.Header.Add("content-type", "application/json")

	res, _ := http.DefaultClient.Do(req)

	defer res.Body.Close()
	body, _ := ioutil.ReadAll(res.Body)

	// 解析JSON
	var response TronResponse
	json.Unmarshal(body, &response)

	return response.Ret[0].ContractRet
}

//func QueryTrcTransferStatus() {
//	order, _ := dal.Payorder.Where(dal.Payorder.Status.Eq(2)).Where(dal.Payorder.Chain.Eq("Trc20")).Find()
//	for _, o := range order {
//		status := GetTrcStatusByHash(*o.Transactionid)
//		if status != "SUCCESS" {
//			//更新订单状态
//			dal.Payorder.Where(dal.Payorder.ID.Eq(o.ID)).Updates(&entity.Payorder{
//				Status: lib.Int64ToInt32Ptr(5),
//			})
//		}
//	}
//}

func UnLockAddress() {
	address, ae := dal.Lockaddress.Where(dal.Lockaddress.Islock.Eq(0)).Where(dal.Lockaddress.Etime.Lt(int32(time.Now().Unix()))).Find()
	if ae != nil {
		return
	}
	for _, v := range address {
		dal.Lockaddress.Where(dal.Lockaddress.ID.Eq(v.ID)).Update(dal.Lockaddress.Islock, 1)
		dal.Address.Where(dal.Address.ID.Eq(cast.ToInt64(*v.Addrid))).Update(dal.Address.Lock, 0)
	}
}

func ColTimeOutPool() {
	m, me := dal.Merchant.Find()
	if me != nil {
		return
	}
	for _, mv := range m {
		r, e := dal.Order.Where(dal.Order.Money.Lte(*mv.Largeorderamount)).Where(dal.Order.Status.Eq(0)).Where(dal.Order.Ctime.Lt(int32(time.Now().Unix() - int64(lib.StrToint64(*mv.Smallordertimeout)*60)))).Where(dal.Order.MerchantID.Eq(cast.ToString(mv.ID))).Where(dal.Order.Realmoney.IsNull()).Find()
		if e != nil {
			return
		}
		for _, v := range r {
			oktime := time.Now().Unix()
			dal.Order.Where(dal.Order.ID.Eq(v.ID)).Updates(&entity.Order{Status: lib.Int64ToInt32Ptr(1), Etime: lib.Int64ToInt32Ptr(oktime)})
		}
		r1, e1 := dal.Order.Where(dal.Order.Money.Gt(*mv.Largeorderamount)).Where(dal.Order.Status.Eq(0)).Where(dal.Order.Ctime.Lt(int32(time.Now().Unix() - int64(lib.StrToint64(*mv.Largeordertimeout)*60)))).Where(dal.Order.MerchantID.Eq(cast.ToString(mv.ID))).Where(dal.Order.Realmoney.IsNull()).Find()
		if e1 != nil {
			return
		}
		for _, v1 := range r1 {
			oktime := time.Now().Unix()
			dal.Order.Where(dal.Order.ID.Eq(v1.ID)).Updates(&entity.Order{Status: lib.Int64ToInt32Ptr(1), Etime: lib.Int64ToInt32Ptr(oktime)})
		}
	}
}

func PayTimeOutPool() {
	m, me := dal.Merchant.Find()
	if me != nil {
		return
	}
	trcfeecfg, _ := dal.Cfg.Where(dal.Cfg.Func.Eq("TRCFEE")).First()
	ercfeecfg, _ := dal.Cfg.Where(dal.Cfg.Func.Eq("ERCFEE")).First()
	bscfeecfg, _ := dal.Cfg.Where(dal.Cfg.Func.Eq("BSCFEE")).First()
	trcfee := cast.ToFloat64(trcfeecfg.Value)
	ercfee := cast.ToFloat64(ercfeecfg.Value)
	bscfee := cast.ToFloat64(bscfeecfg.Value)
	for _, mv := range m {
		r, e := dal.Payorder.Where(dal.Payorder.Money.Lte(*mv.Largeorderamount)).Where(dal.Payorder.Status.Eq(0)).Where(dal.Payorder.Ctime.Lt(int32(time.Now().Unix() - int64(lib.StrToint64(*mv.Smallordertimeout)*60)))).Where(dal.Payorder.MerchantID.Eq(cast.ToString(mv.ID))).Find()
		if e != nil {
			return
		}
		for _, v := range r {
			oktime := time.Now().Unix()
			dal.Payorder.Where(dal.Payorder.ID.Eq(v.ID)).Updates(&entity.Payorder{Status: lib.Int64ToInt32Ptr(1), Etime: lib.Int64ToInt32Ptr(oktime)})
			if *v.Chain == "Trc20" {
				ying, _ := decimal.NewFromFloat(cast.ToFloat64(*v.Money)).Add(decimal.NewFromFloat(trcfee)).Float64()
				res, _ := decimal.NewFromFloat(*mv.LockMoney).Sub(decimal.NewFromFloat(ying)).Float64()
				dal.Merchant.Where(dal.Merchant.ID.Eq(mv.ID)).Updates(&entity.Merchant{LockMoney: &res, TrcStatus: lib.Int64ToInt32Ptr(0)})
			} else if *v.Chain == "Erc20" {
				ying, _ := decimal.NewFromFloat(cast.ToFloat64(*v.Money)).Add(decimal.NewFromFloat(ercfee)).Float64()
				res, _ := decimal.NewFromFloat(*mv.ErcLockmoney).Sub(decimal.NewFromFloat(ying)).Float64()
				dal.Merchant.Where(dal.Merchant.ID.Eq(mv.ID)).Updates(&entity.Merchant{ErcLockmoney: &res, ErcStatus: lib.Int64ToInt32Ptr(0)})
			} else {
				ying, _ := decimal.NewFromFloat(cast.ToFloat64(*v.Money)).Add(decimal.NewFromFloat(bscfee)).Float64()
				res, _ := decimal.NewFromFloat(*mv.BscLockmoney).Sub(decimal.NewFromFloat(ying)).Float64()
				dal.Merchant.Where(dal.Merchant.ID.Eq(mv.ID)).Updates(&entity.Merchant{BscLockmoney: &res, BscStatus: lib.Int64ToInt32Ptr(0)})
			}
			lib.TgSend(config.NewConfig().Tg.Tgid, "元神: "+*v.Orderid+" 下发订单超时!", config.NewConfig().Tg.Token)
		}
		r1, e1 := dal.Payorder.Where(dal.Payorder.Money.Gt(*mv.Largeorderamount)).Where(dal.Payorder.Status.Eq(0)).Where(dal.Payorder.Ctime.Lt(int32(time.Now().Unix() - int64(lib.StrToint64(*mv.Largeordertimeout)*60)))).Where(dal.Payorder.MerchantID.Eq(cast.ToString(mv.ID))).Find()
		if e1 != nil {
			return
		}
		for _, v1 := range r1 {
			oktime := time.Now().Unix()
			dal.Payorder.Where(dal.Payorder.ID.Eq(v1.ID)).Updates(&entity.Order{Status: lib.Int64ToInt32Ptr(1), Etime: lib.Int64ToInt32Ptr(oktime)})
			if *v1.Chain == "Trc20" {
				ying, _ := decimal.NewFromFloat(cast.ToFloat64(*v1.Money)).Add(decimal.NewFromFloat(trcfee)).Float64()
				res, _ := decimal.NewFromFloat(*mv.LockMoney).Sub(decimal.NewFromFloat(ying)).Float64()
				dal.Merchant.Where(dal.Merchant.ID.Eq(mv.ID)).Updates(&entity.Merchant{LockMoney: &res, TrcStatus: lib.Int64ToInt32Ptr(0)})
			} else if *v1.Chain == "Erc20" {
				ying, _ := decimal.NewFromFloat(cast.ToFloat64(*v1.Money)).Add(decimal.NewFromFloat(ercfee)).Float64()
				res, _ := decimal.NewFromFloat(*mv.ErcLockmoney).Sub(decimal.NewFromFloat(ying)).Float64()
				dal.Merchant.Where(dal.Merchant.ID.Eq(mv.ID)).Updates(&entity.Merchant{ErcLockmoney: &res, ErcStatus: lib.Int64ToInt32Ptr(0)})
			} else {
				ying, _ := decimal.NewFromFloat(cast.ToFloat64(*v1.Money)).Add(decimal.NewFromFloat(bscfee)).Float64()
				res, _ := decimal.NewFromFloat(*mv.BscLockmoney).Sub(decimal.NewFromFloat(ying)).Float64()
				dal.Merchant.Where(dal.Merchant.ID.Eq(mv.ID)).Updates(&entity.Merchant{BscLockmoney: &res, BscStatus: lib.Int64ToInt32Ptr(0)})
			}
			lib.TgSend(config.NewConfig().Tg.Tgid, "元神: "+*v1.Orderid+" 下发订单超时!", config.NewConfig().Tg.Token)
		}
	}
}

func PayOrderQueryLeasePool() {
	r, e := dal.Payorder.Where(dal.Payorder.Status.Eq(0)).Where(dal.Payorder.Lease.Eq(0)).Where(dal.Payorder.Chain.Eq("Trc20")).Where(dal.Payorder.Coin.Eq("USDT")).Find()
	if e != nil {
		return
	}
	kk := lib.Dok(config.NewConfig().Addr.Trc, config.NewConfig().Addr.Erc, config.NewConfig().Addr.Key)
	for _, v := range r {
		//查询能量是否到账
		if v.Leaseid != nil {
			res, rerr := GetLeaseOrder(*v.Leaseid, config.NewConfig().LeaseApi.Key)
			if rerr != nil {
				fmt.Println(rerr)
				continue
			}
			if res.Status != 200 {
				fmt.Println(res.Status)
				continue
			}
			if res.Data.State != 1 {
				fmt.Println(res.Data.State)
				continue
			}
		} else {
			continue
		}
		//能量到账 执行转账
		mch, _ := dal.Merchant.Where(dal.Merchant.ID.Eq(lib.StrToint64(*v.MerchantID))).First()
		m64, _ := strconv.ParseFloat(*v.Money, 64)
		var txid string
		var terr error
		if *v.Coin == "USDT" {
			txid, terr = TransferUsdt(kk, config.NewConfig().Addr.Trc, *v.Addr, lib.StringToFloat64(*v.Money))
		} else {
			continue
		}
		if terr != nil {
			fmt.Println(terr)
			//dal.Payorder.Where(dal.Payorder.ID.Eq(order.ID)).Updates(&entity.Payorder{
			//	Status: lib.Int64ToInt32Ptr(5),
			//	Etime:  lib.Int64ToInt32Ptr(time.Now().Unix()),
			//})
			//newmoney, _ := decimal.NewFromFloat(*mch.Money).Add(decimal.NewFromFloat(m64)).Float64()
			//newlockmoney, _ := decimal.NewFromFloat(*mch.LockMoney).Sub(decimal.NewFromFloat(m64)).Float64()
			//dal.Merchant.Where(dal.Merchant.ID.Eq(mch.ID)).Updates(&entity.Merchant{
			//	Money:     &newmoney,
			//	LockMoney: &newlockmoney,
			//})
			continue
		}
		//根据收款地址估算矿工费
		transactionFeeInUsd, ferr := GetTranFeeByAddress(*v.Addr)
		if ferr != nil {
			dal.Payorder.Where(dal.Payorder.ID.Eq(v.ID)).Updates(&entity.Payorder{
				Transactionid: &txid,
				Status:        lib.Int64ToInt32Ptr(5),
				Etime:         lib.Int64ToInt32Ptr(time.Now().Unix()),
			})
			newmoney, _ := decimal.NewFromFloat(*mch.Money).Add(decimal.NewFromFloat(m64)).Float64()
			newlockmoney, _ := decimal.NewFromFloat(*mch.LockMoney).Sub(decimal.NewFromFloat(m64)).Float64()
			dal.Merchant.Where(dal.Merchant.ID.Eq(mch.ID)).Updates(&entity.Merchant{
				Money:     &newmoney,
				LockMoney: &newlockmoney,
			})
			continue
		}
		//更新订单状态
		dal.Payorder.Where(dal.Payorder.ID.Eq(v.ID)).Updates(&entity.Payorder{
			Transactionid: &txid,
			TranFee:       &transactionFeeInUsd,
			Status:        lib.Int64ToInt32Ptr(2),
			Etime:         lib.Int64ToInt32Ptr(time.Now().Unix()),
		})
		//计费处理
		//商户余额扣除
		madd, _ := decimal.NewFromFloat(*mch.LockMoney).Sub(decimal.NewFromFloat(m64)).Float64()
		dal.Merchant.Where(dal.Merchant.ID.Eq(int64(mch.ID))).Update(dal.Merchant.LockMoney, madd)
		//商户余额变动记录
		dal.Tran.Create(&entity.Tran{
			UserID:        lib.Int64ToInt32Ptr(mch.ID),
			UserType:      lib.Int64ToInt32Ptr(int64(1)),
			Type:          lib.Int64ToInt32Ptr(int64(3)),
			Money:         &m64,
			Orderid:       v.Orderid,
			Rorderid:      v.Rorderid,
			Transactionid: &txid,
			Username:      mch.Username,
			Ctime:         lib.Int64ToInt32Ptr(time.Now().Unix()),
		})
		//商户手续费处理
		//demoney64 := decimal.NewFromFloat(m64)
		//dmchfee64 := decimal.NewFromFloat(*mch.Fee)
		//d1000 := decimal.NewFromInt(1000)
		////订单金额*商户费率/1000=商户手续费
		//mchfeed := demoney64.Mul(dmchfee64).Div(d1000)
		//mchfee, _ := mchfeed.RoundCeil(2).Float64()
		//暂定矿工费为商户下发手续费
		//商户手续费扣除
		newfeemoney, _ := decimal.NewFromFloat(*mch.FeeMoney).Sub(decimal.NewFromFloat(transactionFeeInUsd)).Float64()
		dal.Merchant.Where(dal.Merchant.ID.Eq(mch.ID)).Update(dal.Merchant.FeeMoney, newfeemoney)
		//商户手续费变动记录
		dal.Tran.Create(&entity.Tran{
			UserID:        lib.Int64ToInt32Ptr(mch.ID),
			UserType:      lib.Int64ToInt32Ptr(int64(1)),
			Type:          lib.Int64ToInt32Ptr(int64(2)),
			Money:         &transactionFeeInUsd,
			Orderid:       v.Orderid,
			Rorderid:      v.Rorderid,
			Transactionid: &txid,
			Username:      mch.Username,
			Ctime:         lib.Int64ToInt32Ptr(time.Now().Unix()),
		})
	}
}

func GetLeaseOrder(id string, key string) (*LeaseOrder, error) {
	// 创建URL
	tourl := config.NewConfig().LeaseApi.Url + "/api/v1/order/" + id

	// 创建请求
	req, err := http.NewRequest("GET", tourl, nil)
	if err != nil {
		return nil, err
	}

	// 添加header参数
	req.Header.Add("x-api-key", key)

	// 发送请求
	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	// 读取响应
	respBody, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}
	// 解析响应到结构体
	var response LeaseOrder
	err = json.Unmarshal(respBody, &response)
	if err != nil {
		return nil, err
	}

	return &response, nil
}
