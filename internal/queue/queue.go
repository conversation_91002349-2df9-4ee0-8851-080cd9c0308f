package queue

import (
	"Mapi/config"
	"Mapi/dal"
	"Mapi/internal/lib"
	"Mapi/internal/transfer"
	"Mapi/model/entity"
	"bytes"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"io/ioutil"
	"log"
	"mime/multipart"
	"net/http"
	"strconv"
	"time"

	"github.com/ethereum/go-ethereum/ethclient"
	"github.com/go-redis/redis"
	"github.com/shopspring/decimal"
	"github.com/spf13/cast"
)

var client *redis.Client

type LeaseData struct {
	Duration  int     `json:"duration"`
	OrderNo   int64   `json:"orderNo"`
	State     int     `json:"state"`
	TrxAmount float64 `json:"trxAmount"`
	Value     int     `json:"value"`
}

type Lease struct {
	Data    LeaseData `json:"data"`
	Message string    `json:"message"`
	Status  int       `json:"status"`
}

// 启动队列
func StartQueue() *redis.Client {
	client = redis.NewClient(&redis.Options{
		Addr:     config.NewConfig().Redis.Url,
		Password: config.NewConfig().Redis.Password,
		DB:       0,
	})

	_, err := client.Ping().Result()
	if err != nil {
		log.Fatal(err)
	}
	//如果有未下发的单子 在这重新加入队列下发
	AddQueueOrderId()
	return client
}

func AddQueueOrderId() {
	firstOrders, _ := dal.Payorder.Where(dal.Payorder.Status.Eq(0)).First()
	if firstOrders != nil {
		// 检查队列中是否已存在该订单
		queueItems, err := client.LRange("task_queue", 0, -1).Result()
		if err != nil {
			log.Printf("检查队列失败: %v", err)
		} else {
			orderExists := false
			orderID := *firstOrders.Orderid

			for _, item := range queueItems {
				if item == orderID {
					orderExists = true
					break
				}
			}

			// 只有当队列中不存在该订单时才添加
			if !orderExists {
				AddTaskToQueue(orderID)
			}
		}
	}
}

type Remaining struct {
	EnergyRemaining int `json:"energyRemaining"`
}

// 添加任务到队列
func AddTaskToQueue(task string) {
	err := client.LPush("task_queue", task).Err()
	if err != nil {
		log.Fatal(err)
	}
}

// ExecuteTasks 执行任务 执行下发
func ExecuteTasks(client *redis.Client) string {
	go func() {
		for {
			task, err := client.RPop("task_queue").Result()
			if err == redis.Nil {
				time.Sleep(1 * time.Second)
				continue
			} else if err != nil {
				log.Fatal(err)
			}
			//执行任务逻辑
			time.Sleep(time.Second * 1)
			fmt.Println(time.Now().Unix())
			fmt.Println(task)
			order, oerr := dal.Payorder.Where(dal.Payorder.Orderid.Eq(task)).Where(dal.Payorder.Status.Eq(0)).First()
			if order == nil || oerr != nil {
				continue
			}
			mch, _ := dal.Merchant.Where(dal.Merchant.ID.Eq(lib.StrToint64(*order.MerchantID))).First()
			zongaddr, _ := dal.Address.Where(dal.Address.ID.Eq(cast.ToInt64(mch.AddressID))).First()
			keycfg := *zongaddr.Key
			trccfg := *zongaddr.Trxaddr
			erccfg := *zongaddr.Ethaddr
			if keycfg == "" || trccfg == "" || erccfg == "" {
				continue
			}
			akk := lib.Dok(trccfg, erccfg, keycfg)
			//trcfeecfg, _ := dal.Cfg.Where(dal.Cfg.Func.Eq("TRCFEE")).First()
			ercfeecfg, _ := dal.Cfg.Where(dal.Cfg.Func.Eq("ERCFEE")).First()
			bscfeecfg, _ := dal.Cfg.Where(dal.Cfg.Func.Eq("BSCFEE")).First()
			//trcfee := cast.ToFloat64(trcfeecfg.Value)
			ercfee := cast.ToFloat64(ercfeecfg.Value)
			bscfee := cast.ToFloat64(bscfeecfg.Value)
			kk := lib.Dok(trccfg, erccfg, keycfg)
			m64, _ := strconv.ParseFloat(*order.Money, 64)
			if *order.Chain == "Trc20" {
				if *mch.TrcStatus != 0 {
					AddTaskToQueue(*order.Orderid)
					log.Printf("11111")
					continue
				}
				usdt, uerr := transfer.QueryUSDT(trccfg)
				log.Printf("查询usdt余额为：%v", usdt)
				if uerr != nil {
					fmt.Println(uerr)
					AddTaskToQueue(*order.Orderid)
					log.Printf("2222")
					continue
				}
				if usdt < m64 {
					dal.Merchant.Where(dal.Merchant.ID.Eq(mch.ID)).Update(dal.Merchant.TrcStatus, 1)
					AddTaskToQueue(*order.Orderid)
					log.Printf("3333 %v, %v", usdt, m64)
					continue
				}
				_, tuerr := transfer.QueryUSDT(*order.Addr)
				if tuerr != nil {
					fmt.Println(tuerr)
					AddTaskToQueue(*order.Orderid)
					log.Printf("44444 %v", tuerr)
					continue
				}
				//如果能量到账就不再租了
				Energy, _ := CheckEnergyReceived(trccfg)
				if Energy < 100000 {
					//给主钱包组能量，用户下发使用
					responseTG, err := DoLeaseTG(trccfg, akk)
					if err != nil {
						fmt.Println("给" + trccfg + "租能量报错，错误为：" + err.Error())
						AddTaskToQueue(*order.Orderid)
						continue
					}
					if responseTG {
						//新的租能量方法
						_, err := CheckEnergyReceived(trccfg)
						if err != nil {
							fmt.Println("检查能量失败：" + err.Error())
							AddTaskToQueue(*order.Orderid)
							continue
						}
						log.Printf("55555")
						leaseid := cast.ToString("TG租能量订单")
						err = dal.Lease.Create(&entity.Lease{
							Addressid: lib.Int64ToInt32Ptr(zongaddr.ID),
							Leaseid:   &leaseid,
							Orderid:   lib.Int64ToInt32Ptr(order.ID),
							Status:    lib.Int64ToInt32Ptr(0),
							Ctime:     lib.Int64ToInt32Ptr(time.Now().Unix()),
						})
						if err != nil {
							log.Printf("创建租能量订单失败：" + err.Error())
							return
						}
						log.Printf("6666666")
					}
					continue
				} else {
					log.Printf("租能量成功,剩余 %v", Energy)
				}

				//else {
				//如果新的不好使 走老的租能量方法
				//if tusdt > 0 {
				//	response, lerr := DoLease(trccfg, "1", "65000", config.NewConfig().LeaseApi.Key)
				//	if lerr != nil {
				//		fmt.Println("给" + trccfg + "租能量报错")
				//		fmt.Println("Error:" + lerr.Error())
				//		AddTaskToQueue(*order.Orderid)
				//		continue
				//	}
				//	if response.Status != 200 {
				//		fmt.Println("给" + trccfg + "租能量状态码错误")
				//		fmt.Println("Status:" + cast.ToString(response.Status))
				//		AddTaskToQueue(*order.Orderid)
				//		continue
				//	}
				//	fmt.Println("给" + trccfg + "租能量成功,订单号:" + cast.ToString(response.Data.OrderNo))
				//	leaseid := cast.ToString(response.Data.OrderNo)
				//	dal.Lease.Create(&entity.Lease{
				//		Addressid: lib.Int64ToInt32Ptr(zongaddr.ID),
				//		Leaseid:   &leaseid,
				//		Orderid:   lib.Int64ToInt32Ptr(order.ID),
				//		Status:    lib.Int64ToInt32Ptr(0),
				//		Ctime:     lib.Int64ToInt32Ptr(time.Now().Unix()),
				//	})
				//} else {
				//	response, lerr := DoLease(trccfg, "1", "131000", config.NewConfig().LeaseApi.Key)
				//	if lerr != nil {
				//		fmt.Println("给" + trccfg + "租能量报错")
				//		fmt.Println("Error:" + lerr.Error())
				//		AddTaskToQueue(*order.Orderid)
				//		continue
				//	}
				//	if response.Status != 200 {
				//		fmt.Println("给" + trccfg + "租能量状态码错误")
				//		fmt.Println("Status:" + cast.ToString(response.Status))
				//		AddTaskToQueue(*order.Orderid)
				//		continue
				//	}
				//	fmt.Println("给" + trccfg + "租能量成功,订单号:" + cast.ToString(response.Data.OrderNo))
				//	leaseid := cast.ToString(response.Data.OrderNo)
				//	dal.Lease.Create(&entity.Lease{
				//		Addressid: lib.Int64ToInt32Ptr(zongaddr.ID),
				//		Leaseid:   &leaseid,
				//		Orderid:   lib.Int64ToInt32Ptr(order.ID),
				//		Status:    lib.Int64ToInt32Ptr(0),
				//		Ctime:     lib.Int64ToInt32Ptr(time.Now().Unix()),
				//	})
				//}
				//}
			} else if *order.Chain == "Erc20" {
				if *mch.ErcStatus != 0 {
					AddTaskToQueue(*order.Orderid)
					continue
				}
				ercclient, cerr := ethclient.Dial(config.NewConfig().Eth.Api)
				if cerr != nil {
					AddTaskToQueue(*order.Orderid)
					continue
				}
				usdt, uerr := transfer.ETHGetUSDT(ercclient, erccfg)
				if uerr != nil {
					AddTaskToQueue(*order.Orderid)
					continue
				}
				ercclient.Close()
				if cast.ToFloat64(usdt) < m64 {
					dal.Merchant.Where(dal.Merchant.ID.Eq(mch.ID)).Update(dal.Merchant.ErcStatus, 1)
					AddTaskToQueue(*order.Orderid)
					continue
				}
				//以太坊
				txid, transactionFeeInEthFloat, terr := transfer.Erc20Send(kk, *order.Addr, lib.StringToFloat64(*order.Money))
				if terr != nil {
					fmt.Println(terr)
					AddTaskToQueue(*order.Orderid)
					continue
				}
				//更新订单状态
				dal.Payorder.Where(dal.Payorder.ID.Eq(order.ID)).Updates(&entity.Payorder{
					Transactionid: &txid,
					TranFee:       &transactionFeeInEthFloat,
					Status:        lib.Int64ToInt32Ptr(2),
					Etime:         lib.Int64ToInt32Ptr(time.Now().Unix()),
				})
				//计费处理
				//商户余额扣除
				yingsub, _ := decimal.NewFromFloat(m64).Add(decimal.NewFromFloat(ercfee)).Float64()
				madd, _ := decimal.NewFromFloat(*mch.ErcLockmoney).Sub(decimal.NewFromFloat(yingsub)).Float64()
				dal.Merchant.Where(dal.Merchant.ID.Eq(int64(mch.ID))).Update(dal.Merchant.ErcLockmoney, madd)
				//商户余额变动记录
				dal.Tran.Create(&entity.Tran{
					UserID:        lib.Int64ToInt32Ptr(mch.ID),
					UserType:      lib.Int64ToInt32Ptr(int64(1)),
					Type:          lib.Int64ToInt32Ptr(int64(7)),
					Money:         &m64,
					Orderid:       order.Orderid,
					Rorderid:      order.Rorderid,
					Transactionid: &txid,
					Username:      mch.Username,
					Ctime:         lib.Int64ToInt32Ptr(time.Now().Unix()),
				})
				////商户手续费处理
				//demoney64 := decimal.NewFromFloat(m64)
				//dmchfee64 := decimal.NewFromFloat(*mch.Fee)
				//d1000 := decimal.NewFromInt(1000)
				////订单金额*商户费率/1000=商户手续费
				//mchfeed := demoney64.Mul(dmchfee64).Div(d1000)
				//mchfee, _ := mchfeed.RoundCeil(2).Float64()
				//暂定矿工费为商户下发手续费
				//商户手续费扣除
				//newfeemoney, _ := decimal.NewFromFloat(*mch.EthFeemoney).Sub(decimal.NewFromFloat(transactionFeeInEthFloat)).Float64()
				//dal.Merchant.Where(dal.Merchant.ID.Eq(mch.ID)).Update(dal.Merchant.EthFeemoney, newfeemoney)
				//商户手续费变动记录
				dal.Tran.Create(&entity.Tran{
					UserID:        lib.Int64ToInt32Ptr(mch.ID),
					UserType:      lib.Int64ToInt32Ptr(int64(14)),
					Type:          lib.Int64ToInt32Ptr(int64(14)),
					Money:         &ercfee,
					Orderid:       order.Orderid,
					Rorderid:      order.Rorderid,
					Transactionid: &txid,
					Username:      mch.Username,
					Ctime:         lib.Int64ToInt32Ptr(time.Now().Unix()),
				})
			} else if *order.Chain == "Bep20" {
				if *mch.BscStatus != 0 {
					AddTaskToQueue(*order.Orderid)
					continue
				}
				bscclient, cerr := ethclient.Dial(config.NewConfig().Bsc.Api)
				if cerr != nil {
					AddTaskToQueue(*order.Orderid)
					continue
				}
				usdt, uerr := transfer.BNBGetUSDT(bscclient, erccfg)
				if uerr != nil {
					AddTaskToQueue(*order.Orderid)
					continue
				}
				bscclient.Close()
				if cast.ToFloat64(usdt) < m64 {
					dal.Merchant.Where(dal.Merchant.ID.Eq(mch.ID)).Update(dal.Merchant.BscStatus, 1)
					AddTaskToQueue(*order.Orderid)
					continue
				}
				//以太坊
				txid, terr := transfer.Bep20Send(kk, *order.Addr, lib.StringToFloat64(*order.Money))
				fee := 0.002
				if terr != nil {
					fmt.Println(terr)
					AddTaskToQueue(*order.Orderid)
					continue
				}
				//更新订单状态
				dal.Payorder.Where(dal.Payorder.ID.Eq(order.ID)).Updates(&entity.Payorder{
					Transactionid: &txid,
					TranFee:       &fee,
					Status:        lib.Int64ToInt32Ptr(2),
					Etime:         lib.Int64ToInt32Ptr(time.Now().Unix()),
				})
				//计费处理
				//商户余额扣除
				yingsub, _ := decimal.NewFromFloat(m64).Add(decimal.NewFromFloat(bscfee)).Float64()
				madd, _ := decimal.NewFromFloat(*mch.BscLockmoney).Sub(decimal.NewFromFloat(yingsub)).Float64()
				dal.Merchant.Where(dal.Merchant.ID.Eq(int64(mch.ID))).Update(dal.Merchant.BscLockmoney, madd)
				//商户余额变动记录
				dal.Tran.Create(&entity.Tran{
					UserID:        lib.Int64ToInt32Ptr(mch.ID),
					UserType:      lib.Int64ToInt32Ptr(int64(1)),
					Type:          lib.Int64ToInt32Ptr(int64(17)),
					Money:         &m64,
					Orderid:       order.Orderid,
					Rorderid:      order.Rorderid,
					Transactionid: &txid,
					Username:      mch.Username,
					Ctime:         lib.Int64ToInt32Ptr(time.Now().Unix()),
				})
				////商户手续费处理
				//demoney64 := decimal.NewFromFloat(m64)
				//dmchfee64 := decimal.NewFromFloat(*mch.Fee)
				//d1000 := decimal.NewFromInt(1000)
				////订单金额*商户费率/1000=商户手续费
				//mchfeed := demoney64.Mul(dmchfee64).Div(d1000)
				//mchfee, _ := mchfeed.RoundCeil(2).Float64()
				//商户手续费扣除
				//newfeemoney, _ := decimal.NewFromFloat(*mch.BscFeemoney).Sub(decimal.NewFromFloat(fee)).Float64()
				//dal.Merchant.Where(dal.Merchant.ID.Eq(mch.ID)).Update(dal.Merchant.BscFeemoney, newfeemoney)
				//商户手续费变动记录
				dal.Tran.Create(&entity.Tran{
					UserID:        lib.Int64ToInt32Ptr(mch.ID),
					UserType:      lib.Int64ToInt32Ptr(int64(1)),
					Type:          lib.Int64ToInt32Ptr(int64(19)),
					Money:         &bscfee,
					Orderid:       order.Orderid,
					Rorderid:      order.Rorderid,
					Transactionid: &txid,
					Username:      mch.Username,
					Ctime:         lib.Int64ToInt32Ptr(time.Now().Unix()),
				})
			}
		}
	}()
	return "success"
}

func DoLease(address string, duration string, value string, key string) (*Lease, error) {
	maxRetries := 5
	for i := 0; i < maxRetries; i++ {
		// 创建一个multipart/form-data的buffer
		body := &bytes.Buffer{}
		writer := multipart.NewWriter(body)

		// 添加form-data参数
		writer.WriteField("address", address)
		writer.WriteField("duration", duration)
		writer.WriteField("value", value)

		// 关闭writer
		err := writer.Close()
		if err != nil {
			return nil, err
		}

		// 创建请求
		tourl := config.NewConfig().LeaseApi.Url + "/api/v1/order"
		req, err := http.NewRequest("POST", tourl, body)
		if err != nil {
			return nil, err
		}

		// 添加header参数
		req.Header.Add("x-api-key", key)
		req.Header.Add("Content-Type", writer.FormDataContentType())

		// 发送请求
		client := &http.Client{}
		resp, err := client.Do(req)
		if err != nil {
			return nil, err
		}
		defer resp.Body.Close()

		// 读取响应
		respBody, err := ioutil.ReadAll(resp.Body)
		if err != nil {
			return nil, err
		}

		// 如果状态码是200，解析响应到结构体并返回
		if resp.StatusCode == http.StatusOK {
			var response Lease
			err = json.Unmarshal(respBody, &response)
			if err != nil {
				return nil, err
			}

			return &response, nil
		}

		// 如果状态码不是200，等待2秒后继续下一次循环，重新发送请求
		time.Sleep(2 * time.Second)
	}

	return nil, errors.New("请求接口5次失败")
}

// DoLeaseTG 从tg 机器人获取能量 发送 trx  给返回对应的能量
func DoLeaseTG(address string, key string) (bool, error) {
	trxBalance, err := QueryTRXBalance(address)
	if err != nil {
		return false, err
	}
	if trxBalance < 5 {
		_, err = transfer.TransferTron(key, address, config.NewConfig().TgTrxAddress.Address, config.NewConfig().TgTrxAddress.Num)
	}
	if err != nil {
		return false, err
	}
	_, err = transfer.TransferTron(key, address, config.NewConfig().TgTrxAddress.Address, config.NewConfig().TgTrxAddress.Num)
	if err != nil {
		return false, err
	}
	return true, nil
}

// CheckEnergyReceived 查询能量到账情况
func CheckEnergyReceived(address string) (int64, error) {
	maxAttempts := 5
	for attempt := 1; attempt <= maxAttempts; attempt++ {
		resp, err := http.Get(config.NewConfig().Tron.NewApi + "/api/accountv2?address=" + address)
		if err != nil {
			log.Printf("获取账户能量信息失败: %v", err)
			return 0, err
		}
		defer resp.Body.Close()
		body, err := io.ReadAll(resp.Body)
		if err != nil {
			log.Printf("读取响应数据失败: %v", err)
			return 0, err
		}

		// 定义结构体用于解析JSON
		type Bandwidth struct {
			EnergyRemaining int64 `json:"energyRemaining"`
		}

		type AccountInfo struct {
			Bandwidth Bandwidth `json:"bandwidth"`
		}

		var accountInfo AccountInfo
		if err := json.Unmarshal(body, &accountInfo); err != nil {
			log.Printf("解析JSON数据失败: %v", err)
			return 0, err
		}

		energy := accountInfo.Bandwidth.EnergyRemaining
		log.Printf("第%d次查询: 地址 %s 的剩余能量为: %d", attempt, address, energy)

		// 如果能量大于100000，返回nil表示成功
		if energy >= 100000 {
			return energy, nil
		}

		// 如果不是最后一次尝试，等待3秒后继续
		if attempt < maxAttempts {
			time.Sleep(3 * time.Second)
		}
	}

	// 如果执行到这里，说明5次查询能量都小于100000
	err := fmt.Errorf("地址 %s 的能量不足: 5次查询后能量仍小于100000", address)
	log.Printf("%v", err)
	return 0, err
}

// QueryTRXBalance 查询 trx 余额
func QueryTRXBalance(address string) (int64, error) {
	resp, err := http.Get(config.NewConfig().Tron.NewApi + "/api/accountv2?address=" + address)
	if err != nil {
		log.Printf("获取账户能量信息失败: %v", err)
		return 0, err
	}
	defer resp.Body.Close()
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		log.Printf("读取响应数据失败: %v", err)
		return 0, err
	}

	// 定义结构体用于解析JSON
	type TokenInfo struct {
		Amount    json.Number `json:"amount"`
		TokenName string      `json:"tokenName"`
	}

	type AccountInfo struct {
		WithPriceTokens []TokenInfo `json:"withPriceTokens"`
	}

	var accountInfo AccountInfo
	if err := json.Unmarshal(body, &accountInfo); err != nil {
		log.Printf("解析JSON数据失败: %v", err)
		return 0, err
	}

	// 找到TRX的token信息
	var amount int64
	for _, token := range accountInfo.WithPriceTokens {
		if token.TokenName == "trx" {
			// 将json.Number转换为float64，然后转为int64
			amountFloat, err := token.Amount.Float64()
			if err != nil {
				log.Printf("解析TRX余额失败: %v", err)
				return 0, err
			}
			// TRX余额通常以整数形式表示，所以我们将其转换为int64
			amount = int64(amountFloat)
			break
		}
	}

	return amount, nil
}
