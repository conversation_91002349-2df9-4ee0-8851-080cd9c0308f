package main

import (
	"bytes"
	"crypto/sha256"
	"encoding/hex"
	"github.com/mr-tron/base58"
	"log"
)

func main() {
	hexaddr, _ := DecodeBase58Address("TBvgiaPt7SXuQgNAv5xJJGL6Wz933bcLjT")
	log.Printf(hexaddr)

	address, _ := EncodeHexAddress(hexaddr)
	log.Printf(address)
}

func EncodeBytesAddress(input []byte) string {
	hash0 := sha256.Sum256(input)
	hash1 := sha256.Sum256(hash0[:])
	checksum := hash1[:4]
	output := bytes.Join([][]byte{input, checksum}, []byte{})

	b58Addr := base58.Encode(output)

	return b58Addr
}

func EncodeHexAddress(hexAddr string) (string, error) {
	input, err := hex.DecodeString(hexAddr)
	if err != nil {
		return "", err
	}
	return EncodeBytesAddress(input), nil
}

func DecodeBase58Address(b58Addr string) (string, error) {
	b58, err := base58.Decode(b58Addr)
	if err != nil {
		return "", err
	}
	hexAddr := hex.EncodeToString(b58[:21])
	return hexAddr, nil
}
