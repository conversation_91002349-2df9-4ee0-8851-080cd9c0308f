// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package entity

const TableNameCfg = "cfg"

// Cfg mapped from table <cfg>
type Cfg struct {
	Func  string `gorm:"column:Func;type:varchar(255);not null;comment:配置名称" json:"Func"`
	Value string `gorm:"column:Value;type:varchar(255);not null;comment:值" json:"Value"`
}

// TableName Cfg's table name
func (*Cfg) TableName() string {
	return TableNameCfg
}
