// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package entity

const TableNameRole = "roles"

// Role mapped from table <roles>
type Role struct {
	ID          int32   `gorm:"column:id;type:int unsigned;primaryKey;autoIncrement:true" json:"id"`
	Name        *string `gorm:"column:name;type:varchar(255);comment:名称" json:"name"`
	Description *string `gorm:"column:description;type:varchar(255);comment:描述" json:"description"`
	Ctime       *int32  `gorm:"column:ctime;type:int" json:"ctime"`
	DelFlg      *int32  `gorm:"column:del_flg;type:int" json:"del_flg"`
}

// TableName Role's table name
func (*Role) TableName() string {
	return TableNameRole
}
