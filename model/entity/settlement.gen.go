// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package entity

import (
	"time"

	"gorm.io/gorm"
)

const TableNameSettlement = "settlement"

// Settlement mapped from table <settlement>
type Settlement struct {
	ID        int64          `gorm:"column:id;type:bigint unsigned;primaryKey;autoIncrement:true" json:"id"`
	CreatedAt *time.Time     `gorm:"column:created_at;type:datetime(3)" json:"created_at"`
	UpdatedAt *time.Time     `gorm:"column:updated_at;type:datetime(3)" json:"updated_at"`
	DeletedAt gorm.DeletedAt `gorm:"column:deleted_at;type:datetime(3);index:idx_settlement_deleted_at,priority:1" json:"deleted_at"`
	Type      *int32         `gorm:"column:type;type:smallint;comment:1为商户2为代理" json:"type"`
	Money     *float32       `gorm:"column:money;type:float;comment:结算金额" json:"money"`
	Needmoney *string        `gorm:"column:needmoney;type:varchar(255);comment:结算后地址金额" json:"needmoney"`
	UserID    *int32         `gorm:"column:user_id;type:smallint;comment:商户或代理id" json:"user_id"`
	Status    *int32         `gorm:"column:status;type:smallint;comment:0待处理" json:"status"`
	Username  *string        `gorm:"column:username;type:varchar(255);comment:商户或代理用户名" json:"username"`
	Ctime     *int32         `gorm:"column:ctime;type:int" json:"ctime"`
	Address   *string        `gorm:"column:address;type:varchar(255);comment:收款地址" json:"address"`
	Chain     *string        `gorm:"column:chain;type:varchar(255);comment:链" json:"chain"`
}

// TableName Settlement's table name
func (*Settlement) TableName() string {
	return TableNameSettlement
}
