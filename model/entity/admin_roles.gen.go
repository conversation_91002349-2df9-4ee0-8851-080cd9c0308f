// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package entity

const TableNameAdminRole = "admin_roles"

// AdminRole mapped from table <admin_roles>
type AdminRole struct {
	ID      int32  `gorm:"column:id;type:int unsigned;primaryKey;autoIncrement:true" json:"id"`
	Adminid *int32 `gorm:"column:adminid;type:int;comment:admin表的id" json:"adminid"`
	RoleID  *int32 `gorm:"column:role_id;type:int;comment:roles表的id" json:"role_id"`
	Ctime   *int32 `gorm:"column:ctime;type:int" json:"ctime"`
	DelFlg  *int32 `gorm:"column:del_flg;type:int" json:"del_flg"`
}

// TableName AdminRole's table name
func (*AdminRole) TableName() string {
	return TableNameAdminRole
}
