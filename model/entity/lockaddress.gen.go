// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package entity

const TableNameLockaddress = "lockaddress"

// Lockaddress mapped from table <lockaddress>
type Lockaddress struct {
	ID     int32   `gorm:"column:id;type:int unsigned;primaryKey;autoIncrement:true" json:"id"`
	Addrid *int32  `gorm:"column:addrid;type:int;comment:地址id" json:"addrid"`
	Money  *string `gorm:"column:money;type:varchar(255);comment:金额" json:"money"`
	Ctime  *int32  `gorm:"column:ctime;type:int;comment:锁定时间" json:"ctime"`
	Etime  *int32  `gorm:"column:etime;type:int;comment:解锁时间" json:"etime"`
	Islock *int32  `gorm:"column:islock;type:int;comment:锁定状态 0锁定 1解锁" json:"islock"`
}

// TableName Lockaddress's table name
func (*Lockaddress) TableName() string {
	return TableNameLockaddress
}
