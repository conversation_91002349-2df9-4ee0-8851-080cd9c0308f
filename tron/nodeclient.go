package tron

import (
	"Mapi/config"
	"bytes"
	"encoding/json"
	"errors"
	_ "fmt"
	"github.com/google/go-querystring/query"
	"io/ioutil"
	"net/http"
	um "net/url"
)

type NodeClient struct {
	url string
}

type ApiError struct {
	Error string `json:"Error"`
}

func NewNodeClient(url string) *NodeClient {
	return &NodeClient{
		url: url,
	}
}

func (nc *NodeClient) Get(result interface{}, api string, params interface{}) error {
	qs, err := query.Values(params)
	if err != nil {
		return err
	}
	url, err := um.Parse(nc.url + api)
	if err != nil {
		return err
	}
	url.RawQuery = qs.Encode()
	//fmt.Printf("url => %+v\n",url.String())
	rsp, err := http.Get(url.String())
	if err != nil {
		return err
	}
	err = nc.parseBody(result, rsp)
	return err
}

//	func (nc *NodeClient) Post(result interface{}, api string, params interface{}) error {
//		url := nc.url + api
//		payload, err := json.Marshal(params)
//		if err != nil {
//			return err
//		}
//		//fmt.Printf("req payload => %+v\n", string(payload))
//		rsp, err := http.Post(url, "application/json", bytes.NewReader(payload))
//		if err != nil {
//			return err
//		}
//		err = nc.parseBody(result, rsp)
//		return err
//	}
func (nc *NodeClient) Post(result interface{}, api string, params interface{}) error {
	url := nc.url + api
	payload, err := json.Marshal(params)
	if err != nil {
		return err
	}
	apiKey := config.NewConfig().Tron.AppKey
	req, err := http.NewRequest("POST", url, bytes.NewReader(payload))
	if err != nil {
		return err
	}
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("TRON-PRO-API-KEY", apiKey)

	client := &http.Client{}
	rsp, err := client.Do(req)
	if err != nil {
		return err
	}

	err = nc.parseBody(result, rsp)
	return err
}

func (nc *NodeClient) parseBody(result interface{}, rsp *http.Response) error {
	body, err := ioutil.ReadAll(rsp.Body)
	if err != nil {
		return err
	}
	if rsp.StatusCode != 200 {
		return errors.New("[ Error ] Unexpected http status code")
	}
	//fmt.Printf("rsp => %v\n", string(body))
	return json.Unmarshal(body, &result)
}
