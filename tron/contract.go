package tron

import (
	"Mapi/api"
	"bytes"
	"encoding/hex"
	"errors"
	_ "fmt"

	"github.com/ethereum/go-ethereum/accounts/abi"
)

type Contract struct {
	contractAbi     abi.ABI
	tronApi         *TronApi
	credential      *Credential
	contractAddress string
}

func NewContract(tronApi *TronApi, credential *Credential, abiBytes []byte) (*Contract, error) {
	contractAbi, err := abi.JSON(bytes.NewReader(abiBytes))
	if err != nil {
		return nil, err
	}
	return &Contract{
		tronApi:     tronApi,
		credential:  credential,
		contractAbi: contractAbi,
	}, nil
}

func (c *Contract) At(address string) *Contract {
	c.contractAddress = address
	return c
}

func (c *Contract) Transact(name string, args ...interface{}) (*TransactionResult, error) {
	method, ok := c.contractAbi.Methods[name]
	if !ok {
		return nil, errors.New("method name missing")
	}

	data, err := c.contractAbi.Pack(name, args...)
	if err != nil {
		return nil, err
	}

	rsp, err := c.tronApi.TriggerSmartContract(
		c.contractAddress,
		method.Sig,
		hex.EncodeToString(data[4:]),
		c.credential.AddressBase58(),
	)
	if err != nil {
		return nil, err
	}

	sig, err := c.credential.SignHex(rsp.Transaction.TxId)
	if err != nil {
		return nil, err
	}

	rsp.Transaction.Signature = []string{sig}
	succeed, err := c.tronApi.BroadcastTransaction(rsp.Transaction)
	return &TransactionResult{rsp.Transaction.TxId, succeed}, err
}

func (c *Contract) Call(result interface{}, name string, args ...interface{}) error {
	method, ok := c.contractAbi.Methods[name]
	if !ok {
		return errors.New("method name missing")
	}

	data, err := c.contractAbi.Pack(name, args...)
	if err != nil {
		return err
	}

	rsp, err := c.tronApi.TriggerConstantContract(
		c.contractAddress,
		method.Sig,
		hex.EncodeToString(data[4:]),
		c.credential.AddressBase58(),
	)
	if err != nil {
		return err
	}
	//unpack
	bz, err := hex.DecodeString(rsp.ConstantResult[0])
	if err != nil {
		return err
	}
	return c.contractAbi.Unpack(result, name, bz)
}

func (c *Contract) GetEvents(since int64) ([]api.ContractEvent, error) {
	return c.tronApi.GetContractEvents(c.contractAddress, since)
}
