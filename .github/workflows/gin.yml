name: Run Tests

on:
  push:
    branches:
      - master
  pull_request:
    branches:
      - master

permissions:
  contents: read

jobs:
  lint:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
      - name: Set up Go
        uses: actions/setup-go@v5
        with:
          go-version: "^1"
      - name: Setup golangci-lint
        uses: golangci/golangci-lint-action@v6
        with:
          version: v1.61.0
          args: --verbose
  test:
    needs: lint
    strategy:
      matrix:
        os: [ubuntu-latest, macos-latest]
        go: ["1.23", "1.24"]
        test-tags:
          [
            "",
            "-tags nomsgpack",
            '--ldflags="-checklinkname=0" -tags sonic',
            "-tags go_json",
            "-race",
          ]
        include:
          - os: ubuntu-latest
            go-build: ~/.cache/go-build
          - os: macos-latest
            go-build: ~/Library/Caches/go-build
    name: ${{ matrix.os }} @ Go ${{ matrix.go }} ${{ matrix.test-tags }}
    runs-on: ${{ matrix.os }}
    env:
      GO111MODULE: on
      TESTTAGS: ${{ matrix.test-tags }}
      GOPROXY: https://proxy.golang.org
    steps:
      - name: Set up Go ${{ matrix.go }}
        uses: actions/setup-go@v5
        with:
          go-version: ${{ matrix.go }}
          cache: false

      - name: Checkout Code
        uses: actions/checkout@v4
        with:
          ref: ${{ github.ref }}

      - uses: actions/cache@v4
        with:
          path: |
            ${{ matrix.go-build }}
            ~/go/pkg/mod
          key: ${{ runner.os }}-go-${{ hashFiles('**/go.sum') }}
          restore-keys: |
            ${{ runner.os }}-go-

      - name: Run Tests
        run: make test

      - name: Upload coverage to Codecov
        uses: codecov/codecov-action@v4
        with:
          flags: ${{ matrix.os }},go-${{ matrix.go }},${{ matrix.test-tags }}
