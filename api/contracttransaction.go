package api

type ContractTransaction struct{
  Visible bool `json:"visible"`
  TxId string `json:"txID"`
  RawData RawData02 `json:"raw_data"`
  RawDataHex string `json:"raw_data_hex"`
  Signature []string `json:"signature"`
}


type RawData02 struct{
  Contract []Contract02 `json:"contract"`
  RefBlockBytes string `json:"ref_block_bytes"`
  RefBlockHash string `json:"ref_block_hash"`
  Expiration int64 `json:"expiration"`
  TimeStamp int64 `json:"timestamp"`
  FeeLimit int64 `json:"fee_limit"`
}

type Contract02 struct{
  Parameter Parameter02 `json:"parameter"`
  Type string `json:"type"`
}

type Parameter02 struct{
  Value Value02 `json:"value"`
  TypeUrl string `json:"type_url"`
}

type Value02 struct{
  Data string `json:"data"`
  OwnerAddress string `json:"owner_address"`
  ContractAddress string `json:"contract_address"`
}
